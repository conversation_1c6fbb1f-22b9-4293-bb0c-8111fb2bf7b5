/* 有效期切换动画 */
.f-zuheka-validity-content {
  overflow: hidden;
}

.f-zuheka-validity-fade-enter-active,
.f-zuheka-validity-fade-leave-active {
  transition: all 0.2s ease;
}

.f-zuheka-validity-fade-enter {
  transform: translateY(-10px);
  opacity: 0;
}

.f-zuheka-validity-fade-leave-to {
  transform: translateY(10px);
  opacity: 0;
}

.f-zuheka-validity-fade-enter-to,
.f-zuheka-validity-fade-leave {
  transform: translateY(0);
  opacity: 1;
}

/* 价格标签hover效果 */
.o-service-card:has(.o-price-select-tag:hover) .f-price-select-hover,
.o-service-card.price-tag-hovered .f-price-select-hover {
  border-color: #3363ff;
  background-color: rgba(51, 99, 255, 0.05);
}

/* 单价更新动画效果 */
.f-price-select-hover {
  transition: all 0.3s ease;
}

.f-price-select-hover.price-update-animation {
  animation: priceUpdatePulse 0.6s ease-out;
}

@keyframes priceUpdatePulse {
  0% {
    transform: scale(1);
    border-color: #3363ff;
    background-color: rgba(51, 99, 255, 0.1);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 3px rgba(51, 99, 255, 0.3);
    border-color: #3363ff;
    background-color: rgba(51, 99, 255, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: none;
    border-color: #3363ff;
    background-color: rgba(51, 99, 255, 0.05);
  }
}
/* 服务卡片高亮效果 */
.o-service-card-highlight {
  animation: serviceCardHighlight 1s ease-out;
  box-shadow: 0 4px 12px rgba(51, 99, 255, 0.3) !important;
  border-color: #3363ff !important;
}

@keyframes serviceCardHighlight {
  0% {
    transform: scale(1);
    background-color: rgba(51, 99, 255, 0.05);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 6px 16px rgba(51, 99, 255, 0.4);
    background-color: rgba(51, 99, 255, 0.1);
  }
  100% {
    transform: scale(1);
    background-color: rgba(51, 99, 255, 0.05);
  }
}
/* 弹出数字效果 */
.number-popup {
  position: fixed;
  transform: translate(-50%, -14px);
  z-index: 9999;
  animation: numberPopup 1s ease-out forwards;
  pointer-events: none;
  font-weight: bold;
  font-size: 18px;
}

/* 蓝色弹出数字（单击+1模式） */
.number-popup-blue {
  color: #3363ff;
}

/* 红色弹出数字（长按+10模式） */
.number-popup-red {
  color: #7c3bff;
}

@keyframes numberPopup {
  0% {
    transform: translate(-50%, -14px) scale(0.8);
    opacity: 1;
  }
  20% {
    transform: translate(-50%, -20px) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -60px) scale(1);
    opacity: 0;
  }
}

/* 操作类型弹出效果 */
.action-popup {
  position: fixed;
  transform: translate(-50%, -20px);
  z-index: 9998;
  animation: actionPopup 1.2s ease-out forwards;
  pointer-events: none;
  font-weight: bold;
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 绿色操作弹出（增加操作） */
.action-popup-green {
  color: #22c55e;
}

/* 红色操作弹出（减少操作） */
.action-popup-red {
  color: #ef4444;
}

@keyframes actionPopup {
  0% {
    transform: translate(-50%, -20px) scale(0.9);
    opacity: 1;
  }
  15% {
    transform: translate(-50%, -25px) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50px) scale(1);
    opacity: 0;
  }
}
