<transition :name="transitionDirection" mode="out-in">
  <div>
    <link rel="stylesheet" href="component/zuheka/zuheka.css" />
    <link rel="stylesheet" href="component/zuheka/zuheka.css" />
    <link rel="stylesheet" href="component/priceScrollBox/priceScrollBox.css" />
    <div class="main-right box-border space-x-2 pr-4 pt-2">
      <!--选择订单内容-->
      <div
        class="o-1vh bg-white flex flex-1 flex-col overflow-hidden rounded-lg"
      >
        <div class="o-title-box-shadow shrink-0 p-4">
          <div class="el-input el-input--suffix">
            <input
              type="text"
              autocomplete="off"
              placeholder="请输入服务名称"
              name="search_keyword"
              class="el-input__inner"
              v-model.trim="search_keyword"
              ref="search_keyword"
              @keyup.enter.exact.stop="billingInquiryEnter"
            />
            <span class="el-input__suffix" @click="billingInquiryEnter">
              <span class="el-input__suffix-inner">
                <i class="el-input__icon el-icon-search"></i>
              </span>
            </span>
          </div>
        </div>
        <div
          class="o-scrollbar h-0 flex-1 overflow-y-auto p-4"
          ref="serverCardWrap"
        >
          <div
            class="o-little-server-card-grid-box gap-2"
            v-infinite-scroll="loadMoreProduct"
            infinite-scroll-disabled="isServerScroll"
            infinite-scroll-distance="10"
            infinite-scroll-immediate-check="isServerScroll"
          >
            <div
              class="bg-gray-50 border-gray-200 hover:shadow-md hover:translate-y--1 active:scale-98 transform cursor-pointer rounded-md border border-solid px-4 py-2 transition-all hover:border-primary"
              v-for="(value,index) in zhk_server_name"
              @click="bind_zhk_add_server(value, $event)"
              @mousedown="handleMouseDown(value, $event)"
              @mouseup="handleMouseUp($event)"
              @contextmenu.prevent="handleRightClick(value, $event)"
              :class="{ 'o-service-card-pulse': value._justAdded }"
            >
              <div class="flex h-12 items-center">
                <div class="line-clamp-2 text-lg font-bold leading-5">
                  {{value.service_name}}
                </div>
              </div>
              <div><span class="text-xs">￥</span>{{value.price}}</div>
            </div>
            <!-- 触底效果 -->
            <div v-if="busy" class="loadingtip transition">{{loadingtip}}</div>
          </div>
        </div>
      </div>
      <!--开单内容-->
      <div
        class="o-1vh bg-white flex shrink-0 flex-col overflow-hidden rounded-lg"
        style="width: 650px"
      >
        <member-search-bar
          :login-info="loginInfo"
          @handle-select-member="handleMemberSelect"
          @handle-clear="clearPage"
        />
        <div class="o-scrollbar h-0 flex-1 overflow-y-auto">
          <div class="p-4">
            <transition-group
              name="o-service-card-list"
              tag="div"
              class="space-y-3"
            >
              <div
                class="o-service-card bg-white border-gray-200 relative overflow-hidden rounded-md border border-solid transition-all hover:shadow-lg"
                :class="{ 'o-service-card-item-removing': item._removing }"
                v-for="(item,index) in zhk_server_details_name"
                :key="item.id + '_' + (item._animationId || index)"
              >
                <div
                  class="o-service-card-title-bg border-b-solid border-b-1 border-b-gray-200 px-6 pb-3 pt-6 transition-all"
                >
                  <div class="flex items-center">
                    <div class="pr-4 text-xl font-bold">
                      {{item.service_name}}
                    </div>
                    <div class="o-tag o-tag-indigo text-xs">服务</div>
                    <div class="flex flex-1 items-center justify-end pl-6 pr-4">
                      <div
                        class="el-icon-delete hover:text-red cursor-pointer transition"
                        @click="zhk_open_details_price_del(index)"
                      ></div>
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="mt-3 flex shrink-0 items-center space-x-6">
                      <div
                        class="o-price-select-tag"
                        @click="handleSetUnitPrice(index, item.zhk_price_show)"
                      >
                        <div>原价</div>
                        <div>￥{{item.zhk_price_show}}</div>
                      </div>
                      <div
                        class="o-price-select-tag"
                        @click="handleSetUnitPrice(index, item.member_price / 100)"
                        v-if="item.member_price != item.zhk_price && item.member_price>0"
                      >
                        <div>会员价</div>
                        <div>￥{{item.member_price | filterMoney}}</div>
                      </div>
                    </div>
                    <div class="flex-1">
                      <!-- 选择提成 -->
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-between px-5 py-4 text-sm"
                >
                  <div class="flex items-center">
                    <div class="text-gray-500 shrink-0 pr-2">单价</div>
                    <div
                      class="el-input el-input--small el-input--prefix"
                      style="width: 152px"
                    >
                      <input
                        type="text"
                        class="f-price-select-hover el-input__inner no-spinners"
                        style="
                          font-size: 18px;
                          font-weight: bolder;
                          text-align: right;
                        "
                        v-model.trim="item.unitPrice"
                        @input="handleUnitPriceChange(index)"
                        onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 46"
                        oninput="
                          // 只允许数字和小数点
                          this.value = this.value.replace(/[^0-9.]/g, '');
                          // 防止多个小数点
                          if(this.value.split('.').length > 2) {
                            this.value = this.value.substring(0, this.value.lastIndexOf('.'));
                          }
                          // 限制最大值
                          if(parseFloat(this.value) >= 10000000) this.value = '9999999.99';
                          // 限制小数位数
                          if(this.value.includes('.') && this.value.split('.')[1] && this.value.split('.')[1].length > 2) {
                            let parts = this.value.split('.');
                            this.value = parts[0] + '.' + parts[1].substring(0, 2);
                          }
                        "
                      />
                      <span class="el-input__prefix center">
                        <span class="el-input__prefix-inner pl-1">￥</span>
                      </span>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <div class="text-gray-500 shrink-0 pr-2">数量</div>
                    <div class="o-numberInput-box" style="width: 130px">
                      <i class="el-icon-minus" @click="jianshao(index)"></i>
                      <input
                        class="el-input__inner text-center"
                        v-model="item.numberAvailable"
                        size="small"
                        style="
                          width: 80px;
                          font-size: 18px;
                          font-weight: bolder;
                        "
                        step="1"
                        maxlength="4"
                        @input="handleNumInputChange($event, index)"
                      />
                      <i class="el-icon-plus" @click="zengjia(index)"></i>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <div class="text-gray-500 shrink-0 pr-2">小计</div>
                    <div
                      class="el-input el-input--small el-input--prefix"
                      style="width: 152px"
                    >
                      <input
                        type="text"
                        class="el-input__inner no-spinners"
                        style="
                          font-size: 18px;
                          font-weight: bolder;
                          text-align: right;
                        "
                        v-model.trim="item.subtotal"
                        @input="handleSubtotalChange(index)"
                        onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 46"
                        oninput="
                          // 只允许数字和小数点
                          this.value = this.value.replace(/[^0-9.]/g, '');
                          // 防止多个小数点
                          if(this.value.split('.').length > 2) {
                            this.value = this.value.substring(0, this.value.lastIndexOf('.'));
                          }
                          // 限制最大值
                          if(parseFloat(this.value) >= 10000000) this.value = '9999999.99';
                          // 限制小数位数
                          if(this.value.includes('.') && this.value.split('.')[1] && this.value.split('.')[1].length > 2) {
                            let parts = this.value.split('.');
                            this.value = parts[0] + '.' + parts[1].substring(0, 2);
                          }
                        "
                      />
                      <span class="el-input__prefix center">
                        <span class="el-input__prefix-inner pl-1">￥</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </transition-group>
          </div>
        </div>
        <div class="o-total-box relative shrink-0">
          <div class="z-1 relative">
            <div class="px-6 pt-4">
              <div class="flex h-10 items-center">
                <div class="text-gray-500 w-20 text-sm">有效期：</div>
                <div class="flex">
                  <el-radio v-model="cardinfo.permanent" label="1">
                    永久有效
                  </el-radio>
                  <el-radio v-model="cardinfo.permanent" label="2">
                    限制使用天数
                  </el-radio>
                  <el-radio v-model="cardinfo.permanent" label="3">
                    固定使用日期
                  </el-radio>
                </div>
              </div>
              <transition name="f-zuheka-validity-fade" mode="out-in">
                <div
                  v-if="cardinfo.permanent==2"
                  key="days-validity"
                  class="o-validity-content"
                >
                  <div class="flex h-10 items-center">
                    <div class="text-gray-500 w-20 text-sm">有效天数：</div>
                    <el-input
                      v-model="cardinfo.validity_time"
                      placeholder="请输入天数"
                      style="width: 180px"
                      size="small"
                    >
                      <template slot="append">天</template>
                    </el-input>
                  </div>
                  <div class="flex h-10 items-center">
                    <div class="text-gray-500 w-20 text-sm">生效方式：</div>
                    <div class="flex">
                      <el-radio v-model="cardinfo.effectiveMethod" label="1">
                        购买后立刻生效
                      </el-radio>
                      <el-radio v-model="cardinfo.effectiveMethod" label="2">
                        使用一次后生效
                      </el-radio>
                    </div>
                  </div>
                </div>
                <div
                  v-else-if="cardinfo.permanent==3"
                  key="date-validity"
                  class="o-validity-content"
                >
                  <div class="flex h-10 items-center">
                    <div class="text-gray-500 w-20 text-sm">到期日期：</div>
                    <el-date-picker
                      v-model="cardinfo.zuheka_validity"
                      type="date"
                      value-format="yyyy-MM-dd"
                      :picker-options="giftPickTimeOptions"
                      placeholder="选择日期"
                      size="small"
                    ></el-date-picker>
                  </div>
                </div>
              </transition>
            </div>
            <div class="flex h-10 items-center px-6 text-sm">
              <div class="text-gray-500 w-20 shrink-0">设置提成：</div>
              <div
                class="line-clamp-2 cursor-pointer text-primary"
                @click="zhkchange_xiaoshou"
              >
                {{SalesShow || selesShows}}
              </div>
            </div>
            <div class="flex h-10 items-center px-6">
              <div class="text-gray-500 w-20 shrink-0 text-sm">内部备注：</div>
              <el-input
                type="text"
                size="small"
                placeholder="请输入"
                v-model="beizhu_info"
              ></el-input>
            </div>
            <div class="o-font-shadow flex items-baseline px-6 py-5 font-bold">
              <span class="text-xl">合计：</span>
              <span class="pr-1 text-lg">￥</span>
              <price-scroll-box :price="pay_all_show"></price-scroll-box>
            </div>
          </div>
        </div>
        <f-base-button
          class="absolute bottom-8 right-10 z-10"
          style="width: 180px"
          title="去结算"
          type="primary"
          @click="goSettle('receipt')"
        />
      </div>
      <!--选择销售模态框-->
      <el-dialog
        title="选择协助销售"
        :visible.sync="helpStaffVisible"
        width="35%"
        top="7vh"
      >
        <div style="height: calc(100vh - 500px); overflow: auto">
          <el-checkbox-group v-model="checkHelpStaffArr">
            <template v-for="(helpStaff,index) in helpStaffAll">
              <div class="xuazne_xiaoshou" v-if="bindStaffId!=helpStaff.id">
                <el-checkbox
                  :label="helpStaff"
                  style="height: 25px; width: 25px"
                >
                  {{helpStaff.nickname}} ({{helpStaff.job_num}})
                </el-checkbox>
              </div>
            </template>
          </el-checkbox-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="closeHelpStaffVisible(0)">
            取消
          </el-button>
          <el-button type="primary" @click="closeHelpStaffVisible(1)">
            确定
          </el-button>
        </span>
      </el-dialog>

      <!--选择销售模态框-->
      <el-dialog
        title="选择销售"
        :visible.sync="zhk_xiao_shou"
        width="35%"
        top="7vh"
        :append-to-body="true"
      >
        <!--<div class="xuanze_jishi_search">-->
        <!--<el-input placeholder="输入销售名称" suffix-icon="el-icon-search"></el-input>-->
        <!--</div>-->
        <div style="height: calc(100vh - 500px); overflow: auto">
          <div class="xuazne_xiaoshou" v-for="(value , index) in zhkxiaoshous ">
            <el-checkbox
              v-model="value.is_choice_xiaoshou"
              style="height: 25px; width: 25px"
              @change="chioce_xiaoshou(index,value.is_choice_xiaoshou,value.id)"
            >
              {{value.nickname}}
            </el-checkbox>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="zhk_xiaoshou_over">
            取消
          </el-button>
          <el-button type="primary" @click="zhk_xiaoshou_save">确定</el-button>
        </span>
      </el-dialog>
      <!-- 点击充值收款后出现开单框 -->

      <template v-if="buy_receipt">
        <app-pay
          :buy-receipt="buy_receipt"
          :login-info="loginInfo"
          :use-card="isRechargeCard"
          :order-no="orderNo"
          :bill-to-pay="billToPay"
          :is-pay-status="isPayStatus"
          @close-pay="bindClosePay"
        ></app-pay>
      </template>
    </div>
  </div>
</transition>
