"use strict";
Vue.component("app-zuheka", function (resolve, reject) {
  $.get("component/zuheka/zuheka.html").then(function (res) {
    resolve({
      template: res,
      props: {
        login: {
          type: Object,
        },
        handle_foucs_input: {
          type: Boolean,
          value: false,
        },
        "transition-direction": {
          type: String,
          value: "slide-down",
        },
      },
      data: function () {
        let goodsSearchConfig = localStorage.getItem("goodsSearchConfig");
        let serverLimit = 10;
        let productLimit = 10;
        let cardLimit = 10;
        if (goodsSearchConfig) {
          goodsSearchConfig = JSON.parse(goodsSearchConfig);
          serverLimit = goodsSearchConfig["serverLimit"]
            ? goodsSearchConfig["serverLimit"]
            : 10;
          productLimit = goodsSearchConfig["productLimit"]
            ? goodsSearchConfig["productLimit"]
            : 10;
          cardLimit = goodsSearchConfig["cardLimit"]
            ? goodsSearchConfig["cardLimit"]
            : 10;
        }
        return {
          istbz: this.$root.istbz,
          //订单号
          orderNo: "",
          //未知数据
          isRechargeCard: false,
          //充卡下单完智慧跳转支付页面
          //收银台--充卡收款跳转到收银台。 0:会员余额,1:支付宝/微信,2:现金,4:自定义收款
          isPayStatus: 4,
          //收银台到支付页面的标志位
          billToPay: 0,
          buy_receipt: false,
          //下单时候需要的充卡信息
          extraData: {},
          //loading页面转圈圈
          loading: false,
          url: baseUrl,
          vipHeadPortrait: "../images/zuhekatouxiang.png",
          newMember: {
            vip_is_sex: 0,
          },
          //充卡开单详情
          zhk_server_details_name: [],
          //循环的服务列表
          zhk_server_name: [],
          //搜索的关键字
          search_keyword: "", // 发送查询的细心
          //循环的服务标签列表
          cashier_open_order_service_label: "",
          //判断当前标签的
          isActiveServer: 0,
          server_number: 1,
          //备注信息
          beizhu_info: "",
          //充卡的有效期
          zuheka_validity: "",
          checked: false,
          //展示输入的总金额
          input_price_show: "",
          //优惠方式 deduction、discount
          discountMethods: "deduction",
          //优惠(扣减)金额
          deductionPrice: "",
          //输入的金额
          input_price: "",
          //当前的选择折扣
          input_dis: "",
          //最后需要提交支付的值
          pay_all: 0,
          pay_all_show: "0.00",
          loginInfo: {}, // 登录信息
          serverLabelid: "",
          serverPage: 1, //服务查询的页数
          serverLimit: serverLimit, //服务查询的个数
          isServerScroll: false,
          // 充卡产品触底
          isComboScroll: false,
          serverAllCount: 0,
          busy: false,
          loadingtip: "加载中···",
          //会员信息展示
          // zhk_memberObj: {
          //     phone: '',
          //     name: '',
          //     is_sex: -1,
          // },
          zhk_memberObj: {},
          memberInfo: {},
          //会员姓名
          MemberName: "",
          zhk_sex: 0, //充卡的性别
          //判断是不是展示销售
          zhk_xiao_shou: false,
          //存贮销售内容
          zhkxiaoshous: [],
          //销售复选框
          zhk_check1: false,
          //销售用来存储以选择的销售信息。
          xiao_shou_zhanshi: [],
          //在页面展示选中的销售内容
          zhk_xiao_shou_zhanshi: [],
          //页面展示销售的变量
          SalesShow: "",
          selesShows: "请选择",
          //判断是否要展示新的会员页面
          is_sechargeMember: true,
          is_showgeMember: false,
          //会员的相关信息
          cz_huiyuanxinxi: {},
          giftPickTimeOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7; //如果没有后面的-8.64e7就是不可以选择今天的
            },
          },
          // 选择协助员工
          helpStaffArr: {},
          helpStaffAll: [],
          helpStaffVisible: false,
          checkHelpStaffArr: [],
          bindStaffId: 0,
          isactive1: 4,
          // 有效期等
          cardinfo: {
            permanent: "2",
            validity_time: "",
            effectiveMethod: "1",
            dueDate: "",
          },
          // 客户搜索框
          vipSearch: "",
          isShowMemberSearch: false,
          // 长按相关变量
          longPressTimer: null,
          longPressInterval: null,
          longPressFirstTimer: null, // 第一次+10的定时器
          isLongPressing: false,
          wasLongPressed: false, // 标记是否曾经进入过长按模式
          longPressData: null,
          longPressEvent: null,
          // 右键长按相关变量
          rightLongPressTimer: null,
          rightLongPressInterval: null,
          rightLongPressFirstTimer: null,
          isRightLongPressing: false,
          wasRightLongPressed: false,
          rightLongPressData: null,
          rightLongPressEvent: null,
          rightPressStartTime: 0,
          isRightPressed: false,
        };
      },
      mounted: function () {
        this.serviceList();
        this.setupPriceTagHover();
        this.setupGlobalMouseEvents();
      },

      methods: {
        setupPriceTagHover: function () {
          var _self = this;
          // 使用事件委托来处理动态添加的元素
          this.$nextTick(function () {
            $(document).on("mouseenter", ".o-price-select-tag", function () {
              $(this).closest(".o-service-card").addClass("price-tag-hovered");
            });
            $(document).on("mouseleave", ".o-price-select-tag", function () {
              $(this)
                .closest(".o-service-card")
                .removeClass("price-tag-hovered");
            });
          });
        },
        setupGlobalMouseEvents: function () {
          const _self = this;
          // 全局鼠标松开事件，确保长按能正确结束
          _self.globalMouseUpHandler = function (event) {
            console.log("全局鼠标松开事件，按钮:", event.button);
            // 只有在长按状态下才处理全局事件
            if (event.button === 0 && _self.isLongPressing) {
              console.log("全局结束左键长按");
              _self.endLongPress();
            } else if (event.button === 2 && _self.isRightLongPressing) {
              console.log("全局结束右键长按");
              _self.endRightLongPress();
            }
          };
          document.addEventListener("mouseup", _self.globalMouseUpHandler);
        },
        handleMemberSelect: function (memberInfo) {
          console.log("handleMemberSelect", memberInfo);
          this.memberInformation = memberInfo;
          this.cz_huiyuanxinxi = memberInfo;
          this.is_sechargeMember = false;
          this.is_showgeMember = true;
          this.memberInfo = memberInfo;
          this.MemberName = this.memberInfo["member_name"];
          this.zhk_sex = this.memberInfo["sex"];
          this.deductionPrice = "";
          this.input_dis = "";
        },
        //调整单价
        handleUnitPriceChange: function (index) {
          const _self = this;
          const item = _self.zhk_server_details_name[index];
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const quantity = parseInt(item.numberAvailable) || 1;

          // 计算小计 = 单价 * 数量
          const subtotal = (unitPrice * quantity).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * quantity * 100);

          // 重新计算总价
          _self.CalculatePrice();
        },

        //设置单价
        handleSetUnitPrice: function (index, price) {
          const _self = this;
          const item = _self.zhk_server_details_name[index];
          const unitPrice = parseFloat(price) || 0;
          const quantity = parseInt(item.numberAvailable) || 1;

          // 设置单价
          item.unitPrice = unitPrice.toFixed(2);

          // 计算小计 = 单价 * 数量
          const subtotal = (unitPrice * quantity).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * quantity * 100);

          // 重新计算总价
          _self.CalculatePrice();

          // 触发单价输入框动画效果
          _self.$nextTick(() => {
            const serviceCard =
              document.querySelectorAll(".o-service-card")[index];
            if (serviceCard) {
              const priceInput = serviceCard.querySelector(
                ".f-price-select-hover"
              );
              if (priceInput) {
                // 添加动画类
                priceInput.classList.add("price-update-animation");

                // 动画结束后移除类
                setTimeout(() => {
                  priceInput.classList.remove("price-update-animation");
                }, 600);
              }
            }
          });
        },
        //会员搜索
        bindSearch: function () {
          if (!this.vipSearch) return;
          this.searchCurrentPage = 1;
          this.getMember();
        },
        //会员列表
        getMember: function () {
          var _self = this;
          _self.searchLoading = true;
          $.ajax({
            url: _self.url + "/android/Member/getMember",
            type: "POST",
            data: {
              adviser: "", // 新客归属（筛选用）
              birthday: 0, // 生日 0 全部 1 今天 2明天 3 本周 4 本月 5 下月
              count: -1, // 消费次数，-1 全部 -2 一次以内 -3 三次以内 -4 五次以内，自定义直接输入次数筛选用
              starttime: "", // 开始时间
              endtime: "", // 结束时间（生日筛选 "年月日"）
              keyword: _self.vipSearch, // 会员名称、备注名、会员编号、手机号搜索
              last_time: -1, // 消费期限，-1 全部、-2 一个月，-3 两个月，-4三个月，自定义直输入数字（筛选用，默认空）
              level_id: "", // 等级id
              maxscore: _self.memberPointsMin, // 积分范围，最大积分
              minscore: -1, // 积分范围，最小积分
              member_source: "", // 来源id
              limit: _self.searchLimit, // 分页每页条数
              page: _self.searchCurrentPage, // 分页第几页（必须）
              tab_id: "", // 标签id
              merchant_id: _self.loginInfo.merchantid, // 商户id
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.searchLoading = false;
                // _self.isFlag = false;
                _self.vip_info = res.data;
                _self.allCount = res.count;
              } else {
                _self.searchLoading = false;
                _self.$message.error({
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },
        // 选定客户赋值
        handleSelect(data) {
          this.memberInformation = data;
          this.cz_huiyuanxinxi = data;
          this.is_sechargeMember = false;
          this.is_showgeMember = true;
          this.memberInfo = data;
          this.MemberName = this.memberInfo["member_name"];
          this.zhk_sex = this.memberInfo["sex"];
          this.deductionPrice = "";
          this.input_dis = "";
        },
        // 选择协助接待员工
        chooseHelpStaff(id) {
          var _self = this;
          let myPromise = new Promise((resolve, reject) => {
            if (this.helpStaffAll.length == 0) {
              $.ajax({
                url: _self.url + "/android/Staff/sellsman",
                type: "post",
                data: {
                  merchantid: _self.loginInfo.merchantid,
                  storeid: _self.loginInfo.storeid,
                },
                success: function (res) {
                  // var res = JSON.parse(res);
                  if (res.code == 1) {
                    _self.helpStaffAll = res.data;
                    _self.helpStaffArr[_self.isactive1] = [];
                    resolve();
                  } else {
                    _self.$message({
                      type: "error",
                      message: res.msg,
                      duration: 1500,
                    });
                  }
                },
              });
            } else {
              resolve();
            }
          });
          myPromise.then(() => {
            if (typeof this.helpStaffArr[this.isactive1] !== "undefined") {
              this.checkHelpStaffArr = [...this.helpStaffArr[this.isactive1]];
            } else {
              this.checkHelpStaffArr = [];
            }
            this.bindStaffId = id;
            this.helpStaffVisible = true;
          });
        },
        closeHelpStaffVisible(type) {
          // 1, 确定关闭 2，取消关闭
          if (type) {
            this.helpStaffArr[this.isactive1] = [...this.checkHelpStaffArr];
          } else {
          }
          this.checkHelpStaffArr = [];
          this.helpStaffVisible = false;
        },
        //聚焦
        inputFocus: function (dom) {
          this.$nextTick(
            function () {
              dom.focus();
            }.bind(this)
          );
        },
        //clearPage 清空页面
        clearPage: function () {
          this.zhk_server_details_name = [];
          // this.is_showgeMember=false;
          this.cz_huiyuanxinxi = {};
          this.beizhu_info = "";
          this.zuheka_validity = "";
          this.zhkxiaoshous = [];
          this.zhk_xiao_shou_zhanshi = [];
          this.checked = false;
          this.zhk_memberObj = {};
          this.is_sechargeMember = true;
          this.is_showgeMember = false;
          this.MemberName = "";
          this.xiao_shou_zhanshi = [];
          this.SalesShow = "";
          this.pay_all_show = "0.00";
          this.pay_all = 0;
          this.input_price_show = "";
          this.deductionPrice = "";
          this.input_dis = "";
          this.helpStaffArr[this.isactive1] = [];
          this.cardinfo = {
            permanent: "2",
            validity_time: "",
            effectiveMethod: "1",
            dueDate: "",
          };

          // 重置服务卡片的动画状态
          this.zhk_server_name.forEach((item) => {
            this.$set(item, "_justAdded", false);
          });

          this.inputFocus(this.$refs.search_keyword);
        },
        //关闭收款
        bindClosePay: function (flag) {
          this.buy_receipt = flag;
          this.clearPage();
        },
        // zhk删除会员
        bindDelMemberInfo: function () {
          var _self = this;
          _self.cz_huiyuanxinxi = {};
          _self.zhk_memberObj = {};
          _self.is_sechargeMember = true;
          _self.is_showgeMember = false;
          _self.MemberName = "";
          _self.helpStaffArr[_self.isactive1] = [];
        },
        //选择销售弹框
        zhkchange_xiaoshou: function () {
          var _self = this;
          _self.zhk_xiao_shou = true;
          _self.loading = true;
          let salemenArr = _self.zhk_xiao_shou_zhanshi;
          _self.xiao_shou_zhanshi = salemenArr;
          $.ajax({
            url: _self.url + "/android/Staff/sellsman",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.zhkxiaoshous = res.data;
                if (salemenArr?.length != 0) {
                  for (let i = 0; i < _self.zhkxiaoshous.length; i++) {
                    let flag = true;
                    for (let j = 0; j < salemenArr.length; j++) {
                      if (salemenArr[j].id == _self.zhkxiaoshous[i].id) {
                        _self.zhkxiaoshous[i]["is_choice_xiaoshou"] = true;
                        flag = false;
                        break;
                      }
                    }
                    if (flag) {
                      _self.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
                    }
                  }
                } else {
                  for (let i = 0; i < _self.zhkxiaoshous.length; i++) {
                    _self.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
                  }
                }
                _self.loading = false;
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },
        //充卡确定销售
        zhk_xiaoshou_save: function () {
          var _self = this;
          //确定之前清空销售页面展示。
          _self.SalesShow = "";
          if (_self.xiao_shou_zhanshi) {
            _self.zhk_xiao_shou_zhanshi = [];
            _self.zhk_xiao_shou_zhanshi = JSON.parse(
              JSON.stringify(_self.xiao_shou_zhanshi)
            ); //深拷贝
            var arrlength = _self.zhk_xiao_shou_zhanshi.length;
            if (_self.zhk_xiao_shou_zhanshi) {
              for (let i = 0; i < arrlength; i++) {
                if (i == arrlength - 1) {
                  _self.SalesShow += _self.zhk_xiao_shou_zhanshi[i]["nickname"];
                } else {
                  _self.SalesShow +=
                    _self.zhk_xiao_shou_zhanshi[i]["nickname"] + "、";
                }
              }
            } else {
              _self.SalesShow = "";
            }
          } else {
            _self.zhk_xiao_shou_zhanshi = _self.xiao_shou_zhanshi;
            _self.SalesShow = "";
          }
          _self.xiao_shou_zhanshi = [];
          _self.zhk_xiao_shou = false;
        },
        //充卡取消销售
        zhk_xiaoshou_over: function () {
          var _self = this;
          //上来给销售页面展示清空
          _self.SalesShow = "";
          var arrlength = _self.zhk_xiao_shou_zhanshi.length;
          // _self.zhkxiaoshous=[];
          if (_self.zhk_xiao_shou_zhanshi) {
            for (let i = 0; i < arrlength; i++) {
              if (i == arrlength - 1) {
                _self.SalesShow += _self.zhk_xiao_shou_zhanshi[i]["nickname"];
              } else {
                _self.SalesShow +=
                  _self.zhk_xiao_shou_zhanshi[i]["nickname"] + "、";
              }
            }
          } else {
            _self.SalesShow = "";
          }
          for (let i = 0; i < _self.zhkxiaoshous.length; i++) {
            _self.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
          }
          _self.$forceUpdate();
          _self.zhk_xiao_shou = false;
        },
        //选择销售框
        chioce_xiaoshou: function (index, data, xiao_shou_id) {
          let _self = this;
          let item = _self.zhkxiaoshous[index];
          if (data == true) {
            item["is_choice_xiaoshou"] = data;
            _self.xiao_shou_zhanshi.push(item);
          } else {
            for (let j = 0; j < _self.xiao_shou_zhanshi.length; j++) {
              if (_self.xiao_shou_zhanshi[j]["id"] == xiao_shou_id) {
                _self.xiao_shou_zhanshi.splice(j, 1);
              }
            }
          }
          _self.$forceUpdate();
        },
        //价格输入框input匹配
        TotalAmountMatching: function () {
          this.input_price_show = this.input_price_show.replace(/[^\d\.]/g, "");
          this.input_price_show = this.input_price_show.replace(/^\./g, "");
          this.input_price_show = this.input_price_show.replace(/\.{2,}/g, ".");
          this.input_price_show = this.input_price_show
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          this.input_price_show = this.input_price_show.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          this.input_price_show = this.input_price_show.replace(/^0.$/, "0.");
          if (this.input_price_show > 100000.0) {
            this.input_price_show = "100000.00";
          } else if (this.input_price_show == "") {
            this.input_dis = "";
          }
          var _self = this;
          if (_self.input_dis != "") {
            _self.pay_all_show = (
              _self.input_price_show *
              ((100 - _self.input_dis) / 10000) *
              100
            ).toFixed(2);
          } else {
            _self.pay_all_show = ((_self.input_price_show / 100) * 100).toFixed(
              2
            );
          }
        },
        handleDiscountMethodsClick: function (value) {
          this.discountMethods = value;
          switch (value) {
            case "deduction":
              this.deductionMatch();
              break;
            case "discount":
              this.discountMatch();
              break;
          }
        },
        //优惠减扣匹配
        deductionMatch: function () {
          var _self = this;
          _self.deductionPrice = _self.deductionPrice.replace(/[^\d\.]/g, "");
          _self.deductionPrice = _self.deductionPrice.replace(/^\./g, "");
          _self.deductionPrice = _self.deductionPrice.replace(/\.{2,}/g, ".");
          _self.deductionPrice = _self.deductionPrice
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          _self.deductionPrice = _self.deductionPrice.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          _self.deductionPrice = _self.deductionPrice.replace(/^0.$/, "0.");
          _self.pay_all_show = _self.input_price_show - _self.deductionPrice;
          if (_self.pay_all_show < 0) {
            _self.pay_all_show = 0;
          }
          _self.pay_all_show = _self.pay_all_show.toFixed(2);
        },
        //折扣匹配
        discountMatch: function () {
          var _self = this;
          var reg = /^100$|^(\d|[1-9]\d)$/g;
          if (_self.input_dis != "") {
            if (_self.input_dis != 0) {
              if (_self.input_dis.match(reg)) {
              } else {
                _self.input_dis = 0;
              }
            } else {
              _self.input_dis = 0;
            }
          }
          if (_self.input_dis != "") {
            _self.pay_all_show = (
              _self.input_price_show *
              ((100 - _self.input_dis) / 10000) *
              100
            ).toFixed(2);
          } else {
            _self.pay_all_show = ((_self.input_price_show / 100) * 100).toFixed(
              2
            );
          }
        },
        //展示最终价格
        //删除服务
        zhk_open_details_price_del: function (index) {
          var _self = this;

          // 获取要删除的服务信息用于提示
          var deletedService = _self.zhk_server_details_name[index];

          // 添加删除动画类
          _self.$set(deletedService, "_removing", true);

          // 延迟删除以显示缩小动画
          setTimeout(() => {
            _self.input_price = 0;

            // 删除服务项
            _self.zhk_server_details_name.splice(index, 1);
            var arrlength = _self.zhk_server_details_name.length;
            if (arrlength == 0) {
              _self.input_price_show = "";
              _self.input_dis = "";
              _self.pay_all_show = "0.00";
            } else {
              for (let i = 0; i < _self.zhk_server_details_name.length; i++) {
                _self.input_price +=
                  _self.zhk_server_details_name[i]["totalAmount"];
              }
              _self.input_price_show = (_self.input_price / 100).toFixed(2);
            }
            if (_self.input_dis != "") {
              _self.pay_all_show = (
                _self.input_price *
                ((100 - _self.input_dis) / 10000)
              ).toFixed(2);
            } else {
              _self.pay_all_show = (_self.input_price / 100).toFixed(2);
            }
          }, 100);
        },

        //实体卡登录  0527502818
        loadEntityCard: function (card) {
          let _self = this;
          $.ajax({
            url: _self.url + "/android/Member/readEntityCard",
            type: "post",
            data: {
              card_voucher: card,
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              //定义的变量赋值，给
              if (res.code == 1 && res.data.length > 0) {
                _self.memberInformation = res.data[0];
                _self.cz_huiyuanxinxi = res.data[0];
                _self.is_sechargeMember = false;
                _self.is_showgeMember = true;
                _self.memberInfo = res.data[0];
                _self.MemberName = _self.memberInfo["member_name"];
                _self.zhk_sex = _self.memberInfo["sex"];
                // _self.loading = false;
              } else {
                _self.$message.error({
                  message: "未找到此会员信息",
                  duration: 1500,
                });
              }
            },
            error: function (e) {},
          });
        },
        //充卡会员enter方法
        bindInquire: function (phone) {
          var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
          if (!phone) {
            this.$message.error({
              message: "输入手机号,登录会员",
              duration: 1500,
            });
          } else if (phone.length != "11" && !reg.test(phone)) {
            if (phone.length == 10) {
              this.loadEntityCard(phone);
            } else {
            }
          } else {
            this.memberSearch(phone);
          }
        },
        //查询会员
        memberSearch: function (phone) {
          var _self = this;
          var keyword = phone;
          let loading = this.$loading({
            lock: true,
            text: "加载中...",
            spinner: "el-icon-loading",
            background: "rgba(255,255,255,0)",
          });
          $.ajax({
            url: _self.url + "/android/vip/memberSearch",
            type: "post",
            data: {
              keyword: keyword,
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              _self.loading = false;
              // var res = JSON.parse(res);
              //定义的变量赋值，给
              // _self.memberInformation = res.data[0];
              // _self.cz_huiyuanxinxi = res.data[0];
              // _self.is_sechargeMember = false;
              // _self.is_showgeMember = true;

              if (res.code == 1 && res.data.length > 0) {
                _self.memberInformation = res.data[0];
                _self.cz_huiyuanxinxi = res.data[0];
                _self.is_sechargeMember = false;
                _self.is_showgeMember = true;

                _self.memberInfo = res.data[0];
                _self.MemberName = _self.memberInfo["member_name"];
                _self.zhk_sex = _self.memberInfo["sex"];
                loading.close();
              } else {
                loading.close();
                if (phone.length == 11) {
                  _self.$message.error({
                    message: "未找到此会员信息",
                    duration: 1500,
                  });
                }
              }
            },
            error: function (error) {
              this.loading = false;
            },
          });
        },
        // 充卡请求会员方法
        bindzhkInquireMember: function (phone) {
          // var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
          // console.log(phone);
          // if (phone.length == 7) {
          //     this.memberSearch(phone)
          // }
          if (phone.length == 11) {
            this.memberSearch(phone);
          }
        },

        //自动查询服务
        serviceList: function (flag) {
          var _self = this;
          _self.loading = true;
          $.ajax({
            url: _self.url + "/android/Service/serviceList",
            type: "post",
            data: {
              keyword: _self.search_keyword,
              labelid: _self.serverLabelid,
              sort: 1, // 默认正序，1正序 2倒序
              status: 1, // 1上架。  2下架
              storeid: _self.loginInfo.storeid,
              page: _self.serverPage,
              limit: _self.serverLimit,
            },
            success: function (res) {
              _self.loading = false;
              // var res = JSON.parse(res);
              if (res.code == 0) {
                _self.serverAllCount = res.count;
                if (_self.serverAllCount < 10) {
                  _self.busy = false;
                }
                if (flag == 1) {
                  _self.zhk_server_name = _self.zhk_server_name.concat(
                    res.data
                  );
                  if (res.count == 0) {
                    _self.isServerScroll = true;
                  } else {
                    _self.isServerScroll = false;
                  }
                } else {
                  _self.zhk_server_name = res.data;
                  _self.isServerScroll = false;
                }
                // console.log('起始数据');
                // console.log(_self.cashier_open_order_service_name);
                //获取数据后然后对数据赋值这个时候将查不到数据的div属性换掉
                //以下在加载服务时候给没一个服务添加一个服务人员id用来存储后面要穿的值
                for (let i = 0; i < _self.zhk_server_name.length; i++) {
                  _self.zhk_server_name[i]["technician_id"] = [];
                  _self.zhk_server_name[i]["salesmen"] = [];
                  _self.zhk_server_name[i]["num"] = 1;
                  _self.zhk_server_name[i]["manualDiscount"] = 1;
                  _self.zhk_server_name[i]["discount"] = 1;
                  _self.zhk_server_name[i]["manualDiscountCard"] = {};
                  _self.zhk_server_name[i]["zhonglei"] = 1; //添加一个字段用来判断种类
                  _self.zhk_server_name[i]["_animationId"] = Date.now() + i; //添加动画标识
                }
              } else {
                _self.zhk_server_name = [];
                _self.$message({
                  type: "error",
                  message: "暂无数据",
                  duration: 1500,
                });
              }
            },
            error: function (error) {
              _self.loading = false;
            },
          });
        },

        chioce_sex: function (index) {
          var _self = this;
          _self.newMember["vio_is_sex"] = index;
        },
        // 开始长按
        startLongPress: function (value, event) {
          const _self = this;
          _self.longPressData = value;
          _self.longPressEvent = event;
          _self.isLongPressing = false;
          _self.wasLongPressed = false; // 重置长按标记

          // 0.4秒后开始长按模式
          _self.longPressTimer = setTimeout(() => {
            _self.isLongPressing = true;
            _self.startContinuousAdd();
          }, 400); // 恢复到400ms (0.4秒)
        },
        // 结束长按
        endLongPress: function () {
          const _self = this;

          // 如果当前正在长按模式，说明确实进入了长按，设置标记
          const wasInLongPressMode = _self.isLongPressing;

          if (_self.longPressTimer) {
            clearTimeout(_self.longPressTimer);
            _self.longPressTimer = null;
          }
          if (_self.longPressFirstTimer) {
            clearTimeout(_self.longPressFirstTimer);
            _self.longPressFirstTimer = null;
          }
          if (_self.longPressInterval) {
            clearInterval(_self.longPressInterval);
            _self.longPressInterval = null;
          }

          _self.isLongPressing = false;
          _self.longPressData = null;
          _self.longPressEvent = null;

          // 只有真正进入过长按模式才设置标记
          if (wasInLongPressMode) {
            _self.wasLongPressed = true;
            // 100ms后自动重置标记，防止影响后续正常点击
            setTimeout(() => {
              _self.wasLongPressed = false;
            }, 100);
          }
        },
        // 开始连续添加
        startContinuousAdd: function () {
          const _self = this;
          if (!_self.isLongPressing) return;

          // 第一次+10在200ms后执行
          _self.longPressFirstTimer = setTimeout(() => {
            if (!_self.isLongPressing) return;

            // 第一次添加10个数量
            _self.addServiceQuantity(
              _self.longPressData,
              _self.longPressEvent,
              10
            );

            // 后续每600ms执行一次
            _self.longPressInterval = setInterval(() => {
              if (!_self.isLongPressing) {
                clearInterval(_self.longPressInterval);
                return;
              }

              // 添加10个数量
              _self.addServiceQuantity(
                _self.longPressData,
                _self.longPressEvent,
                10
              );
            }, 1000);
          }, 200);
        },
        // 添加服务数量（支持指定数量）
        addServiceQuantity: function (value, event, quantity = 1) {
          const _self = this;
          const data = value;
          var zhk_price;
          var priceIndex = data["price"].indexOf("-");
          if (priceIndex == -1) {
            zhk_price = data["price"];
          } else {
            zhk_price = data["price"].slice(priceIndex + 1);
          }
          var arrlength = _self.zhk_server_details_name.length;

          // 查找是否已存在该服务
          let duplicateIndex = -1;
          for (let i = 0; i < arrlength; i++) {
            if (data["id"] == _self.zhk_server_details_name[i]["id"]) {
              duplicateIndex = i;
              break;
            }
          }

          if (duplicateIndex !== -1) {
            // 如果服务已存在，增加指定数量
            const item = _self.zhk_server_details_name[duplicateIndex];
            const newQuantity = Math.min(item.numberAvailable + quantity, 9999);
            const actualAdded = newQuantity - item.numberAvailable;

            if (actualAdded > 0) {
              item.numberAvailable = newQuantity;

              // 重新计算价格
              const unitPrice = parseFloat(item.unitPrice) || 0;
              const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
              item.subtotal = subtotal;
              item.totalAmount = Math.round(
                unitPrice * item.numberAvailable * 100
              );

              // 显示弹出数字效果
              if (event) {
                _self.createPopupNumber(
                  event.clientX,
                  event.clientY,
                  newQuantity,
                  quantity >= 10, // 如果一次添加10个或以上，认为是长按模式
                  false // 增加操作
                );
              }

              // 滚动到对应项并高亮
              _self.$nextTick(() => {
                const serviceCards =
                  document.querySelectorAll(".o-service-card");
                if (serviceCards[duplicateIndex]) {
                  serviceCards[duplicateIndex].scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });

                  serviceCards[duplicateIndex].classList.add(
                    "o-service-card-highlight"
                  );
                  setTimeout(() => {
                    serviceCards[duplicateIndex].classList.remove(
                      "o-service-card-highlight"
                    );
                  }, 1000);
                }
              });

              _self.CalculatePrice();
            }
          } else {
            // 如果服务不存在，添加新服务
            data["numberAvailable"] = quantity;
            data["zhk_price_show"] = zhk_price;
            data["zhk_price"] = zhk_price * 100;
            data["unitPrice"] = data["price"];
            data["totalAmount"] = zhk_price * quantity * 100;
            data["subtotal"] = (zhk_price * quantity).toFixed(2);
            data["_animationId"] = Date.now() + Math.random();

            _self.$set(value, "_justAdded", true);
            setTimeout(() => {
              _self.$set(value, "_justAdded", false);
            }, 600);

            _self.zhk_server_details_name.unshift(
              JSON.parse(JSON.stringify(data))
            );

            // 显示弹出数字效果
            if (event) {
              _self.createPopupNumber(
                event.clientX,
                event.clientY,
                quantity, // 新添加服务时显示的就是当前数量
                quantity >= 10, // 如果一次添加10个或以上，认为是长按模式
                false // 增加操作
              );

              // 显示操作类型弹出效果
              const actionText = quantity === 1 ? "+1" : `+${quantity}`;
              _self.createActionPopup(
                event.clientX,
                event.clientY,
                actionText,
                false // 增加操作
              );
            }

            // 滚动到顶端
            _self.$nextTick(() => {
              const serviceCardsContainers =
                document.querySelectorAll(".o-scrollbar");
              const rightScrollContainer = serviceCardsContainers[1];
              if (rightScrollContainer) {
                rightScrollContainer.scrollTo({
                  top: 0,
                  behavior: "smooth",
                });
              }
            });

            _self.CalculatePrice();
          }
        },
        // 统一的鼠标按下处理
        handleMouseDown: function (value, event) {
          if (event.button === 0) {
            // 左键按下
            this.startLongPress(value, event);
          } else if (event.button === 2) {
            // 右键按下
            const startTime = performance.now();
            console.log("右键按下，开始计时，时间戳:", startTime);
            event.preventDefault();
            event.stopPropagation();

            // 使用performance.now()获取更精确的时间
            this.rightPressStartTime = startTime;
            this.rightLongPressData = value;
            this.rightLongPressEvent = event;
            this.isRightPressed = true;

            // 验证时间记录
            console.log(
              "验证：rightPressStartTime已设置为:",
              this.rightPressStartTime
            );

            // 启动长按检测
            this.startRightLongPressDetection();
          }
        },
        // 统一的鼠标松开处理
        handleMouseUp: function (event) {
          if (event.button === 0) {
            // 左键松开
            this.endLongPress();
          } else if (event.button === 2) {
            // 右键松开
            if (this.isRightPressed) {
              const endTime = performance.now();
              const startTime = this.rightPressStartTime || 0;
              const pressDuration = endTime - startTime;
              console.log(
                "右键松开，开始时间:",
                startTime,
                "结束时间:",
                endTime,
                "持续时间:",
                pressDuration.toFixed(2) + "ms"
              );
              console.log("验证：isRightPressed =", this.isRightPressed);
              console.log(
                "验证：isRightLongPressing =",
                this.isRightLongPressing
              );

              // 记录是否处于长按模式
              const wasInLongPressMode = this.isRightLongPressing;

              this.isRightPressed = false;

              // 清除所有右键长按相关的定时器
              this.clearRightLongPressTimers();

              // 如果持续时间小于400ms且没有进入长按模式，执行单击逻辑
              if (pressDuration < 400 && !wasInLongPressMode) {
                console.log("右键短按，执行-1");
                this.removeServiceQuantity(
                  this.rightLongPressData,
                  this.rightLongPressEvent,
                  1
                );
              } else if (wasInLongPressMode) {
                console.log("右键长按结束");
                // 设置长按结束标记，防止触发click事件
                this.wasRightLongPressed = true;
                setTimeout(() => {
                  this.wasRightLongPressed = false;
                }, 100);
              }
              // 长按逻辑已经在定时器中处理了
            }
          }
        },
        // 统一的鼠标离开处理
        handleMouseLeave: function (event) {
          // 暂时移除，因为会干扰长按
        },
        // 开始右键长按检测
        startRightLongPressDetection: function () {
          const _self = this;

          // 400ms后开始长按模式
          _self.rightLongPressTimer = setTimeout(() => {
            if (_self.isRightPressed) {
              console.log("进入右键长按模式");
              _self.isRightLongPressing = true;

              // 200ms后执行第一次-10
              _self.rightLongPressFirstTimer = setTimeout(() => {
                if (_self.isRightPressed && _self.isRightLongPressing) {
                  console.log("执行第一次右键-10");
                  _self.removeServiceQuantity(
                    _self.rightLongPressData,
                    _self.rightLongPressEvent,
                    10
                  );

                  // 开始每1000ms执行一次的循环
                  _self.rightLongPressInterval = setInterval(() => {
                    if (_self.isRightPressed && _self.isRightLongPressing) {
                      console.log("执行后续右键-10");
                      _self.removeServiceQuantity(
                        _self.rightLongPressData,
                        _self.rightLongPressEvent,
                        10
                      );
                    } else {
                      clearInterval(_self.rightLongPressInterval);
                    }
                  }, 1000);
                }
              }, 200);
            }
          }, 400);
        },
        // 清除右键长按定时器
        clearRightLongPressTimers: function () {
          if (this.rightLongPressTimer) {
            clearTimeout(this.rightLongPressTimer);
            this.rightLongPressTimer = null;
          }
          if (this.rightLongPressFirstTimer) {
            clearTimeout(this.rightLongPressFirstTimer);
            this.rightLongPressFirstTimer = null;
          }
          if (this.rightLongPressInterval) {
            clearInterval(this.rightLongPressInterval);
            this.rightLongPressInterval = null;
          }
          this.isRightLongPressing = false;
        },
        // 处理右键长按动作
        handleRightLongPressAction: function (pressDuration) {
          console.log("执行右键长按动作，持续时间:", pressDuration);
          const _self = this;

          if (_self.rightLongPressData && _self.rightLongPressEvent) {
            // 计算应该减少多少次
            const baseTime = 400; // 基础长按时间
            const intervalTime = 1000; // 每次间隔时间
            const firstActionTime = 200; // 第一次动作延迟

            if (pressDuration >= baseTime + firstActionTime) {
              // 至少执行一次-10
              let actionCount = 1;
              const remainingTime = pressDuration - baseTime - firstActionTime;
              actionCount += Math.floor(remainingTime / intervalTime);

              console.log("应该执行", actionCount, "次-10操作");

              // 执行减少操作
              for (let i = 0; i < actionCount; i++) {
                _self.removeServiceQuantity(
                  _self.rightLongPressData,
                  _self.rightLongPressEvent,
                  10
                );
              }
            }
          }
        },
        // 处理右键点击
        handleRightClick: function (value, event) {
          // 这个方法现在主要用于阻止右键菜单
          // 实际的右键逻辑在mousedown/mouseup中处理
          console.log("右键菜单被阻止");
          event.preventDefault();
          event.stopPropagation();
          return false;
        },
        // 右键单击减少服务（现在不再使用，逻辑已移到handleMouseUp中）
        bind_zhk_remove_server: function (value, event) {
          console.log("bind_zhk_remove_server被调用（不应该发生）");
        },
        // 开始右键长按
        startRightLongPress: function (value, event) {
          console.log("开始右键长按", value);
          const _self = this;
          _self.rightLongPressData = value;
          _self.rightLongPressEvent = event;
          _self.isRightLongPressing = false;
          _self.wasRightLongPressed = false; // 重置长按标记

          // 0.4秒后开始右键长按模式
          _self.rightLongPressTimer = setTimeout(() => {
            console.log("进入右键长按模式");
            _self.isRightLongPressing = true;
            _self.startRightContinuousRemove();
          }, 400);
        },
        // 结束右键长按
        endRightLongPress: function () {
          const _self = this;

          // 如果当前正在右键长按模式，说明确实进入了长按，设置标记
          const wasInRightLongPressMode = _self.isRightLongPressing;

          if (_self.rightLongPressTimer) {
            clearTimeout(_self.rightLongPressTimer);
            _self.rightLongPressTimer = null;
          }
          if (_self.rightLongPressFirstTimer) {
            clearTimeout(_self.rightLongPressFirstTimer);
            _self.rightLongPressFirstTimer = null;
          }
          if (_self.rightLongPressInterval) {
            clearInterval(_self.rightLongPressInterval);
            _self.rightLongPressInterval = null;
          }

          _self.isRightLongPressing = false;
          _self.rightLongPressData = null;
          _self.rightLongPressEvent = null;

          // 只有真正进入过右键长按模式才设置标记
          if (wasInRightLongPressMode) {
            _self.wasRightLongPressed = true;
            // 100ms后自动重置标记，防止影响后续正常点击
            setTimeout(() => {
              _self.wasRightLongPressed = false;
            }, 100);
          }
        },
        // 开始右键连续减少
        startRightContinuousRemove: function () {
          console.log("开始右键连续减少");
          const _self = this;
          if (!_self.isRightLongPressing) return;

          // 第一次-10在200ms后执行
          _self.rightLongPressFirstTimer = setTimeout(() => {
            if (!_self.isRightLongPressing) return;
            console.log("执行第一次右键-10");

            // 第一次减少10个数量
            _self.removeServiceQuantity(
              _self.rightLongPressData,
              _self.rightLongPressEvent,
              10
            );

            // 后续每1000ms执行一次
            _self.rightLongPressInterval = setInterval(() => {
              if (!_self.isRightLongPressing) {
                clearInterval(_self.rightLongPressInterval);
                return;
              }

              // 减少10个数量
              _self.removeServiceQuantity(
                _self.rightLongPressData,
                _self.rightLongPressEvent,
                10
              );
            }, 1000);
          }, 200);
        },
        // 减少服务数量（支持指定数量）
        removeServiceQuantity: function (value, event, quantity = 1) {
          const _self = this;
          const data = value;
          var arrlength = _self.zhk_server_details_name.length;

          // 查找是否存在该服务
          let existingIndex = -1;
          for (let i = 0; i < arrlength; i++) {
            if (data["id"] == _self.zhk_server_details_name[i]["id"]) {
              existingIndex = i;
              break;
            }
          }

          if (existingIndex !== -1) {
            const item = _self.zhk_server_details_name[existingIndex];
            const newQuantity = Math.max(item.numberAvailable - quantity, 0);
            const actualRemoved = item.numberAvailable - newQuantity;

            if (actualRemoved > 0) {
              if (newQuantity === 0) {
                // 如果数量变为0，删除该服务项
                _self.$set(item, "_removing", true);
                setTimeout(() => {
                  _self.zhk_server_details_name.splice(existingIndex, 1);
                  _self.CalculatePrice();
                }, 100);
              } else {
                // 更新数量
                item.numberAvailable = newQuantity;

                // 重新计算价格
                const unitPrice = parseFloat(item.unitPrice) || 0;
                const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
                item.subtotal = subtotal;
                item.totalAmount = Math.round(
                  unitPrice * item.numberAvailable * 100
                );

                _self.CalculatePrice();
              }

              // 显示弹出数字效果（负数）
              if (event) {
                _self.createPopupNumber(
                  event.clientX,
                  event.clientY,
                  newQuantity,
                  quantity >= 10, // 如果一次减少10个或以上，认为是长按模式
                  true // 标记为减少操作
                );

                // 显示操作类型弹出效果
                const actionText = quantity === 1 ? "-1" : `-${quantity}`;
                _self.createActionPopup(
                  event.clientX,
                  event.clientY,
                  actionText,
                  true // 减少操作
                );
              }

              // 滚动到对应项并高亮
              if (newQuantity > 0) {
                _self.$nextTick(() => {
                  const serviceCards =
                    document.querySelectorAll(".o-service-card");
                  if (serviceCards[existingIndex]) {
                    serviceCards[existingIndex].scrollIntoView({
                      behavior: "smooth",
                      block: "center",
                    });

                    serviceCards[existingIndex].classList.add(
                      "o-service-card-highlight"
                    );
                    setTimeout(() => {
                      serviceCards[existingIndex].classList.remove(
                        "o-service-card-highlight"
                      );
                    }, 1000);
                  }
                });
              }
            }
          }
        },
        // 创建弹出数字效果
        createPopupNumber: function (
          x,
          y,
          number,
          isLongPress = false,
          isDecrease = false
        ) {
          const popup = document.createElement("div");

          // 根据操作类型和模式设置样式类
          let className = "number-popup ";
          if (isDecrease) {
            // 减少操作：单击用蓝色，长按用红色
            className += isLongPress ? "number-popup-red" : "number-popup-blue";
          } else {
            // 增加操作：单击用蓝色，长按用红色
            className += isLongPress ? "number-popup-red" : "number-popup-blue";
          }

          popup.className = className;
          popup.textContent = number.toString();
          popup.style.left = x + "px";
          popup.style.top = y + "px";

          document.body.appendChild(popup);

          // 动画结束后移除元素
          setTimeout(() => {
            if (popup.parentNode) {
              popup.parentNode.removeChild(popup);
            }
          }, 1000);
        },
        // 创建操作类型弹出效果
        createActionPopup: function (x, y, action, isDecrease = false) {
          const popup = document.createElement("div");

          // 根据操作类型设置样式类
          let className = "action-popup ";
          className += isDecrease ? "action-popup-red" : "action-popup-green";

          popup.className = className;
          popup.textContent = action;
          popup.style.left = x + "px";
          popup.style.top = y + "px";

          document.body.appendChild(popup);

          // 动画结束后移除元素
          setTimeout(() => {
            if (popup.parentNode) {
              popup.parentNode.removeChild(popup);
            }
          }, 1200);
        },
        //充卡点击服务列表
        bind_zhk_add_server: function (value, event) {
          console.log(value);
          var _self = this;

          // 如果是右键点击，不执行左键逻辑
          if (event && event.button === 2) {
            return;
          }

          // 如果正在长按模式，不执行单击逻辑
          if (_self.isLongPressing) {
            return;
          }

          // 如果正在右键长按模式，不执行单击逻辑
          if (_self.isRightLongPressing || _self.isRightPressed) {
            return;
          }

          // 如果刚刚结束长按，不执行单击逻辑
          if (_self.wasLongPressed) {
            _self.wasLongPressed = false; // 重置标记
            return;
          }

          // 如果刚刚结束右键长按，不执行单击逻辑
          if (_self.wasRightLongPressed) {
            _self.wasRightLongPressed = false; // 重置标记
            return;
          }

          // 调用统一的添加服务数量方法
          _self.addServiceQuantity(value, event, 1);
        },
        //每次添加服务和删除服务都会触发价格的事件
        CalculatePrice: function () {
          var _self = this;
          _self.input_price = 0;
          var arrlength = _self.zhk_server_details_name.length;
          if (arrlength == 0) {
            // 当没有服务项目时，价格为0
            _self.input_price = 0;
            _self.input_price_show = "0.00";
          } else {
            for (let i = 0; i < _self.zhk_server_details_name.length; i++) {
              _self.input_price +=
                _self.zhk_server_details_name[i]["totalAmount"];
            }
            _self.input_price_show = (_self.input_price / 100).toFixed(2);
          }
          if (_self.input_dis != "") {
            _self.pay_all_show = (
              _self.input_price *
              ((100 - _self.input_dis) / 10000)
            ).toFixed(2);
          } else {
            _self.pay_all_show = (_self.input_price / 100).toFixed(2);
          }
        },
        // 键盘等搜索按钮搜索事件
        billingInquiryEnter: function () {
          var _self = this;
          _self.serverPage = 1;
          _self.serviceList();
        },
        //ZHK点击服务标签
        bind_choice_server: function (data, key) {
          let allWidth = 0; //所有标签的总接口
          let count = 0; //所有标签的总数量
          let scrollWidth = 0; //滚动条移动的距离
          this.$refs.comChooseLiTag.forEach((item) => {
            allWidth = allWidth + item.clientWidth + 10;
            count = count + 1;
          });
          if (allWidth > this.$refs.comChooseTag.clientWidth) {
            if (key == 0) {
              scrollWidth = 0;
            } else {
              scrollWidth =
                (allWidth - this.$refs.comChooseTag.clientWidth) / count;
            }
          }
          this.isActiveServer = key;
          this.serverLabelid = data.id || 0;
          this.serverPage = 1;
          this.serviceList();
          this.search_keyword = "";
          this.$nextTick(() => {
            this.$refs.serverCardWrap.scrollTop = 0;
            this.$refs.comChooseTag.scrollLeft = scrollWidth * (key + 1);
          });
        },
        loadMoreProduct: function () {
          var _self = this;
          _self.isServerScroll = true;
          if (_self.serverAllCount == this.zhk_server_name.length) {
            _self.loadingtip = "数据已全部加载";
            return false;
          } else {
            _self.busy = true;
            _self.loadingtip = "加载中···";
            _self.serverPage++;
            _self.serviceList(1);
          }
        },
        //减少充卡的次数
        jianshao: function (index) {
          var _self = this;
          const item = _self.zhk_server_details_name[index];

          if (item.numberAvailable == 1) {
            item.numberAvailable = 1;
          } else {
            item.numberAvailable--;
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          _self.CalculatePrice();
        },
        //增加充卡的次数
        zengjia: function (index) {
          var _self = this;
          const item = _self.zhk_server_details_name[index];

          if (item.numberAvailable == 9999) {
            item.numberAvailable = 9999;
          } else {
            item.numberAvailable++;
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          _self.CalculatePrice();
        },
        handleNumInputChange: function (e, index) {
          const _self = this;
          const item = _self.zhk_server_details_name[index];
          const numValue = parseInt(e.target.value);

          // 验证输入值是否为有效数字
          if (isNaN(numValue)) {
            // 如果输入无效，重置为1
            item.numberAvailable = 1;
          } else {
            // 限制数量范围在1-9999之间
            if (numValue < 1) {
              item.numberAvailable = 1;
            } else if (numValue > 9999) {
              item.numberAvailable = 9999;
            } else {
              item.numberAvailable = numValue;
            }
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          // 重新计算总价
          _self.CalculatePrice();
        },

        //调整小计
        handleSubtotalChange: function (index) {
          const _self = this;
          const item = _self.zhk_server_details_name[index];
          const subtotal = parseFloat(item.subtotal) || 0;
          const quantity = parseInt(item.numberAvailable) || 1;

          // 根据小计和数量计算单价 = 小计 / 数量
          const unitPrice =
            quantity > 0 ? (subtotal / quantity).toFixed(2) : "0.00";
          item.unitPrice = unitPrice;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(subtotal * 100);

          // 重新计算总价
          _self.CalculatePrice();
        },
        //取单
        get_detail: function () {},
        //保存
        save_box: function () {},

        //充卡收款
        goSettle: function (flag) {
          var _self = this;
          var saveorderData = _self.zhk_server_details_name;
          //赋值购买会员的信息
          var buyerId = _self.memberInfo.id;
          var orderItems = [];
          var equityType = 1;
          // var discount = ((100 - _self.input_dis) / 10).toString();
          // if (_self.input_dis){
          //     equityType=1
          // } else{
          //     equityType=2
          // }
          // var pricePay= Math.round(_self.pay_all_show*100);
          // console.log('服务',pricePay);
          //获取销售id
          var salesid = [];
          // 下面是单独销售的逻辑，现在合并销售了
          /* for (let i = 0; i < _self.zhk_xiao_shou_zhanshi.length; i++) {
              salesid.push(_self.zhk_xiao_shou_zhanshi[i]["id"]);
            } */
          salesid = _self.zhk_xiao_shou_zhanshi.map((item) => item["id"]);
          for (var i = 0; i < saveorderData.length; i++) {
            orderItems.push({
              // cardName: save_orderData[i].manualDiscountCard['cardName'] || '',
              // cardName: '',
              // cardDetailsId: save_orderData[i].manualDiscountCard['id'] || 0,     // (开单使用)使用卡项详情的id
              cardDetailsId: 0, // 【可选】(开单使用)使用卡项详情的id
              // cardId: save_orderData[i].manualDiscountCard['membercard_id'] || 0,   // 卡项id
              cardId: 0, // 【可选】卡项id
              // discount: save_orderData[i].manualDiscountCard['discount'] || '10', // 折扣（开单选择充值卡有）
              discount: 1, // 【可选】折扣（开单选择充值卡有）
              equityType: 1, // 【可选】1 无权益 2折扣 3抵扣 4手动改价
              goodsId: saveorderData[i].id, // 【必填】商品id 服务，产品卡项都填写id
              itemId: 0, // 【可选】
              itemImgId: saveorderData[i].itemImgId || "0", // 【可选】预览图id
              itemName: saveorderData[i].service_name, // 【必填】商品名称
              itemType: 1, // 【必填】1 服务 2产品 3卡项 4充值
              num: saveorderData[i].numberAvailable, // 【必填】数量
              originPrice: saveorderData[i].unitPrice * 100, // 【必填】单价  原价( 分 )
              average_price: saveorderData[i].unitPrice * 100, // 【必填】新单价  原价( 分 )
              recharge_money: 0, // 【条件必填】充值金额（本金）金额 （分） itemType=4时必传
              realPay: saveorderData[i].subtotal * 100, // 【必填】小计（分）
              present_money: 0, // 【条件必填】充值（赠送）金额 (分) itemType=4时必传
              salesmen: salesid || [], // 【可选】选择的销售id
              skuId: 0, // 【可选】规格id，非规格天写0
              skuName: "", // 【可选】规格名称（如：红色,大）没有填空字符串
              stage: "1", // 【可选】当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
              technicians: [], // 【可选】服务人员id
            });
          }
          //充卡收款需要判断是不是要加会员
          if (!_self.is_showgeMember) {
            _self.$message({
              type: "warning ",
              message: "请选择会员",
              duration: 1500,
            });
            return;
          }

          if (!saveorderData) {
            _self.$message({
              type: "warning ",
              message: "请选择服务",
              duration: 2500,
            });
            return;
          }
          _self.extraData = {};
          if (_self.cardinfo.permanent == 1) {
            _self.extraData["indate"] = 0;
          }

          if (_self.cardinfo.permanent == 2) {
            if (!_self.cardinfo.validity_time) {
              _self.$message({
                type: "warning ",
                message: "请输入有效天数",
                duration: 2500,
              });
              return;
            }
            _self.extraData["indate"] = 0;
            _self.extraData["effectiveMethod"] = _self.cardinfo.effectiveMethod;
            _self.extraData["validity_time"] = _self.cardinfo.validity_time;
          }

          if (_self.cardinfo.permanent == 3) {
            if (!_self.cardinfo.zuheka_validity) {
              _self.$message({
                type: "warning ",
                message: "请选择到期日期",
                duration: 2500,
              });
              return;
            }
            _self.extraData["indate"] = _self.cardinfo.zuheka_validity;
          }

          _self.extraData["comboCardMoney"] = parseInt(
            Number(_self.pay_all_show) * 100
          );

          // _self.extraData["indate"] = _self.checked ? 0 : _self.zuheka_validity;

          if (_self.helpStaffArr[_self.isactive1]) {
            _self.extraData["help_staff"] = _self.helpStaffArr[
              _self.isactive1
            ].map((item) => item["id"]);
          }

          $.ajax({
            url: _self.url + "/android/order/orderSave",
            type: "post",
            data: {
              // addressInfo: "", // 【可选】收货地址信息
              // bookerid: 0, // 【可选】预约id 来源是预约时使用
              buyerId: buyerId, // 【必填】用户id (orderType为3,4,5,7,8时必填)
              cashierId: _self.loginInfo.id, // 【必填】收银员id
              // dispatchFee: 0, // 【可选】运费 （分）
              // dispatchType: 0, // 【可选】配送类型： 1，到店自提，2，配送，0，非配送
              merchantid: _self.loginInfo.merchantid, // 【必填】商户id
              orderGiftItems: [], // 【可选】订单礼品数组（预留字段）
              orderItems: JSON.stringify(orderItems), // 【必填】订单的详情信息
              // orderNo: 0, // 【可选】订单号（取单时需要传）
              orderType: 5, // 【必填】1：服务；2：产品；3：办卡；4：充值 5：充卡；6 直接收款
              promotions: [], // 【可选】预留字段（优惠信息）
              // presentData: JSON.stringify([]), // 【可选】预留字段（优惠信息）
              remark: _self.beizhu_info, // 【可选】订单备注
              sourceType: 1, // 【必填】来源类型 1,开单，2,预约，3,取单
              storeid: _self.loginInfo.storeid, // 【必填】店铺id
              totalPay: Math.round(_self.pay_all_show * 100), // 【可选，后台用于打印日志看】订单总价（分）
              shift_no: _self.loginInfo.shift_no, // 【必填】班次单号 (android接口必填)
              extraData: JSON.stringify(_self.extraData), // 【可选】充卡额外参数
            },
            success: function (res) {
              _self.loading = false;
              if (res.code != 1) {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
                return;
              }

              // _self.kd_xinxi_list = res.data;
              _self.helpStaffArr[_self.isactive1] = [];
              if (flag == "receipt") {
                //收款
                _self.buy_receipt = true;
                _self.loading = false;
                _self.orderNo = res.data.orderNo;
                _self.orderId = res.data.id;
                _self.isRechargeCard = true;
                _self.zhk_server_details_name = [];
                _self.billToPay = 1;
                _self.isPayStatus = 0; // 开启会员支付
                // _self.isPayStatus = 4;
              }
            },
            error: function (error) {
              _self.loading = false;
            },
          });
        },
        //overtime 点完日期取消永久时间
        overtime: function () {
          if (this.zuheka_validity) {
            this.checked = false;
          }
        },
        ConfirmPermanent: function () {
          if (this.checked == true) {
            this.zuheka_validity = "";
          }
        },
      },
      filters: {
        ellipsis(value) {
          if (!value) return "";
          if (value.length > 12) {
            return value.slice(0, 12) + "...";
          }
          return value;
        },

        // 格式化充值金额/100
        filterMoney: function (money) {
          money = money ? money : 0;
          return (money / 100).toFixed(2);
        },
      },
      watch: {
        vipSearch: function (n, o) {
          if (!n && o) {
            this.searchCurrentPage = 1;
            this.getMember();
          }
        },
        login: {
          handler(n) {
            this.loginInfo = n;
          },
          deep: true,
          immediate: true,
        },
        search_keyword: {
          handler(n) {
            this.serviceList();
          },
          deep: true,
          immediate: true,
        },
        zuheka_validity: {
          handler(n) {
            this.overtime();
          },
          deep: true,
          immediate: true,
        },
        checked: {
          handler(n) {
            this.ConfirmPermanent();
          },
          deep: true,
          immediate: true,
        },
        /* input_price_show: {
            handler(n) {
              this.TotalAmountMatching();
              // this.CalculatePrice();
            },
          }, */
        deductionPrice: {
          handler(n) {
            if (this.discountMethods == "deduction") {
              this.deductionMatch();
            }
          },
        },
        input_dis: {
          handler(n) {
            if (this.discountMethods == "discount") {
              this.discountMatch();
            }
          },
        },
        handle_foucs_input: {
          handler(n) {
            this.inputFocus(this.$refs.search_keyword);
          },
        },
      },
      beforeDestroy: function () {
        // 清理事件监听器
        $(document).off("mouseenter", ".o-price-select-tag");
        $(document).off("mouseleave", ".o-price-select-tag");

        // 清理长按定时器
        this.endLongPress();
        this.endRightLongPress();
        this.clearRightLongPressTimers();

        // 清理全局事件监听器
        if (this.globalMouseUpHandler) {
          document.removeEventListener("mouseup", this.globalMouseUpHandler);
        }
      },
    });
  });
});
