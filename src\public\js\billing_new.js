const { compile } = require("ejs");

let bus = new Vue();
var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

// 自定义指令的格式，名字
Vue.directive("loadmore", {
  bind(el, binding) {
    const selectWrap = el.querySelector(".el-table__body-wrapper");
    selectWrap.addEventListener("scroll", function () {
      let sign = 50;
      const scrollDistance =
        this.scrollHeight - this.scrollTop - this.clientHeight;
      if (scrollDistance <= sign) {
        binding.value();
      }
    });
  },
});
Number.prototype.toFixed = function (d) {
  var s = this + "";
  if (!d) d = 0;
  if (s.indexOf(".") == -1) s += ".";
  s += new Array(d + 1).join("0");
  if (new RegExp("^(-|\\+)?(\\d+(\\.\\d{0," + (d + 1) + "})?)\\d*$").test(s)) {
    var s = "0" + RegExp.$2,
      pm = RegExp.$1,
      a = RegExp.$3.length,
      b = true;
    if (a == d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1]) > 4) {
        for (var i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i]) + 1;
          if (a[i] == 10) {
            a[i] = 0;
            b = i != 1;
          } else break;
        }
      }
      s = a.join("").replace(new RegExp("(\\d+)(\\d{" + d + "})\\d$"), "$1.$2");
    }
    if (b) s = s.substr(1);
    return (pm + s).replace(/\.$/, "");
  }
  return this + "";
};

Vue.component("app-receipt", function (resolve, reject) {
  $.get("component/receipt.html").then(function (res) {
    resolve({
      template: res,
      props: {
        "receipt-member": {
          type: Object,
        },
        login: {
          type: Object,
        },
        handle_foucs_input: {
          type: Boolean,
          value: false,
        },
        "transition-direction": {
          type: String,
          value: "slide-down",
        },
      },
      data: function () {
        return {
          url: baseUrl,
          value1: "1",
          value2: "2",
          value3: "3",
          zj_shou_qian: "",
          zj_price_menu: [
            {
              key1: "7",
              key2: "8",
              key3: "9",
            },
            {
              key1: "4",
              key2: "5",
              key3: "6",
            },
            {
              key1: "1",
              key2: "2",
              key3: "3",
            },
            {
              key1: "00",
              key2: "0",
              key3: "•",
            },
          ],
          zj_all_num: [
            {
              key: "01",
              value: "7",
            },
            {
              key: "02",
              value: "8",
            },
            {
              key: "03",
              value: "9",
            },
            {
              key: "11",
              value: "4",
            },
            {
              key: "12",
              value: "5",
            },
            {
              key: "13",
              value: "6",
            },
            {
              key: "21",
              value: "1",
            },
            {
              key: "22",
              value: "2",
            },
            {
              key: "23",
              value: "3",
            },
            {
              key: "31",
              value: "00",
            },
            {
              key: "32",
              value: "0",
            },
            {
              key: "33",
              value: ".",
            },
          ],
          zj_max_price: false,
          is_searchMember: false,
          receipt_searchMember: "",
          receiptMemberInfo: {}, //会员信息
          loginInfo: {}, // 登录信息
          orderId: 0,
          // 客户搜索框
          vipSearch: "",
          isShowMemberSearch: false,
          vip_info: [],
          searchLimit: 50,
          searchCurrentPage: 1,
          searchAllCount: 0,
          searchLoading: false,
          searchMemberTableHeight: 400,
        };
      },

      mounted: function () {
        this.inputFocus(this.$refs.moneyInner);
      },

      methods: {
        //会员列表
        getMember: function () {
          var _self = this;
          _self.searchLoading = true;
          $.ajax({
            url: _self.url + "/android/Member/getMember",
            type: "POST",
            data: {
              adviser: "", // 新客归属（筛选用）
              birthday: 0, // 生日 0 全部 1 今天 2明天 3 本周 4 本月 5 下月
              count: -1, // 消费次数，-1 全部 -2 一次以内 -3 三次以内 -4 五次以内，自定义直接输入次数筛选用
              starttime: "", // 开始时间
              endtime: "", // 结束时间（生日筛选 "年月日"）
              keyword: _self.vipSearch, // 会员名称、备注名、会员编号、手机号搜索
              last_time: -1, // 消费期限，-1 全部、-2 一个月，-3 两个月，-4三个月，自定义直输入数字（筛选用，默认空）
              level_id: "", // 等级id
              maxscore: _self.memberPointsMin, // 积分范围，最大积分
              minscore: -1, // 积分范围，最小积分
              member_source: "", // 来源id
              limit: _self.searchLimit, // 分页每页条数
              page: _self.searchCurrentPage, // 分页第几页（必须）
              tab_id: "", // 标签id
              merchant_id: _self.loginInfo.merchantid, // 商户id
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.searchLoading = false;
                // _self.isFlag = false;
                _self.vip_info = res.data;
                _self.allCount = res.count;
              } else {
                _self.searchLoading = false;
                _self.$message.error({
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        // 选定客户赋值
        handleSelect(data) {
          this.receiptMemberInfo = data;
        },
        //聚焦
        inputFocus: function (dom) {
          this.$nextTick(
            function () {
              dom.focus();
            }.bind(this)
          );
        },

        //监听
        manualPrice: function () {
          this.zj_shou_qian = this.zj_shou_qian.replace(/[^\d\.]/g, ""); //去掉字符串中除数字和.之外的其它字符
          this.zj_shou_qian = this.zj_shou_qian.replace(/^\./g, "0.");
          this.zj_shou_qian = this.zj_shou_qian.replace(/\.{2,}/g, ".");
          this.zj_shou_qian = this.zj_shou_qian
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          this.zj_shou_qian = this.zj_shou_qian.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          this.zj_shou_qian = this.zj_shou_qian.replace(/^0.$/, "0.");
          if (this.zj_shou_qian > 100000.0) {
            this.zj_shou_qian = "100000.00";
          }
        },
        // 清空
        zj_del_all: function () {
          this.zj_shou_qian = "";
          this.inputFocus(this.$refs.moneyInner);
        },
        // 删除
        zj_del_one: function () {
          this.inputFocus(this.$refs.moneyInner);
          let input_lenth = this.zj_shou_qian.length;
          if (this.zj_shou_qian.length > 0) {
            this.zj_shou_qian = this.zj_shou_qian.slice(0, input_lenth - 1);
          }
        },

        // 数字键
        zj_input_num: function (index, value1) {
          this.inputFocus(this.$refs.moneyInner);

          let isdian = this.zj_shou_qian.indexOf(".");
          let input_lenth = this.zj_shou_qian.length;
          let get_hlnum = index + value1;
          let cha_num = input_lenth - isdian - 1;

          if (input_lenth == 0) {
            if (get_hlnum == "33" || get_hlnum == "31" || get_hlnum == "32") {
              this.zj_shou_qian += "0.";
            } else {
              for (let i of this.zj_all_num) {
                // console.log(i.key);
                if (i.key == get_hlnum) {
                  this.zj_shou_qian += i.value;
                }
              }
            }
          } else {
            if (isdian == -1) {
              for (let i of this.zj_all_num) {
                if (i.key == get_hlnum) {
                  this.zj_shou_qian += i.value;
                }
              }
              if (this.zj_shou_qian > 100000.0) {
                this.zj_shou_qian = "100000.00";
                let that = this;
                setTimeout(function () {
                  that.zj_max_price = true;
                }, 200);
                setTimeout(function () {
                  that.zj_max_price = false;
                }, 1200);
              }
            } else if (this.zj_shou_qian == "0.") {
              if (cha_num * 1 < 2) {
                for (let i of this.zj_all_num) {
                  if (!(get_hlnum == "33")) {
                    if (i.key == get_hlnum) {
                      this.zj_shou_qian += i.value;
                    }
                  }
                }
              }
            } else {
              if (cha_num < 2) {
                for (let i of this.zj_all_num) {
                  // console.log(i.key);
                  if (i.key == get_hlnum) {
                    this.zj_shou_qian += i.value;
                  }
                }
              }
            }
          }
        },

        // 选择会员
        selectMember: function () {
          this.is_searchMember = true;
        },

        del_receiptMember: function () {
          this.receiptMemberInfo = {};
          this.receipt_searchMember = "";
        },

        receipt_search: function (phone) {
          this.$emit("receipt-phone", phone);
        },
        receipt_enter: function (phone) {
          this.$emit("receipt-enter", phone);
        },
        //取消
        cancelSearchMember: function () {
          this.receipt_searchMember = "";
          this.receiptMemberInfo = {};
          this.is_searchMember = false;
        },

        inputFocus: function (dom) {
          this.$nextTick(
            function () {
              dom.focus();
            }.bind(this)
          );
        },

        directCollection: function () {
          var _self = this;
          if (!_self.zj_shou_qian) {
            _self.inputFocus(this.$refs.moneyInner);
            return false;
          }
          var buyerId = 0;
          if (this.receiptMemberInfo && this.receiptMemberInfo.id) {
            buyerId = this.receiptMemberInfo.id;
          }
          var orderItems = [
            {
              cardDetailsId: 0, // (开单使用)使用卡项详情的id
              cardId: 0, // 卡项id
              consumeCard: 1, //是否耗卡（1,不是，2，是）
              consumeCardId: 0, //耗卡的卡项id（consumeCard==2时有效）默认账户传0，其他他传卡项id
              discount: "10", // 折扣（开单选择充值卡有）
              equityType: 1, // 1 无权益 2折扣 3抵扣 4手动改价
              goodsId: 0, // 商品id 服务，产品卡项都填写id
              itemId: "0",
              itemImgId: "0", // 预览图id
              itemName: "直接收款", // 商品名称
              itemType: 5, // 1 服务 2产品 3卡项 4充值
              num: 1, // 数量，除产品外，其他都填写1
              originPrice: Math.round(Number(_self.zj_shou_qian) * 100), // 充值金额  原价( 分 )
              recharge_money: 0, // 充值金额（本金）金额 （分） 手动充值时必传
              realPay: Math.round(Number(_self.zj_shou_qian) * 100), // 充值金额真实支付（分）
              present_money: 0, // 充值（赠送）金额 (分) 手动充值时必传
              salesmen: [], // 选择的销售id
              skuId: 0, // 规格id，非规格天写0
              skuName: "", // 规格名称（如：红色,大）没有填空字符串
              stage: "1", // 当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
              technicians: [], // 服务人员id
            },
          ];
          var orderGiftItems = [];
          var promotions = [];
          $.ajax({
            url: _self.url + "/android/order/orderSave",
            type: "post",
            data: {
              addressInfo: "", // 收货地址信息
              bookerid: 0, // 预约id 来源是预约时使用
              buyerId: buyerId, // 用户id
              cashierId: _self.loginInfo.id, // 收银员id
              dispatchFee: 0, // 运费 （分）
              dispatchType: 0, // 配送类型： 1，到店自提，2，配送，0，非配送
              merchantid: _self.loginInfo.merchantid, //  商户id
              orderGiftItems: JSON.stringify([]), //订单礼品数组（预留字段）
              orderNo: "", // 订单号（取单时需要传）
              orderType: 6, // 1：服务；2：产品；3：办卡；4：充值 5：充卡；6 直接收款
              promotions: JSON.stringify([]), // 预留字段（优惠信息）
              presentData: JSON.stringify([]), // 预留字段（优惠信息）
              remark: "", // 订单备注
              sourceType: 1, // 来源类型 1,开单，2,预约，3,取单
              storeid: _self.loginInfo.storeid, // 店铺id
              totalPay: Math.round(Number(_self.zj_shou_qian) * 100), // 订单总价（分）
              orderItems: JSON.stringify(orderItems),
              shift_no: _self.loginInfo.shift_no,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              _self.loading = false;
              global.flag = 0;
              if (res.code == 1) {
                var obj = {
                  buy_receipt: true,
                  data: res.data,
                };
                _self.$emit("send", obj);
                _self.receiptMemberInfo = {};
                _self.zj_shou_qian = "";
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            error: function (error) {
              _self.loading = false;
            },
          });
        },
      },

      filters: {
        // 格式化充值金额/100
        filterMoney: function (money) {
          money = money ? money : 0;
          return (money / 100).toFixed(2);
        },
      },
      watch: {
        vipSearch: function (n, o) {
          if (!n && o) {
            this.searchCurrentPage = 1;
            this.getMember();
          }
        },
        receiptMember: function (n) {
          this.receiptMemberInfo = n;
        },

        login: {
          handler(n) {
            this.loginInfo = n;
          },
          deep: true,
          immediate: true,
        },
        handle_foucs_input: {
          handler(n) {
            this.inputFocus(this.$refs.moneyInner);
          },
        },
      },
    });
  });
});

//赠送商品的组件
Vue.component("app-gift", function (resolve, reject) {
  $.get("component/choose_gift.html").then(function (res) {
    resolve({
      template: res,
      props: {
        login: {
          type: Object,
        },
        giftDataArray: {
          type: Array,
        },
        member: {
          type: Object,
        },
      },
      data: function () {
        let goodsSearchConfig = localStorage.getItem("goodsSearchConfig");
        let serverLimit = 10;
        let productLimit = 10;
        if (goodsSearchConfig) {
          goodsSearchConfig = JSON.parse(goodsSearchConfig);
          serverLimit = goodsSearchConfig["serverLimit"]
            ? goodsSearchConfig["serverLimit"]
            : 10;
          productLimit = goodsSearchConfig["productLimit"]
            ? goodsSearchConfig["productLimit"]
            : 10;
        }
        return {
          url: baseUrl,
          search_keyword: "",
          serverLabelid: 0,
          serverGiftPage: 1,
          serverLimit: serverLimit,
          product_keyword: "",
          productLabelid: 0,
          productPage: 1,
          productLimit: productLimit,
          isServerGiftScroll: false,
          isProductScroll: false,
          isChooseGift: true,
          billingType: 1,
          C_open_order_specifications_save: [],
          cashier_open_order_service_name: [],
          cashier_open_order_product_name: [],
          // 触底加载
          loadingtip: "加载中···",
          busy: false,
          busyCard: false,
          busyProduct: false,
          loginInfo: {},
          memberInfo: {},
          search_product: "",
          giftData: [],
          isProductSpecifications: false,
          C_open_order_Specifications: {},
          specificationData: {},
          C_open_order_specifications_attr: {},
          C_open_order_specifications_name: {},
          sku: {},
          giftPickTimeOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7; //如果没有后面的-8.64e7就是不可以选择今天的
            },
          },
        };
      },
      mounted: function () {
        this.productGiftList(0);
        this.$nextTick(() => {
          this.$refs.productWrap.scrollTop = 0;
          this.inputGiftFocus(this.$refs.search_product);
        });
        // this.serviceList();
      },
      methods: {
        // uuai 首页--服务查询  添加销售和服务人员的key还有数量都在这个函数里面
        serviceGiftList: function (flag) {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Service/serviceList",
            type: "post",
            data: {
              keyword: _self.search_keyword,
              labelid: _self.serverLabelid,
              sort: 1, // 默认正序，1正序 2倒序
              status: 1, // 1上架。  2下架
              storeid: _self.loginInfo.storeid,
              page: _self.serverGiftPage,
              limit: _self.serverLimit,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 0) {
                _self.serverAllCount = res.count;
                if (_self.serverAllCount < 10) {
                  _self.busy = false;
                }
                if (flag == 1) {
                  _self.cashier_open_order_service_name =
                    _self.cashier_open_order_service_name.concat(res.data);
                  if (res.count == 0) {
                    _self.isServerGiftScroll = true;
                  } else {
                    _self.isServerGiftScroll = false;
                  }
                } else {
                  _self.cashier_open_order_service_name = res.data;

                  _self.isServerGiftScroll = false;
                }
                // console.log('起始数据');
                // console.log(_self.cashier_open_order_service_name);
                //获取数据后然后对数据赋值这个时候将查不到数据的div属性换掉
                //以下在加载服务时候给没一个服务添加一个服务人员id用来存储后面要穿的值
                for (
                  let i = 0;
                  i < _self.cashier_open_order_service_name.length;
                  i++
                ) {
                  _self.cashier_open_order_service_name[i]["technician_id"] =
                    [];
                  _self.cashier_open_order_service_name[i]["salesmen"] = [];
                  _self.cashier_open_order_service_name[i]["num"] = 1;
                  _self.cashier_open_order_service_name[i]["manualDiscount"] =
                    1;
                  _self.cashier_open_order_service_name[i]["discount"] = 1;
                  _self.cashier_open_order_service_name[i][
                    "manualDiscountCard"
                  ] = {};
                  _self.cashier_open_order_service_name[i]["zhonglei"] = 1; //添加一个字段用来判断种类
                }
              } else {
                this.cashier_open_order_service_name = [];
                _self.$message({
                  type: "error",
                  message: "暂无数据",
                  duration: 1500,
                });
              }
            },
          });
        },
        //选择产品接口
        productGiftList: function (flag) {
          this.loading = true;

          var _self = this;
          $.ajax({
            url: _self.url + "/android/Product/getProductData",
            type: "post",
            data: {
              class: "",
              keyword: _self.search_product,
              label: _self.productLabelid,
              limit: _self.productLimit,
              merchantid: _self.loginInfo.merchantid,
              order: "1",
              page: _self.productPage,
              status: "1",
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              _self.productPage++;
              // var res = JSON.parse(res);
              if (res.code == 0) {
                // 接口上是0成功 其他都是1
                _self.productAllCount = res.count;
                if (_self.productAllCount < 10) {
                  _self.busyProduct = false;
                }
                if (flag == 1) {
                  _self.cashier_open_order_product_name =
                    _self.cashier_open_order_product_name.concat(res.data);
                  if (res.count == 0) {
                    _self.isProductScroll = true;
                  } else {
                    _self.isProductScroll = false;
                  }
                } else {
                  _self.cashier_open_order_product_name = res.data;
                }
                for (
                  let i = 0;
                  i < _self.cashier_open_order_product_name.length;
                  i++
                ) {
                  _self.cashier_open_order_product_name[i]["manualDiscount"] =
                    1;
                  _self.cashier_open_order_product_name[i][
                    "manualDiscountCard"
                  ] = {};
                  _self.cashier_open_order_product_name[i]["zhonglei"] = 2;
                  _self.cashier_open_order_product_name[i]["discount"] = 1;
                  _self.cashier_open_order_product_name[i].goodsType = 2;
                }
                // console.log(_self.cashier_open_order_product_name);
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        // 开单产品加载更多
        loadMoreCom: function () {
          var _self = this;
          _self.isProductScroll = true;
          _self.busyProduct = true;
          if (
            _self.productAllCount ==
            _self.cashier_open_order_product_name.length
          ) {
            _self.loadingtip = "数据已全部加载";
            return false;
          } else {
            _self.busyProduct = true;
            _self.loadingtip = "加载中···";
            // _self.productPage;
            _self.productGiftList(1);
          }
        },

        loadProductMore: function () {
          var _self = this;

          _self.isServerGiftScroll = true;
          _self.busy = true;
          if (
            _self.serverAllCount == this.cashier_open_order_service_name.length
          ) {
            _self.loadingtip = "数据已全部加载";
            return false;
          } else {
            _self.busy = true;
            _self.loadingtip = "加载中···";
            _self.serverGiftPage++;
            _self.serviceGiftList(1);
          }
        },

        billingChangeType: function (value) {
          if (value == 0) {
            if (this.memberInfo && this.memberInfo.id) {
              this.switchFunction();
            } else {
              this.$message({
                type: "warning",
                message: "未登会员不能赠送服务",
                duration: 1500,
              });
              this.billingType = 1;
            }
          } else {
            this.switchFunction();
          }
        },
        switchFunction: function () {
          // var billingType = this.billingType;
          switch (this.billingType) {
            case "0":
              if (!this.cashier_open_order_service_name.length > 0) {
                this.isServerGiftScroll = false;
                this.serverGiftPage = 1;
                this.serviceGiftList();
              }
              this.$nextTick(() => {
                this.$refs.serverWrap.scrollTop = 0;
                this.inputGiftFocus(this.$refs.search_keyword);
              });
              break;
            case "1":
              if (!this.cashier_open_order_product_name.length > 0) {
                this.isProductScroll = false;
                this.productPage = 1;
                this.productGiftList();
              }
              this.$nextTick(() => {
                this.$refs.productWrap.scrollTop = 0;
                this.inputGiftFocus(this.$refs.search_product);
              });
              break;
          }
          this.search_product = this.search_keyword = "";
        },
        // 键盘等搜索按钮搜索事件
        billingInquiryEnter: function () {
          var _self = this;

          _self.serverGiftPage = 1;
          _self.serviceGiftList();
        },

        // 键盘等搜索按钮搜索事件
        billingInquiryProductEnter: function () {
          var _self = this;

          _self.productPage = 1;
          _self.productGiftList();
        },

        inputGiftFocus: function (dom) {
          this.$nextTick(
            function () {
              dom.focus();
              $(this.$refs.actualHarvest).focus();
            }.bind(this)
          );
        },

        closeChooseGift: function () {
          this.isChooseGift = false;
          this.giftData = [];
          this.$emit("show-choose-gift", false);
          this.$emit("choose-gift-data", this.giftData);
        },

        //格式化日期：yyyy-MM-dd  日期默认加一周
        formatDate: function (date) {
          date.setDate(date.getDate() + 7);
          var myyear = date.getFullYear();
          var mymonth = date.getMonth() + 1;
          var myweekday = date.getDate();

          if (mymonth < 10) {
            mymonth = "0" + mymonth;
          }
          if (myweekday < 10) {
            myweekday = "0" + myweekday;
          }
          return myyear + "-" + mymonth + "-" + myweekday;
        },

        //数量计数器
        giftNumChange: function (value, index) {
          // console.log(value);
          // console.log(index);
          this.giftData[index].originPrice = this.giftData[index].price * value;
          // console.log(this.giftData[index].originPrice);
        },

        addGiftServer: function (data, index) {
          let giftDataObj = {};
          giftDataObj.itemImgId = data.itemImgId;
          giftDataObj.itemName = data.service_name;
          giftDataObj.itemType = 1;
          giftDataObj.goodsId = data.id;
          giftDataObj.num = 1;
          giftDataObj.skuId = 0;
          giftDataObj.skuName = "";
          if (data.price.indexOf("-") == -1) {
            giftDataObj.originPrice = Number(data.price) * 100;
          } else {
            let price = data.price.split("-")[1];
            giftDataObj.originPrice = Number(price) * 100;
          }
          giftDataObj.price = giftDataObj.originPrice;
          // giftDataObj.indate=this.formatDate(new Date());
          giftDataObj.indate = "";
          this.giftData.push(giftDataObj);
        },
        addGiftProduct: function (data, index) {
          let _self = this;

          if (data.issku == 1) {
            $.ajax({
              url: _self.url + "/android/Product/getProductInfo",
              type: "post",
              data: {
                productId: data.id,
                merchantid: _self.loginInfo.merchantid,
                storeid: _self.loginInfo.storeid,
              },
              success: function (res) {
                // var res = JSON.parse(res);
                if (res.code == 1) {
                  _self.isProductSpecifications = true;
                  let price = "";
                  if (res.data["s_price"].indexOf("￥") != -1) {
                    price = res.data["s_price"].substring(1);
                  }
                  _self.C_open_order_Specifications["price"] = price;
                  _self.specificationData = res.data;
                  _self.C_open_order_specifications_name =
                    res["data"]["skuinfo"]["skuattr"];
                  _self.C_open_order_specifications_attr =
                    res["data"]["skuinfo"]["skulist"];
                  for (
                    let i = 0;
                    i < _self.C_open_order_specifications_name.length;
                    i++
                  ) {
                    for (
                      let j = 0;
                      j <
                      _self.C_open_order_specifications_name[i]["item"].length;
                      j++
                    ) {
                      _self.C_open_order_specifications_name[i]["item"][
                        j
                      ].is_show = false;
                    }
                  }
                } else {
                  // console.log(1);
                  _self.$message({
                    type: "error",
                    message: res.msg,
                    duration: 1500,
                  });
                }
              },
            });
          } else {
            let giftDataObj = {};
            giftDataObj.itemImgId = data.itemImgId;
            giftDataObj.itemName = data.product_name;
            giftDataObj.itemType = 2;
            giftDataObj.goodsId = data.id;
            giftDataObj.num = 1;
            giftDataObj.skuId = 0;
            giftDataObj.skuName = "";
            giftDataObj.originPrice = Number(data.price);
            giftDataObj.price = Number(data.price);
            giftDataObj.indate = 0;
            this.giftData.push(giftDataObj);
          }
        },

        //批量时间
        copyIndate: function (index) {
          if (this.giftData) {
            let copyTime = this.giftData[index].indate;
            this.giftData.forEach((item) => {
              if (item.itemType == 1) {
                item.indate = copyTime;
              }
            });
          }
        },

        handleCloseSpecification: function () {
          this.isProductSpecifications = false;
          // 清除扣卡数据
          _self.chooseProjuctFlag = false;
          _self.chooseProjuctService = {};
        },

        //选择产品规格
        specificationBtn_product: function (data, dataItem, index1, index2) {
          let _self = this;

          let time_true = 0;
          let specifications = []; //存储最终确定时候保存的选中规格id
          let specifications_atrr = ""; //存储对比属性的sku_val_id值

          for (let i = 0; i < data.item.length; i++) {
            let item = data.item[i];
            if (item.id == dataItem.id) {
              if (item.is_show == true) {
                item.is_show = false;
              } else {
                item.is_show = true;
              }
            } else {
              item.is_show = false;
            }
          }
          //因为要动态将选择规格的价格改变所以添加循环为判断是否全选了
          for (
            let i = 0;
            i < _self.C_open_order_specifications_name.length;
            i++
          ) {
            for (
              let j = 0;
              j < _self.C_open_order_specifications_name[i]["item"].length;
              j++
            ) {
              if (
                _self.C_open_order_specifications_name[i]["item"][j][
                  "is_show"
                ] == true
              ) {
                time_true++;
                specifications.push(
                  _self.C_open_order_specifications_name[i]["item"][j]["id"]
                );
              }
            }
          }

          //全选了为其匹配赋值
          if (time_true == _self.C_open_order_specifications_name.length) {
            for (
              let i = 0;
              i < _self.C_open_order_specifications_attr.length;
              i++
            ) {
              specifications_atrr =
                _self.C_open_order_specifications_attr[i]["sku_val_id"];
              if (specifications_atrr == specifications.join(",")) {
                _self.C_open_order_Specifications["price"] = parseFloat(
                  _self.C_open_order_specifications_attr[i]["price"] / 100
                ).toFixed(2);
                _self.C_open_order_Specifications["subtotal"] = (
                  _self.C_open_order_Specifications["price"] *
                  _self.C_open_order_Specifications["num"]
                ).toFixed(2);
                _self.sku.id =
                  _self._self.C_open_order_specifications_attr[i].id;
                _self.sku.name =
                  _self._self.C_open_order_specifications_attr[i].sku;
                _self.sku.price =
                  _self._self.C_open_order_specifications_attr[i].price;
              }
            }
          } else {
            // _self.C_open_order_Specifications['price'] = _self.data_server_product['realPrice']
            let price = "";
            if (_self.specificationData["s_price"].indexOf("￥") != -1) {
              price = _self.specificationData["s_price"].substring(1);
            }
            _self.C_open_order_Specifications["price"] = price;
          }
          this.$forceUpdate();
        },

        cancelProductPpecifications: function () {
          this.isProductSpecifications = false;
        },
        saveProductSpecifications: function () {
          let giftDataObj = {};
          giftDataObj.itemImgId = this.specificationData.img_id;
          giftDataObj.itemName = this.specificationData.product_name;
          giftDataObj.itemType = 2;
          giftDataObj.goodsId = this.specificationData.store_product_id;
          giftDataObj.num = 1;
          giftDataObj.skuId = this.sku.id;
          giftDataObj.skuName = this.sku.name;
          giftDataObj.originPrice = Number(this.sku.price);
          giftDataObj.price = Number(this.sku.price);
          giftDataObj.indate = 0;
          this.giftData.push(giftDataObj);
          this.isProductSpecifications = false;
        },
        clearPage: function () {
          this.giftData = [];
        },
        open_details_price_del: function (index) {
          this.giftData.splice(index, 1);
        },

        cancelChooseGift: function () {
          this.giftData = [];
          this.isChooseGift = false;
          this.$emit("show-choose-gift", false);
          this.$emit("choose-gift-data", this.giftData);
        },
        comfirmChooseGift: function () {
          if (this.giftData) {
            let flag = false;
            for (let i = 0; i < this.giftData.length; i++) {
              let item = this.giftData[i];
              if (!item.indate && item.itemType == 1) {
                flag = true;
                break;
              }
            }
            if (flag) {
              this.$message({
                type: "warning",
                message: "赠送的服务没选择有效期",
                duration: 1500,
              });
            } else {
              this.isChooseGift = false;
              this.$emit("show-choose-gift", false);
              this.$emit("choose-gift-data", this.giftData);
            }
          } else {
            this.isChooseGift = false;
            this.$emit("show-choose-gift", false);
            this.$emit("choose-gift-data", this.giftData);
          }
        },
      },
      filters: {
        // 格式化充值金额/100
        filterMoney: function (money) {
          money = money ? money : 0;
          return (money / 100).toFixed(2);
        },
      },
      computed: {},
      watch: {
        login: {
          handler: function (n) {
            this.isChooseGift = true;
            this.loginInfo = n;
          },
          deep: true,
          immediate: true,
        },
        giftDataArray: {
          handler: function (n) {
            this.giftData = n;
          },
          deep: true,
          immediate: true,
        },
        member: {
          handler: function (n) {
            this.memberInfo = n;
          },
          deep: true,
          immediate: true,
        },
      },
    });
  });
});

Vue.component("member-search-dialog", function (resolve, reject) {
  $.get("component/memberSearchDialog.html").then(function (res) {
    resolve({
      template: res,
      props: {
        value: {
          type: Boolean,
          default: false,
        },
        loginInfo: {
          type: Object,
          default: function () {
            return {
              merchantid: "",
              storeid: "",
            };
          },
        },
      },
      data: function () {
        return {
          vipSearch: "",
          vip_info: [],
          searchLimit: 50,
          searchCurrentPage: 1,
          searchAllCount: 0,
          searchLoading: false,
          searchMemberTableHeight: 400,
          isShowMemberSearch: false,
          url: baseUrl,
        };
      },
      methods: {
        calculateTableHeight() {
          this.$nextTick(() => {
            if (this.$refs.searchMemberDialogRef) {
              const clientHeight =
                this.$refs.searchMemberDialogRef.$el.clientHeight;
              this.searchMemberTableHeight = clientHeight - 400;
            }
          });
        },
        handleShowSearchMember() {
          this.vipSearch = "";
          this.getMember();
          // this.isShowMemberSearch = true;
          this.calculateTableHeight();
          window.addEventListener("resize", this.calculateTableHeight);
        },
        handleCloseSearchMember() {
          this.isShowMemberSearch = false;
          window.removeEventListener("resize", this.calculateTableHeight);
          this.$emit("sync-is-show-memeber-search", false);
        },
        //会员列表
        getMember: function () {
          var _self = this;

          // 确保 loginInfo 已初始化
          if (
            !_self.loginInfo ||
            !_self.loginInfo.merchantid ||
            !_self.loginInfo.storeid
          ) {
            _self.$message.error({
              message: "登录信息不完整，请重新登录",
              duration: 1500,
            });
            return;
          }

          _self.searchLoading = true;
          $.ajax({
            url: _self.url + "/android/Member/getMember",
            type: "POST",
            data: {
              adviser: "", // 新客归属（筛选用）
              birthday: 0, // 生日 0 全部 1 今天 2明天 3 本周 4 本月 5 下月
              count: -1, // 消费次数，-1 全部 -2 一次以内 -3 三次以内 -4 五次以内，自定义直接输入次数筛选用
              starttime: "", // 开始时间
              endtime: "", // 结束时间（生日筛选 "年月日"）
              keyword: _self.vipSearch, // 会员名称、备注名、会员编号、手机号搜索
              last_time: -1, // 消费期限，-1 全部、-2 一个月，-3 两个月，-4三个月，自定义直输入数字（筛选用，默认空）
              level_id: "", // 等级id
              maxscore: _self.memberPointsMin, // 积分范围，最大积分
              minscore: -1, // 积分范围，最小积分
              member_source: "", // 来源id
              limit: _self.searchLimit, // 分页每页条数
              page: _self.searchCurrentPage, // 分页第几页（必须）
              tab_id: "", // 标签id
              merchant_id: _self.loginInfo.merchantid, // 商户id
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.searchLoading = false;
                // _self.isFlag = false;
                _self.vip_info = res.data;
                _self.allCount = res.count;
              } else {
                _self.searchLoading = false;
                _self.$message.error({
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },
        formatMoney(money) {
          return ff_util.formatMoney(money / 100);
        },
        // TODO 重复函数，有待提取
        fromTheCurrentTime(date) {
          if (!date) return "";
          try {
            const today = new Date();
            const inputDate = new Date(date);

            if (isNaN(inputDate.getTime())) return "";

            let years = today.getFullYear() - inputDate.getFullYear();
            let months = today.getMonth() - inputDate.getMonth();

            if (months < 0) {
              years--;
              months += 12;
            }

            // 如果日期小于当前日期，月份减1
            if (today.getDate() < inputDate.getDate()) {
              months--;
              if (months < 0) {
                years--;
                months += 12;
              }
            }

            // 计算总月份
            const totalMonths = years * 12 + months;

            // 如果小于1年，只返回月份
            if (years < 1) {
              return `${totalMonths} 个月`;
            }

            // 如果小于2年，返回"几年几个月"
            if (years < 2) {
              return `${years} 年 ${months} 个月`;
            }

            // 否则只返回"几年"
            return `${years} 年`;
          } catch (error) {
            return "";
          }
        },
        getAge(birthday) {
          if (!birthday) return "";
          try {
            const today = new Date();
            const birthDate = new Date(birthday);

            // 检查birthDate是否为有效日期
            if (isNaN(birthDate.getTime())) return "";

            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (
              monthDiff < 0 ||
              (monthDiff === 0 && today.getDate() < birthDate.getDate())
            ) {
              age--;
            }
            return age + " 岁";
          } catch (e) {
            return "";
          }
        },
        calculateDaysAgo(value) {
          if (!value) return "";
          const now = new Date();
          const lastTime = new Date(value);
          const diffTime = Math.abs(now - lastTime);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return diffDays + " 天";
        },
        //    以下是分页的方法
        handleSizeChange: function (val) {
          this.limit = val;
          this.isFlag = true;
          this.getMember();
        },
        handleCurrentChange: function (val) {
          this.currentPage = val;
          this.isFlag = true;
          this.getMember();
          this.$nextTick(() => {
            this.$refs.vipTable.bodyWrapper.scrollTop = 0;
          });
        },
        // 选定客户
        chioceMember(row) {
          // this.handleSelect(row);
          // 创建普通对象副本，避免传递响应式数据
          const plainRow = JSON.parse(JSON.stringify(row));
          // const plainRow = Vue.observable(row);
          this.$emit("handle-select-member", plainRow);
          this.handleCloseSearchMember();
        },
        bindSearch: function () {
          if (!this.vipSearch) return;
          this.searchCurrentPage = 1;
          this.getMember();
        },
        //自动完成选择会员列表
        handleSelect: function (data) {
          let _self = this;
          switch (_self.isactive1) {
            case 0:
              // 开单
              _self.memberInfo = data;
              _self.memberSearchSuccess = 1;
              // 获取余额卡信息
              _self.getBalanceCard(1);
              // 获取扣卡信息
              _self.getMemberStampCardInfo(1);
              // 获取权益信息
              _self.billingType = 2;
              break;
            case 1:
              // 办卡
              // _self.memberObjData = data;
              // _self.memberObj.name = data.member_name;
              _self.memberObjData = [data];
              _self.isVipCardMember = true;
              _self.memberObj.name = data.member_name;
              _self.memberObj.phone = data.phone;
              _self.memberObj.id = data.id;
              break;
            case 3:
              // 收银台直接收款
              _self.receiptMember = data;
              break;
          }
        },
      },
      watch: {
        value: {
          handler(n) {
            this.isShowMemberSearch = n;

            this.$emit("sync-is-show-memeber-search", n);
            if (n) {
              this.handleShowSearchMember();
            }
          },
        },
      },
    });
  });
});

var app = new Vue({
  el: "#app",
  data: function () {
    let goodsSearchConfig = localStorage.getItem("goodsSearchConfig");
    //console.log(goodsSearchConfig);
    let serverLimit = 10;
    let productLimit = 10;
    let cardLimit = 10;
    if (goodsSearchConfig) {
      goodsSearchConfig = JSON.parse(goodsSearchConfig);
      serverLimit = goodsSearchConfig["serverLimit"]
        ? goodsSearchConfig["serverLimit"]
        : 10;
      productLimit = goodsSearchConfig["productLimit"]
        ? goodsSearchConfig["productLimit"]
        : 10;
      cardLimit = goodsSearchConfig["cardLimit"]
        ? goodsSearchConfig["cardLimit"]
        : 10;
    }
    return {
      istbz: top.app.istbz,
      globalTechnicianName: globalTechnicianName,
      oneObj: {},
      //对接口数据
      //登录页面
      chioced_id: 0, //bindOffer中接口里seevicedid
      timeCardCount: [], //bindOffer中接口里保存次卡的数组
      activeCollapseNames: "0", //折叠面板的v-model的值
      membercardChangeIndex: 0, //折叠面板change事件的值
      newMemberCardInfo: [],
      sampleMemberCardInfo: [], //单独抽出所有权益中的卡
      sampleMemberCardInfo_runOut: [], // 用完的服务
      chooseProjuctFlag: false, //会员扣卡选择规格时的标志位，为了区别会员扣卡时带权益的服务和普通选择规格的服务，出现在保存规格的方法里
      chooseProjuctServiceId: 0,
      orderCount: 0, //获取url上从订单页面过来的订单号
      orderSourceType: 1, //订单来源类型 1,开单，2,预约，3,取单
      isPayStatus: 4, //跳到收银台Pay组件   0:会员余额，1:支付宝/微信，2:现金；4：自定义记账
      loading: true,
      loginInfo: {},
      url: baseUrl,
      isactive1: 0,
      transitionDirection: "slide-down", // 用于控制页面切换的动画方向

      C_open_order_Specifications: {},
      index_server_product: null, //定义一个变量用来给确定规格判断是服务还是产品的确定规格
      data_server_product: null, //定义一个变量用来给确定规格判断是服务还是产品的数据
      is_product_server: false,
      C_open_order_specifications_name: [], //记录可选规格的值数组
      C_open_order_specifications_attr: [], //记录选择规格后要对比的属性数组
      C_Specifications_choice1_class: [],
      cashier_open_order_service_choice: [], //选择服务后保留的服务数据

      // 开单
      billingType: 0, //服务 卡项  扣卡

      cashier_open_order_service_name: [], // 开单--服务
      cashier_open_order_service_label: [], // 标签数据
      serverLabelid: 0,
      isActiveServer: 0,
      serverPage: 1,
      serverLimit: serverLimit,
      serverAllCount: 0,
      search_keyword: "",
      isServerScroll: false,

      cashier_open_order_product_name: [], //开单--产品
      product_label: [],
      isActiveProduct: 0,
      productLabelid: 0,
      search_product: "",
      productPage: 1,
      productLimit: productLimit,
      productAllCount: 0,
      // 开单--扣卡
      memberInfo: {},
      /** 开单会员**/
      queryMember: "",
      memberSecondaryCard: [], //扣卡数据
      offerCardOnce: [], //可用的优惠权益
      offerCard_info: [],
      offerCarddis: [],
      balanceCard: [], //会员可用充值卡数据
      judgeCard: 0, //  1:使用优惠权益 2:使用耗卡 0是默认。

      //现金券信息
      couponInfo: [],
      isCouponCard: false,
      assignGoods: false, //指定商品，无门槛
      assignPrice: 0, //指定商品，商品价格

      //办理会员卡中的现金券
      isVipCouponCard: false,

      /*开单详情*/
      beizhu_info: "",
      is_offer: 0,
      carIndex: -1,
      // 取单
      isTakeOrder: false,
      takeOrder: [], //取单列表
      takeOrderLimit: 25,
      takeOrderPage: 1,
      //开单--保存and收款
      saveOrderNo: {},

      //多个用到
      C_open_order_specifications_save: [], //选择规格后或者选择服务后保留的数据
      isGoodsType: 1, //1.服务； 2.商品
      //收银台开单服务标签数据
      open_order_search_servers: [], //用来存储符合查询的数据
      servers_none_show: "servers_none_show_n",

      cashier_open_order_details_server_name: [],
      serviceSpecificationStatus: 1, //有规格的服务的状态

      //开单选择服务人员相关数据
      ji_shi: false, //判断服务人员弹框
      ji_shis: [], //服务人员信息循环数据
      ji_shi_save: [], //存取选中服务人员的相关数据
      ji_shi_zhanshi: [], //用来循环展示服务人员姓名
      which_server_technician: null, //用来确定添加服务人员是给哪一个服务添加服务人员

      //开单选择销售相关数据
      xiao_shou: false, //判断弹出框
      xiaoshous: [], //数据循环
      xiao_shou_save: [], //存取选中销售的相关数据
      xiao_shou_zhanshi: [], //用来循环展示销售姓名
      which_server_Sale: null, //判断给哪个添加销售

      //选择批量的相关数据
      pi_liang: false,
      pi_is_ji_shi: [], //用来给批量点击判断选中的服务有没有服务人员
      pi_is_xiaoshou: [], //用来给批量点击判断选中的服务有没有销售
      pi_is_ji_name: "", //给服务添加字段显示选中的服务人员姓名
      pi_is_xiao_name: "", //给服务添加字段显示选中的销售姓名
      order_is_show_jishi: true, //判断批量框是不是要展示应用服务人员
      order_is_show_both: true, //判断批量框要不要展示应用服务人员和销售
      //以下是左侧导航条判断数据
      shouyin_kaidan_show: true,
      shouyin_banka_show: false,
      shouyin_chongzhi_show: false,
      shouyin_zuheka_show: false,
      shouyin_zhijieshoukuan_show: false,

      //收银台开单的产品接收数据

      //以下是充值的数据信息

      /* uuai 充值 */

      cz_chongzhika: [], //充值卡列表
      cz_search_keyword: "", //会员搜索
      is_cz_sex: null, //性别
      cz_huiyuanxinxi: {}, //充值会员信息
      is_sechargeMember: false,

      cz_single_czk: {},

      cz_chongzhijine: [], //充值列表
      userDefined: 0, // 是否开启自定义充值
      cz_customize: 0,
      is_recharge_card: false, //充值卡

      cz_iserweima: false, //二维码弹窗
      cz_ischongzhijine: false, //充值弹窗
      // 充值金额弹框的列表

      cz_ischongxiao: false, //选择销售弹窗
      // 充值金额and赠送金额
      cz_chongzhi: {
        benjin: null,
        zengsong: null,
        beizhu: "",
        chongzhikaid: "",
        recharge_name: "",
      },

      //充值页面的添加服务的变量
      chong_add_server: false,
      cz_ischong_Addfuwu: 0,
      cz_server_biaoti: [
        {
          name: "美体",
        },
        {
          name: "护甲",
        },
        {
          name: "推拿",
        },
        {
          name: "足疗",
        },
      ],
      cz_server_biaoti_name2: [
        {
          name: "护甲1",
          price: "100.00",
        },
        {
          name: "护甲2",
          price: "200.00",
        },
        {
          name: "护甲3",
          price: "300.00",
        },
      ],
      cz_server_biaoti_name3: [
        {
          name: "推拿1",
          price: "400.00",
        },
        {
          name: "推拿2",
          price: "500.00",
        },
        {
          name: "推拿3",
          price: "600.00",
        },
      ],
      cz_server_biaoti_name4: [
        {
          name: "足疗1",
          price: "700.00",
        },
        {
          name: "足疗2",
          price: "800.00",
        },
        {
          name: "足疗3",
          price: "900.00",
        },
      ],
      cz_server_biaoti_name: [
        {
          name: "美体1",
          price: "1000.00",
        },
        {
          name: "美体2",
          price: "2000.00",
        },
        {
          name: "美体3",
          price: "3000.00",
        },
      ],
      cz_server_biaoti_name1: [
        {
          name: "美体1",
          price: "1000.00",
        },
        {
          name: "美体2",
          price: "2000.00",
        },
        {
          name: "美体3",
          price: "3000.00",
        },
      ],
      chongzhi_checked: false,
      cz_chong_add_server: false,
      cz_beizhu_info: "",
      cz_pay_all: "5000",
      ch_check1: false,

      right_font1: false,
      right_font2: true,
      right_font3: false,
      // 点击收款后获取的订单信息

      // 开单页面的信息

      kd_czk_list: 0,

      // 充值销售数组
      changexiaoshous: [], //销售数组
      rechange_xiao_shou_zhanshi: [], //展示数组
      rechangeSalesShow: "", //销售展示

      //以下是办卡的数据信息
      // uuai 办卡的变量

      cardType: "0",
      cardKeyword: "", //
      cardArr: [],
      cardTemp: {},
      cardIndex: NaN,
      cardLimit: cardLimit,
      cardPage: 1,
      cardCount: 0,
      isCardScroll: false,
      card_server_name: [],

      isBeneficial: 1, //不使用优惠and 优惠金额
      paymentMoney: 0,
      manualPrice: "",
      memberObj: {
        //会员信息展示
        phone: "",
        name: "",
        is_sex: -1,
      },
      memberObjData: [], //会员信息
      memberInformation: {},
      is_dynamic: false,
      bk_beizhu_info: "", //办卡留言

      bk_server_number_ka: 1,
      bk_xiaoji_price: "",
      bk_tianjia_fuwu: false,
      bk_server_biaoti: [
        {
          name: "美体",
        },
        {
          name: "护甲",
        },
        {
          name: "推拿",
        },
        {
          name: "足疗",
        },
      ],
      //    办卡添加服务模态框变量
      bk_isAddfuwu: 0,

      bk_server_biaoti_name2: [
        {
          name: "护甲1",
          price: "100.00",
        },
        {
          name: "护甲2",
          price: "200.00",
        },
        {
          name: "护甲3",
          price: "300.00",
        },
      ],
      bk_server_biaoti_name3: [
        {
          name: "推拿1",
          price: "400.00",
        },
        {
          name: "推拿2",
          price: "500.00",
        },
        {
          name: "推拿3",
          price: "600.00",
        },
      ],
      bk_server_biaoti_name4: [
        {
          name: "足疗1",
          price: "700.00",
        },
        {
          name: "足疗2",
          price: "800.00",
        },
        {
          name: "足疗3",
          price: "900.00",
        },
      ],
      bk_server_biaoti_name: [
        {
          name: "美体1",
          price: "1000.00",
        },
        {
          name: "美体2",
          price: "2000.00",
        },
        {
          name: "美体3",
          price: "3000.00",
        },
      ],
      bk_server_biaoti_name1: [
        {
          name: "美体1",
          price: "1000.00",
        },
        {
          name: "美体2",
          price: "2000.00",
        },
        {
          name: "美体3",
          price: "3000.00",
        },
      ],
      bk_checked: false,
      bk_youhui_power: false,
      isChooseCostCard: false, //选择耗卡的弹框
      isActiveCostCard: 1, //是否是活跃的耗卡
      bk_is_xuanze_youhui: false,
      bk_xiao_shou: false,

      //办卡选择销售的按钮
      bk_check1: null,
      SalesShow: "", //存储选择的销售
      selesShows: "请选择",
      bk_xiao_shou_zhanshi: [], //展示选中的销售
      //以下是充卡的数据信息
      zhk_todos3: [
        {
          word: "所有服务",
        },
        {
          word: "狐狸",
        },
        {
          word: "美容",
        },
        {
          word: "美体",
        },
      ],
      zhk_isactive2: 0,
      zhk_todos4: [
        {
          word: "美甲1",
          open_price: "100.00",
          url: "123",
        },
        {
          word: "美甲2",
          open_price: "100.00",
          url: "123",
        },
        {
          word: "美甲3",
          open_price: "100.00",
          url: "123",
        },
      ],
      //添加服务的次数
      zhk_server_name: [
        // {open_name: '美甲', open_num: '1', open_price: '100.00', open_price_all: '100.00'},
      ],

      zhk_seen1: 0,
      zhk_pay_all: 22222,
      zhk_save_seen: false,

      //充卡选择销售弹出框的判断
      zhk_check1: -1,
      zhk_xiao_shou: false,
      zhk_xiaoshous: [],
      //    订单备注
      zhk_beizhu_info: "",
      //充卡的数据
      zuheka_validity: "2020-10-10",
      zhk_server_number: 1,
      zhk_checked: false,
      zhk_input_price: "",
      zhk_input_discout: "",
      is_zhk_sex: null,

      //以下是收银台的开单的数据信息
      cashier_open_order_Specifications: false,

      leftMenu: [
        {
          word: "开单",
          id: 0,
        },
        {
          word: "充卡",
          id: 4,
        },
        {
          word: "办卡",
          id: 1,
        },
        {
          word: "充值",
          id: 2,
        },
        {
          word: "直接收款",
          id: 3,
        },
        /* 				{
					word: "核销",
					id: 5
				}, */
      ],

      //以上是俩个导航条弹框
      todos3: [],

      //添加服务的次数
      todos4_num: null,

      change_price_all: "",
      jishi_name: "",
      jishi_name_chioced: [],
      open_all: false,

      selective_sales_volume: "",
      batch: "",
      // seen0:null,
      seen1: 0,
      seen2: 0,
      seen3: 0,
      save_seen: false,

      switch1: -1,
      check1: -1,
      //服务人员弹出框判断

      //    销售弹出框
      check2: "",
      //    取单
      qu_dan: false,

      tetils: [],

      kd_is_xuanze_youhui: false, //开单选择优惠的模态框
      vipChooseCouponCard: false, //开单选择优惠的模态框
      //    以上是收银台开单弹出框

      // 收款
      buy_receipt: false,
      orderNo: "", //订单号
      isRechargeCard: false,
      //开单---直接收款
      receiptMember: {},

      //存储用户的可用权益
      availableEquity: {},
      //定义一个数组用来记录修改订单过来的数据中服务出现的次数和权益相关信息
      changeOrderinfos: [],
      //记录修改订单页面跳过来的时候，当已经选择了优惠权益，并优惠权益次卡。用来记录  该订单的这个服务
      changeDealoncetime: 0,
      //定义个对象用来存储查询会员的权益向后台传值
      member_id: {},
      //定义一个变量用来临时存储修改订单push进去的服务对象最后在赋给C_open_order_specifications_save
      ModifyTheMemberTemporaryObject: [],
      font1: "卡内权益抵扣次数不足，请重新选择",

      payTotal: 0,
      count: 0,

      // 触底加载
      loadingtip: "加载中···",
      busy: false,
      busyCard: false,
      busyProduct: false,
      //打印
      isPrint: false,
      key_num: "",
      paperwidth: 0, //打印纸宽度
      printSet: [], // 设置打印

      //核销
      isVerify: false, //核销查询的弹框
      isVerifySuccess: false, //核销查询的弹框
      verifyCode: "", //核销码
      verifyData: {},
      verify_ji_shi: false, //核销，选择服务人员的弹框
      verifyOrderDetails: {}, //核销详情
      verifyDetailsData: {},
      verifyPayment: {},

      //充卡和直接收款页面input聚焦
      handleFoucsInput: false, //充卡聚焦
      handleDirectFoucsInput: false, //直接收款聚焦

      //现金券
      couponCardCode: "",
      couponVipCardCode: "",

      //选择赠送
      isChooseGift: false, //选择赠送的弹框
      billGiftData: [],
      showGiftData: {
        allPrice: 0,
        serverNum: 0,
        productNum: 0,
      },
      surplus: 0, //店铺的赠送额度

      // //办卡中选择耗卡的弹框
      // isCardChooseCostCard:false,     //办卡中耗卡的弹框
      // isShowCostCard:1,  //办卡页面，展示耗卡信息

      //办卡页面，会员登录信息修改
      isVipCardMember: false,

      //收银台-开单-产品搜索
      setSearchTimeOut: "",

      //核销修改员工业绩
      modifyAchievement: 0,
      verifyDataOrderNo: 0,
      //修改员工业绩
      isModifyPerformance: false,
      zuhekaPerformance: 1, //所有业绩提成信息
      totalPerformance: {},
      performanceList: [], //业绩提成信息列表
      salesmenList: [], //销售业绩列表
      techniciansList: [], //服务人员业绩列表
      isSales: false, //选择销售弹框
      isCrafts: false, //选择服务人员弹框
      isHandelIndex: 0, //每条服务在列表中的下标
      AllSales: [], //所有销售和服务人员
      AllCrafts: [], //所有服务人员
      deductType: [
        {
          name: "比例提成",
          id: 1,
        },
        {
          name: "固定提成",
          id: 2,
        },
      ], //提成方式下拉框
      isDeductType: "", //选择的提成方式
      salesChecked: [], //选中的销售存储
      craftsChecked: [], //存储选中的服务人员
      allDelect: [],
      saveModifyArr: [], //存储修改的数据
      addArr: [], //存储增添的数据
      delArr: [], //存储删除的数据

      //取单时间日期选择器的变量
      fetchOrderTime: ["", ""], //取单选择时间的表的绑定的值
      setQueryFetchOrderTimeOut: "", //取单搜索框的，延迟搜索
      fetchOrderKeyword: "", //取单搜索框绑定的值

      //查看耗材
      isCostMaterial: false,
      costMaterialData: {},
      isPrintCostMaterial: 3, //是否提示打印耗材弹框 1：提示打印弹框  2：不提示弹框直接打印  3：不提示弹框不打印

      //收银台-是否显示还款
      isDebtFlag: false,

      labelScrollFlag: true, // 横向滚动条滚动控件
      memberSearchSuccess: 0,
      timerCardUse: [], // 次数权益使用情况
      balanceCardUse: [], // 余额耗卡使用情况
      // 取单触底加载控制
      takeMoreOrderLoading: false,
      helpStaffArr: {},
      helpStaffAll: [],
      helpStaffVisible: false,
      checkHelpStaffArr: [],
      bindStaffId: 0,
      // 客户搜索框
      vipSearch: "",
      isShowMemberSearch: false,
      vip_info: [],
      searchLimit: 50,
      searchCurrentPage: 1,
      searchAllCount: 0,
      searchLoading: false,
      searchMemberTableHeight: 400,
      // 合并选择
      isShowMergeDialog: false,
      isShowVerifyMergeDialog: false,
      // 合并优惠价等
      // discountMethods: "deduction",
      // deductionPrice: "",
      // input_dis: "",
      refreshKeyForServeList: Date.now(),
      refreshKeyForCardList: Date.now(),
    };
  },
  mounted() {
    //登录信息
    this.getLoginInfo();

    //首页--服务查询
    this.serviceList();

    //首页--标签接口
    // this.labelList();

    // 办卡
    this.getCardDataByPage();

    //input得到焦点
    this.inputFocus(this.$refs.search_keyword);

    //处理页面上的url
    this.processParams();

    //核销修改员工业绩
    this.modifyAchievement = global.login;
    // 修改订单跳转接收
    // this.ChangeOrdersSave();

    // 服务***开单
    // this.orderReservation();
    //会员查询
    //处理是否打印耗材小票
    this.handlePrintCost();
  },
  computed: {
    navTop() {
      return this.leftMenu.findIndex((item) => item.id == this.isactive1) ?? 0;
    },
    memberDefaultBalance() {
      let balance = 0;
      let balanceCard = this.balanceCard ? this.balanceCard : [];
      let memberInfo = this.memberInfo ? this.memberInfo : {};
      try {
        balance = memberInfo.balance;
        balanceCard.forEach((item) => {
          balance -= item["residuebalance"];
        });
      } catch (e) {
        //TODO handle the exception
        balance = 0;
      }
      return balance;
    },
    total: function () {
      let buyArr = this.C_open_order_specifications_save;
      let singlePrice = 0;
      let allMoney = 0;
      for (var i = 0; i < buyArr.length; i++) {
        if (buyArr[i].zhonglei == 3) {
          allMoney += buyArr[i].price * buyArr[i].buyNum;
        } else {
          allMoney += buyArr[i].price * buyArr[i].num;
        }
      }
      return allMoney.toFixed(2);
    },
    canSeeCostMaterial: function () {
      if (this.C_open_order_specifications_save.length > 0) {
        return true;
      } else {
        return false;
      }
    },

    // payTotal: {
    //     get: function () {
    //         let buyArr = this.C_open_order_specifications_save;
    //         let payPrice = 0;
    //         for (var i = 0; i < buyArr.length; i++) {
    //             if (buyArr[i].subtotal) {
    //                 payPrice += parseFloat(buyArr[i].subtotal);
    //             }
    //         }
    //         return payPrice > 0 ? payPrice : 0;
    //     },
    //     set: function (value) {
    //         this.payPrice = value
    //     }
    // },

    // offerPrice: function () {
    //     let offerMoney = 0;
    //     let buyArr = this.C_open_order_specifications_save;
    //     console.log(buyArr);
    //     for (var i = 0; i < buyArr.length; i++) {
    //
    //         if (buyArr[i].manualDiscount == 2 || buyArr[i].manualDiscount == 3) {
    //             console.log("计算属性 计算");
    //             if (buyArr[i].zhonglei == 3) {
    //                 offerMoney = parseFloat((buyArr[i].price * buyArr[i].buyNum) * (buyArr[i].manualDiscountCard.discount / 10)).toFixed(2)
    //             } else {
    //                 console.log("计算属性 计算");
    //                 offerMoney = parseFloat((buyArr[i].price * buyArr[i].num) * (buyArr[i].manualDiscountCard.discount / 10)).toFixed(2)
    //             }
    //         } else if (buyArr[i].manualDiscount == 4 || buyArr[i].manualDiscount == 1) {
    //             if (buyArr[i].zhonglei == 3) {
    //                 offerMoney = parseFloat(buyArr[i].price * buyArr[i].buyNum).toFixed(2)
    //             } else {
    //                 offerMoney = parseFloat(buyArr[i].price * buyArr[i].num).toFixed(2)
    //             }
    //         }
    //         buyArr[i].subtotal = offerMoney > 0 ? offerMoney : 0;
    //
    //     }
    //     return offerMoney
    // }
  },
  methods: {
    // 开单-服务的数量变化input
    handleNumInputChange: function (currentValue, zhonglei, index) {
      if (currentValue <= 0) {
        this.open_details_price_del(index);
        this.refreshKeyForServeList = Date.now();
      } else {
        //zhonglei: 1 服务 2产品 3卡项 4充值
        if (zhonglei == 1 || zhonglei == 2) {
          this.C_open_order_specifications_save[index].num = currentValue;
        } else if (zhonglei == 3) {
          // 3卡项,未见过触发
          this.C_open_order_specifications_save[index].buyNum = currentValue;
        }
        let reduceAmount = 0;
        if (this.C_open_order_specifications_save[index]?.reduceAmount > 0) {
          reduceAmount =
            this.C_open_order_specifications_save[index].reduceAmount;
        }
        // 计算小计
        this.C_open_order_specifications_save[index].subtotal = (
          this.C_open_order_specifications_save[index].price * currentValue -
          reduceAmount
        ).toFixed(2);
        this.handleUseMemberPrice(index);

        this.Pending();
      }
    },
    handleNumInputMinus: function (index) {
      const result = this.C_open_order_specifications_save[index].num - 1;
      if (result <= 0) {
        this.open_details_price_del(index);
      } else {
        this.C_open_order_specifications_save[index].num = result;
      }
    },
    handleNumInputPlus: function (index) {
      // console.log('this.C_open_order_specifications_save[index]',this.C_open_order_specifications_save[index]);
      // const result = this.C_open_order_specifications_save[index].num + 1;
      // console.log("result", result);

      // 根据card_id及服务id匹配哪个服务
      const currentItem = this.sampleMemberCardInfo.filter((item) => {
        if (
          item.cardInfo?.card_id ==
            this.C_open_order_specifications_save[index]?.cardInfo.card_id &&
          item.card_details_id ==
            this.C_open_order_specifications_save[index].card_details_id
        ) {
          return true;
        }
      })[0];
      // 当前服务有多少个
      let currentUseNum = 0;
      this.timerCardUse.forEach((item) => {
        /*  if (
          currentItem.cardInfo.once_cardtype == 3 &&
          currentItem["isgive"] == 2 &&
          b["isgive"] == 2
        ) {
          // 通卡 非赠送
          if (currentItem.cardInfo.card_id == item["consumeCardId"]) {
            // num += 1;
            currentNum = currentNum + currentItem.num;
          }
        } else {
          // 其他次卡
          if (
            currentItem.cardInfo.card_id == item["consumeCardId"] &&
            currentItem.card_details_id == item["cardDetailsId"]
          ) {
            // num += 1;
            currentNum = currentNum + currentItem.num;
          }
        } */
        if (
          currentItem.cardInfo.card_id == item["consumeCardId"] &&
          currentItem.card_details_id == item["cardDetailsId"]
        ) {
          currentUseNum = currentUseNum + item.num;
        }
      });

      if (currentItem.num == -1 || currentItem.num - currentUseNum > 0) {
        this.C_open_order_specifications_save[index].num =
          this.C_open_order_specifications_save[index].num + 1;
      } else {
        return this.$message({
          message: "可用次数不足",
          center: true,
          duration: 1500,
        });
      }
    },
    //合并选择
    handleMergeSelect: function (index) {
      this.change_ji_shi(index, true);
      this.change_xiaoshou(index, true);
      this.isShowMergeDialog = true;
    },
    //合并选择提交
    handleMergeSubmit: function () {
      this.xiaoshou_save();
      this.open_save_technician();
      this.isShowMergeDialog = false;
    },
    handleCannelMerge: function () {
      this.xiaoshou_over();
      this.open_over_technician();
      this.isShowMergeDialog = false;
    },
    //会员列表
    getMember: function () {
      var _self = this;
      _self.searchLoading = true;
      $.ajax({
        url: _self.url + "/android/Member/getMember",
        type: "POST",
        data: {
          adviser: "", // 新客归属（筛选用）
          birthday: 0, // 生日 0 全部 1 今天 2明天 3 本周 4 本月 5 下月
          count: -1, // 消费次数，-1 全部 -2 一次以内 -3 三次以内 -4 五次以内，自定义直接输入次数筛选用
          starttime: "", // 开始时间
          endtime: "", // 结束时间（生日筛选 "年月日"）
          keyword: _self.vipSearch, // 会员名称、备注名、会员编号、手机号搜索
          last_time: -1, // 消费期限，-1 全部、-2 一个月，-3 两个月，-4三个月，自定义直输入数字（筛选用，默认空）
          level_id: "", // 等级id
          maxscore: _self.memberPointsMin, // 积分范围，最大积分
          minscore: -1, // 积分范围，最小积分
          member_source: "", // 来源id
          limit: _self.searchLimit, // 分页每页条数
          page: _self.searchCurrentPage, // 分页第几页（必须）
          tab_id: "", // 标签id
          merchant_id: _self.loginInfo.merchantid, // 商户id
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.searchLoading = false;
            // _self.isFlag = false;
            _self.vip_info = res.data;
            _self.allCount = res.count;
          } else {
            _self.searchLoading = false;
            _self.$message.error({
              message: res.msg,
              duration: 1500,
            });
          }
        },
      });
    },
    //计算字符串字节长度是否超过指定长度
    isTextOverflow(text, maxLength) {
      return text?.length > maxLength;
    },
    // 控制权益选择当前已选权益
    equityUse(type, item) {
      try {
        let data = this.C_open_order_specifications_save[this.carIndex];
        if (!data) {
          return false;
        }
        if (!item) {
          if (typeof data["manualDiscount"] == "undefined") {
            data["manualDiscount"] = 1;
          }
          if (type == data["manualDiscount"]) {
            return true;
          }
        } else {
          //type为1表示次数 type为2表示折扣
          if (type == 1) {
            //
            if (data["manualDiscount"] == 3) {
              return data.manualDiscountCard.id == item["id"];
            } else {
              return false;
            }
          } else {
            if (data["manualDiscount"] == 2) {
              if (data.manualDiscountCard.cardDetailsId) {
                return data.manualDiscountCard.cardDetailsId == item["id"];
              }
              return data.manualDiscountCard.id == item["id"];
            } else {
              return false;
            }
          }
        }
      } catch (e) {
        //TODO handle the exception
        return false;
      }
    },
    // 选择协助接待员工
    chooseHelpStaff(id) {
      var _self = this;
      let myPromise = new Promise((resolve, reject) => {
        if (this.helpStaffAll.length == 0) {
          $.ajax({
            url: _self.url + "/android/Staff/sellsman",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.helpStaffAll = res.data;
                _self.helpStaffArr[_self.isactive1] = [];
                resolve();
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        } else {
          resolve();
        }
      });
      myPromise.then(() => {
        if (typeof this.helpStaffArr[this.isactive1] !== "undefined") {
          this.checkHelpStaffArr = [...this.helpStaffArr[this.isactive1]];
        } else {
          this.checkHelpStaffArr = [];
        }
        this.bindStaffId = id;
        this.helpStaffVisible = true;
      });
    },
    closeHelpStaffVisible(type) {
      // 1, 确定关闭 2，取消关闭
      if (type) {
        this.helpStaffArr[this.isactive1] = [...this.checkHelpStaffArr];
      } else {
      }
      this.checkHelpStaffArr = [];
      this.helpStaffVisible = false;
    },
    getMemberPrice(memberPriceData, levelId) {
      let memberPrice = 0;
      let memberPrice2 = 0;
      memberPriceData.forEach((item) => {
        if (levelId == item["member_level_id"]) {
          if (memberPrice > 0) {
            if (item["price"] < memberPrice) {
              memberPrice = item["price"];
            }
            if (item["price"] > memberPrice2) {
              memberPrice2 = item["price"];
            }
          } else {
            memberPrice = item["price"];
            memberPrice2 = item["price"];
          }
        }
      });
      if (memberPrice > 0) {
        if (memberPrice2 == memberPrice) {
          return "会员价 ￥" + (memberPrice / 100).toFixed(2);
        } else {
          return (
            "会员价 ￥" +
            (memberPrice / 100).toFixed(2) +
            "-" +
            (memberPrice2 / 100).toFixed(2)
          );
        }
      }
      return "";
    },
    getCardBalance(item) {
      let money = 0;
      //console.log(item,this.balanceCard);
      this.balanceCard.some((b) => {
        if (item["card_id"] == b["id"]) {
          money = b["residuebalance"];
          return true;
        }
      });
      return (money / 100).toFixed(2) + "元";
    },
    // 权益次数使用
    cardUseNum(item) {
      // console.log(item,'cardUseNum');
      // console.log(this.timerCardUse,'cardUseNum');
      let num = 0;
      this.timerCardUse.forEach((b) => {
        if (
          item["once_cardtype"] == 3 &&
          item["isgive"] == 2 &&
          b["isgive"] == 2
        ) {
          // 通卡 非赠送
          if (item["membercard_id"] == b["consumeCardId"]) {
            // num += 1;
            num = num + item.num;
          }
        } else {
          // 其他次卡
          if (
            item["membercard_id"] == b["consumeCardId"] &&
            item["id"] == b["cardDetailsId"]
          ) {
            // num += 1;
            num = num + item.num;
          }
        }
      });
      return num;
    },
    // 权益次数使用
    cardUseNum2(item, item2) {
      // console.log(item,item2,'cardUseNum2');
      let num = 0;
      this.timerCardUse.forEach((b) => {
        if (
          item2["once_cardtype"] == 3 &&
          item["givenum"] == 0 &&
          b["isgive"] == 2
        ) {
          // 通卡 非赠送
          if (item2["card_id"] == b["consumeCardId"]) {
            // num += 1;
            num = num + b.num;
          }
        } else {
          // 其他次卡  需要卡项详情id和 卡项id 同时满足条件
          if (
            item2["card_id"] == b["consumeCardId"] &&
            item["card_details_id"] == b["cardDetailsId"]
          ) {
            // num += 1;
            num = num + b.num;
          }
        }
      });
      return num;
    },
    // 耗卡使用金额
    costCardLessMoney(item) {
      let money = 0;
      this.balanceCardUse.forEach((b) => {
        if (item["membercard_id"] == b["consumeCardId"]) {
          money += b.money;
        }
      });
      return (money / 100).toFixed(2);
    },
    labelScroll(e, refName) {
      if (this.labelScrollFlag) {
        this.labelScrollFlag = false;
        let scrollLeft = this.$refs[refName].scrollLeft;
        let change = e.deltaY
          ? e.deltaY
          : e.wheelDelta
            ? e.wheelDelta
            : e.detail
              ? e.detail
              : 100;
        scrollLeft = scrollLeft + change;
        $(this.$refs[refName]).animate(
          {
            scrollLeft: scrollLeft,
          },
          () => {
            this.labelScrollFlag = true;
          }
        );
      }
    },
    deepCopy(obj) {
      var o;
      switch (typeof obj) {
        case "undefined":
          break;
        case "string":
          o = obj + "";
          break;
        case "number":
          o = obj - 0;
          break;
        case "boolean":
          o = obj;
          break;
        case "object":
          if (obj === null) {
            o = null;
          } else {
            if (obj instanceof Array) {
              o = [];
              for (var i = 0, len = obj.length; i < len; i++) {
                o.push(this.deepCopy(obj[i]));
              }
            } else {
              o = {};
              for (var k in obj) {
                o[k] = this.deepCopy(obj[k]);
              }
            }
          }
          break;
        default:
          o = obj;
          break;
      }
      return o;
    },
    // 再点一个
    buy_again(value, index) {
      // console.log(value);
      const goods = this.deepCopy(value);
      // console.log(goods);
      if (goods["zhonglei"] == 2) {
        // 产品
        if (
          goods["manualDiscount"] == 1 &&
          goods["judgeCard"] == 0 &&
          goods["isActiveCostCard"] == 1
        ) {
          // 未使用任何权益
          goods["num"]++;
          goods["subtotal"] = (goods["num"] * goods["price"]).toFixed(2);
          this.C_open_order_specifications_save.splice(index, 1, goods);
        } else {
          // 规格id  specifications_id  sku_val_id
          let goodsIndex = -1;
          // console.log(this.C_open_order_specifications_save);

          this.C_open_order_specifications_save.forEach((item, i) => {
            /*             console.log(
              item["manualDiscount"] == 1 &&
                item["judgeCard"] == 0 &&
                item["isActiveCostCard"] == 1 &&
                item["id"] == goods["id"]
            ); */
            if (
              item["manualDiscount"] == 1 &&
              item["judgeCard"] == 0 &&
              item["isActiveCostCard"] == 1 &&
              item["id"] == goods["id"]
            ) {
              if (goods["specifications_id"]) {
                if (item["specifications_id"] == goods["specifications_id"]) {
                  goodsIndex = i;
                }
              } else if (goods["sku_val_id"]) {
                if (item["sku_val_id"] == goods["sku_val_id"]) {
                  goodsIndex = i;
                }
              } else {
                goodsIndex = i;
              }
            }
          });
          // console.log(goodsIndex);
          if (goodsIndex > -1) {
            // 可以叠加
            const findGoods = JSON.parse(
              JSON.stringify(this.C_open_order_specifications_save[goodsIndex])
            );
            findGoods["num"]++;
            findGoods["subtotal"] = (
              findGoods["num"] * findGoods["price"]
            ).toFixed(2);
            this.C_open_order_specifications_save.splice(
              goodsIndex,
              1,
              findGoods
            );
          } else {
            // 需要新增  并清除权益
            goods["manualDiscount"] = 1;
            goods["judgeCard"] = 0;
            goods["isActiveCostCard"] = 1;
            goods["num"] = 1;
            goods["subtotal"] = (goods["num"] * goods["price"]).toFixed(2);
            goods.choosemembercardId = 0;
            goods.manualDiscountCard = {};
            goods.manualDiscount = 1;
            typeof goods["add_give_preferential"] !== "undefined" &&
              delete goods["add_give_preferential"];
            typeof goods["add_preferential_id"] !== "undefined" &&
              delete goods["add_preferential_id"];
            typeof goods["add_preferential_time"] !== "undefined" &&
              delete goods["add_preferential_time"];
            typeof goods["add_preferential_discount"] !== "undefined" &&
              delete goods["add_preferential_discount"];
            typeof goods["add_preferential_type"] !== "undefined" &&
              delete goods["add_preferential_type"];
            this.C_open_order_specifications_save.splice(index, 0, goods);
          }
        }
      } else {
        // 服务  永远需要新增
        if (
          goods["manualDiscount"] == 1 &&
          goods["judgeCard"] == 0 &&
          goods["isActiveCostCard"] == 1
        ) {
          // 未使用任何权益
          goods["num"] = 1;
          goods["subtotal"] = (goods["num"] * goods["price"]).toFixed(2);
          this.C_open_order_specifications_save.splice(index, 0, goods);
        } else {
          // 使用了权益
          goods["manualDiscount"] = 1;
          goods["judgeCard"] = 0;
          goods["isActiveCostCard"] = 1;
          goods["num"] = 1;
          goods["subtotal"] = (goods["num"] * goods["price"]).toFixed(2);
          goods.choosemembercardId = 0;
          goods.manualDiscountCard = {};
          goods.manualDiscount = 1;
          goods.costCard = {};
          typeof goods["add_give_preferential"] !== "undefined" &&
            delete goods["add_give_preferential"];
          typeof goods["add_preferential_id"] !== "undefined" &&
            delete goods["add_preferential_id"];
          typeof goods["add_preferential_time"] !== "undefined" &&
            delete goods["add_preferential_time"];
          typeof goods["add_preferential_discount"] !== "undefined" &&
            delete goods["add_preferential_discount"];
          typeof goods["add_preferential_type"] !== "undefined" &&
            delete goods["add_preferential_type"];
          this.C_open_order_specifications_save.splice(index, 0, goods);
        }
      }

      let num = 0;
      this.C_open_order_specifications_save.forEach((item) => {
        if (
          item["id"] == goods["id"] &&
          item["zhonglei"] == goods["zhonglei"]
        ) {
          if (goods["specifications_id"]) {
            if (item["specifications_id"] == goods["specifications_id"]) {
              num += item["num"] * 1;
            }
          } else if (goods["sku_val_id"]) {
            if (item["sku_val_id"] == goods["sku_val_id"]) {
              num += item["num"] * 1;
            }
          } else {
            num += item["num"] * 1;
          }
        }
      });
      let msg = "点单成功，当前品项数量为" + num;
      this.$message.success(msg);
      return this.Pending();
    },
    //处理是否打印耗材小票
    handlePrintCost: function () {
      /* let printCostMaterial = true;
      let defalutPrintCostMaterial = true;
      let item = localStorage.getItem("printCostMaterial");
      if (item) {
        item = JSON.parse(item);
        printCostMaterial = item.flag;
      } else {
        printCostMaterial = true;
      }
      let defaultitem = localStorage.getItem("defalutPrintCostMaterial");
      if (defaultitem) {
        defaultitem = JSON.parse(defaultitem);
        defalutPrintCostMaterial = defaultitem.flag;
      } else {
        defalutPrintCostMaterial = true;
      }
      if (printCostMaterial) {
        this.isPrintCostMaterial = 1;
      } else if (defalutPrintCostMaterial) {
        this.isPrintCostMaterial = 2;
      } else {
        this.isPrintCostMaterial = 3;
      } */
      // 强制不打印
      this.isPrintCostMaterial = 3;
    },

    //获取店铺的赠送额度
    getPresentSet: function (orderId) {
      let _self = this;
      _self.loading = true;
      $.ajax({
        url: baseUrl + "/Api/Order/getPresentSet",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderId: orderId || 0,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.surplus = res.data.surplus;
          }
        },
        error: function (error) {},
        complete: () => {
          _self.loading = false;
        },
      });
    },

    //处理页面上的url
    processParams: function () {
      var url = location.search;
      // flag是从会员详情页面跳转过来的 1: 充值   2:办卡   3:开单
      if (url && url.indexOf("&") == -1) {
        // 预约服务***开单
        this.orderReservation();
        //获取店铺的赠送额度
        this.getPresentSet(0);
      } else {
        if (url.indexOf("flag") != -1) {
          let flag = url.split("&f")[1].split("=")[1];
          //解决跳转到收银台界面是，header中的选中样式还在会员的问题
          try {
            parent.window.parentMethod();
          } catch (e) {}
          switch (flag) {
            case "1":
              let phone = url.split("&")[0].split("=")[1];
              this.memberToCharge(phone);
              break;
            case "2":
              let data = {};
              data.phone = url.split("&")[0].split("=")[1];
              data.name = url.split("&")[1].split("=")[1];
              this.memberToCard(data);
              break;
            case "3":
              let telephone = url.split("&")[0].split("=")[1];
              this.memberToReceipt(telephone);
              break;
          }
          //获取店铺的赠送额度
          this.getPresentSet(0);
        } else {
          // 修改订单跳转接收
          this.ChangeOrdersSave();
        }
      }
    },
    //      {word: "开单", id: '0'},
    //      {word: "充卡", id: '4'},
    //      {word: "办卡", id: '1'},
    //      {word: "充值", id: '2'},
    //      {word: "直接收款", id: '3'}

    //会员详情跳转到充值
    memberToCharge: function (data) {
      this.getmenu(2);
      this.cz_search_keyword = data;
      this.bindInquire(data);
      // this.getVipRechargeData();
    },

    //会员详情跳转到办卡
    memberToCard: function (data) {
      this.getmenu(1);
      this.bindInquire(data.phone);
      this.memberObj.phone = data.phone;
    },

    //会员详情跳转到开单
    memberToReceipt: function (data) {
      this.getmenu(0);
      this.bindInquire(data);
    },

    //获取buy_receipt
    getBuyReceipt: function () {
      let reg = /buy_receipt/g;
      let url = location.search;
      let result = url.match(reg);
      if (result) {
        let orderNo = url.split("&")[0].toString().split("=")[1].toString();
        this.orderNo = orderNo;
        this.buy_receipt = true;
      }
    },

    //修改订单后跳转收银台页面修改服务
    ChangeOrdersSave: function () {
      var _self = this;
      // 修改导航栏状态
      try {
        parent.window.parentMethod();
      } catch (e) {
        //console.log(e);
      }

      var url = location.search;
      var theRequest = new Object();
      if (url.indexOf("?") != -1) {
        var str = url.substr(1);
        var strs = str.split("&");
        for (var i = 0; i < strs.length; i++) {
          theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
        }
      }
      var theRequestNull = JSON.stringify(theRequest) == "{}";
      _self.orderCount = theRequest.orderNO;
      if (theRequestNull) {
        //console.log('没有传值');
        this.getPresentSet(0);
      } else {
        if (theRequest.hasOwnProperty("vip_id")) {
          if (theRequest["vip_id"] == 0) {
            //console.log('接受到的值', theRequest);
            var orderNo = theRequest.orderNO;
            let promise = new Promise(function (resolve, reject) {
              $.ajax({
                url: _self.url + "/android/order/fetchOrderDetail",
                type: "post",
                data: {
                  merchantid: _self.loginInfo.merchantid,
                  orderNo: orderNo,
                  storeid: _self.loginInfo.storeid,
                },
                success: function (res) {
                  // var res = JSON.parse(res);
                  if (res.code == 1) {
                    //console.log('uuai', res);
                    // _self.fetchOrderDetailData = res.data;
                    // res1 = res.data;
                    // result1 = res.data;
                    //window.parent.location.reload()

                    resolve(res);
                  }
                },
                error: function (error) {
                  reject(error);
                },
              });
            });

            promise.then(function (res) {
              var result1 = res.data;
              _self.displayOrderOrdinary(result1);
              _self.Pending();
            });
          } else {
            //console.log('接受到的值', theRequest);
            var orderNo = theRequest.orderNO;
            _self.member_id = theRequest.vip_id;
            // console.log('测试为什么报错', _self.loginInfo.merchantid, orderNo, _self.loginInfo.storeid);
            let promise = new Promise(function (resolve, reject) {
              $.ajax({
                url: _self.url + "/api/Order/android/order/fetchOrderDetail",
                type: "post",
                data: {
                  merchantid: _self.loginInfo.merchantid,
                  orderNo: orderNo,
                  storeid: _self.loginInfo.storeid,
                },
                success: function (res) {
                  // var res = JSON.parse(res);
                  if (res.code == 1) {
                    //console.log('开始走着uuai');
                    _self.fetchOrderDetailData = res.data;
                    // _self.Pending()
                    // res1 = res.data;
                    // console.log(res);
                    resolve(res);
                  }
                },
                error: function (error) {
                  //console.log(error);
                  reject(error);
                },
              });
            });
            promise
              .then(function (res) {
                // console.log("接口走了没？？？？？？？？？？？888888888888888");
                //console.log(res.data);
                var result2 = res.data["buyer"]["phone"];
                return $.ajax({
                  url: _self.url + "/android/vip/memberSearch",
                  type: "post",
                  data: {
                    keyword: result2,
                    merchantid: _self.loginInfo.merchantid,
                    storeid: _self.loginInfo.storeid,
                  },
                  success: function (res) {
                    // var res = JSON.parse(res);
                    //定义的变量赋值，给
                    //console.log('会员信息', res);
                    if (res.code == 1 && res.data.length != "") {
                      //console.log('现在测有没有给memberInfo赋值');
                      _self.memberInfo = res.data[0];
                      // _self.is_dynamic = true;
                      // _self.getBalanceCard();
                      // _self.getMemberStampCardInfo();
                      // _self.billingType = 2;
                    } else {
                      _self.$message.error({
                        message: "未找到此会员信息",
                        duration: 1500,
                      });
                    }
                  },
                  error: function (error) {},
                });
              })
              .then(function (res1) {
                //console.log(res1);
                // console.log('uuai', res1);
                // 调用ifname 父页面的方法
                // window.parent.location.href='header.html';
                return $.ajax({
                  url: _self.url + "/android/vip/getBalanceCard",
                  type: "post",
                  data: {
                    storeid: _self.loginInfo.storeid,
                    merchantid: _self.loginInfo.merchantid,
                    memberid: _self.memberInfo.id, //会员id
                  },
                  success: function (res) {
                    // var res = JSON.parse(res);
                    if (res.code == 1) {
                      //console.log('1234', res);
                      _self.is_dynamic = true;
                      _self.loading = false;
                      _self.balanceCard = res.data;
                    }
                  },
                  error: function (error) {
                    _self.loading = false;
                  },
                });
              })
              .then(function () {
                _self.displayOrder();
              });
          }
        }
      }
    },

    //展示订单信息有会员；
    displayOrder: function () {
      var _self = this;
      var data = _self.fetchOrderDetailData;

      //获取店铺的赠送额度
      this.getPresentSet(data.id);

      var arr = []; //赋值给所有服务数组的临时数组
      //为了求出_self.changeOrderinfos结果定义的变量
      var Array = []; //记录出现服务的id  重复的多次记录
      var onlyArray = []; //记录出现服务的id  重复的记录一次

      //获取现金券
      if (data.member_coupon != 0 && data.member_counpon_money != 0) {
        let couponArr = function () {
          return $.ajax({
            url: _self.url + "/android/Member/getCouponInfo",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid, // 商户id
              storeid: _self.loginInfo.storeid, // 商户id
              // "type": req.body.type,              // 1 未使用 2已使用 3已过期，在会员详情中必须传，会员开单选择时不传此字段
              limit: 20, // 分页 每页几条
              page: 1, // 分页 第几页
              uid: data.buyer.id, // 用户id
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 0) {
                _self.couponInfo = res.data;
              } else {
                _self.$message({
                  type: "warning",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            error: function (err) {},
            complete: () => {
              _self.loading = false;
            },
          });
        };
        _self.loading = true;
        couponArr().done((res) => {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.couponInfo = res.data;
            //获取现金券信息
            _self.couponInfo.forEach((item) => {
              if (item.id == data.member_coupon) {
                _self.C_open_order_specifications_save.couponCard = {};
                _self.isCouponCard = true;
                _self.C_open_order_specifications_save.judgeCard = 3;
                _self.C_open_order_specifications_save.couponCard = item;
                _self.C_open_order_specifications_save.couponCard.coupon_name =
                  item.coupon_name;
                _self.Pending();
              }
            });
          }
        });
      }

      for (let i = 0; i < data["orderDetailsData"].length; i++) {
        Array.push(data["orderDetailsData"][i]["goodsId"]);
      }
      for (let i = 0; i < Array.length; i++) {
        if (onlyArray.indexOf(Array[i]) == -1) {
          onlyArray.push(Array[i]);
        }
      }
      var times;
      for (let i = 0; i < onlyArray.length; i++) {
        times = 0;
        for (let j = 0; j < data["orderDetailsData"].length; j++) {
          if (onlyArray[i] == data["orderDetailsData"][j]["goodsId"]) {
            data["orderDetailsData"][j]["showtimes"] = times;
            times++;
            data["orderDetailsData"][j]["showtimes"] = times;
          }
        }
      }
      for (let i = 0; i < data["orderDetailsData"].length; i++) {
        let Obj = {};
        Obj["manualDiscountCard"] = {};
        Obj["manualDiscountCard"]["cardName"] = "";
        Obj["manualDiscountCard"]["membercard_id"] = "";
        Obj["manualDiscountCard"]["id"] = "";
        let technician_name = "";
        let saler_name = "";
        let salesid = [];
        //console.log(data['orderDetailsData'][i]['equity_type'])
        //equity_type 1 无权益 2折扣 3抵扣 4手动改价
        if (data["orderDetailsData"][i]["equity_type"] == 1) {
          //console.log("走了没？？？？？？？？？？？？？-----------44444444444");
          //不选择优惠权益
          if (data["orderDetailsData"][i].technicians.length > 0) {
            for (
              var j = 0;
              j < data["orderDetailsData"][i].technicians.length;
              j++
            ) {
              if (j + 1 == data["orderDetailsData"][i].technicians.length) {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname;
              } else {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname + ", ";
              }
            }
          }
          if (data["orderDetailsData"][i].sellsname.length > 0) {
            for (
              var k = 0;
              k < data["orderDetailsData"][i].sellsname.length;
              k++
            ) {
              if (k + 1 == data["orderDetailsData"][i].sellsname.length) {
                saler_name += data["orderDetailsData"][i].sellsname[k].nickname;
              } else {
                saler_name +=
                  data["orderDetailsData"][i].sellsname[k].nickname + ", ";
              }
              salesid.push(data["orderDetailsData"][i]["sellsname"][k]["id"]);
            }
          }
          //console.log('销售id', data['orderDetailsData'][i]['sellsname']);
          //对接服务人员
          //console.log('这是赋值服务人员时候的服务人员name', technician_name);
          Obj["technician_name"] = technician_name;
          Obj["technician_id"] = data["orderDetailsData"][i]["technicians"];
          Obj["is_open_product_jishi"] =
            data["orderDetailsData"][i]["type"] == 2 ? true : false;
          //对接销售
          Obj["salesmen"] = data["orderDetailsData"][i]["sells_id"];
          Obj["saler_name"] = saler_name;
          //itemId
          Obj["itemId"] = data["orderDetailsData"][i]["itemId"];
          Obj["itemImgId"] = data["orderDetailsData"][i]["itemImgId"];
          //对接数量和价格
          Obj["num"] = data["orderDetailsData"][i]["num"];
          Obj["price"] = data["orderDetailsData"][i]["price"] / 100;
          Obj["member_price"] =
            data["orderDetailsData"][i]["member_price"] / 100;
          //对接规格相关数据
          Obj["sku"] = data["orderDetailsData"][i]["sku_name"];
          //服务对象的名称和id此处有俩个id
          Obj["service_name"] = data["orderDetailsData"][i]["name"];
          Obj["product_id"] = data["orderDetailsData"][i]["couponGoodsId"];
          Obj["goodsId"] = data["orderDetailsData"][i]["goodsId"];
          Obj["id"] = data["orderDetailsData"][i]["goodsId"];
          //判断改对象是服务还是产品;
          Obj["zhonglei"] = data["orderDetailsData"][i]["type"];
          //权益类型
          //权益相关
          Obj["manualDiscount"] = data["orderDetailsData"][i]["equity_type"];
          Obj["manualDiscountCard"]["cardName"] =
            data["orderDetailsData"][i]["cardName"];
          Obj["manualDiscountCard"]["membercard_id"] =
            data["orderDetailsData"][i]["card_id"];

          //判断是不是耗卡  1：不是    2：是
          if (data["orderDetailsData"][i].consumeCard == 1) {
            if (data["orderDetailsData"][i].cardName == "会员等级折扣") {
              Obj["isActiveCostCard"] = 1;
              Obj["judgeCard"] = 0;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] = "不使用耗卡";
            } else {
              Obj["isActiveCostCard"] = 1;
              Obj["judgeCard"] = 0;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] = "不使用耗卡";
            }
          } else {
            Obj["isActiveCostCard"] = 2;
            Obj["judgeCard"] = 2;
            Obj["costCard"] = {};
            Obj["costCard"]["card_info"] =
              data["orderDetailsData"][i]["consumeCardName"];
            Obj["manualDiscountCard"]["discount"] =
              data["orderDetailsData"][i]["discount"];
            Obj["costCard"]["manualDiscount"] = 2;
          }
          //总的价格
          Obj["subtotal"] = (
            data["orderDetailsData"][i]["smallTotal"] / 100
          ).toFixed(2);
          if (data["orderDetailsData"][i].promoterId) {
            Obj["promoterId"] = data["orderDetailsData"][i].promoterId;
          } else {
            Obj["promoterId"] = 0;
          }
          // console.log(data)
          //arr.push(Obj);
          _self.C_open_order_specifications_save.push(Obj);
          // if (data.net_receipts) {
          //     // console.log("预约付定金预约付定金")
          //     _self.payTotal = data.toBePay / 100;
          // } else {
          //     _self.Pending();
          // }
        }
        //equity_type 1 无权益 2折扣 3抵扣 4手动改价
        else if (data["orderDetailsData"][i]["equity_type"] == 2) {
          if (data["orderDetailsData"][i].technicians.length > 0) {
            for (
              var j = 0;
              j < data["orderDetailsData"][i].technicians.length;
              j++
            ) {
              if (j + 1 == data["orderDetailsData"][i].technicians.length) {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname;
              } else {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname + ", ";
              }
            }
          }
          if (data["orderDetailsData"][i].sellsname.length > 0) {
            for (
              var k = 0;
              k < data["orderDetailsData"][i].sellsname.length;
              k++
            ) {
              if (k + 1 == data["orderDetailsData"][i].sellsname.length) {
                saler_name += data["orderDetailsData"][i].sellsname[k].nickname;
              } else {
                saler_name +=
                  data["orderDetailsData"][i].sellsname[k].nickname + ", ";
              }
              salesid.push(data["orderDetailsData"][i]["sellsname"][k]["id"]);
            }
          }
          //console.log('销售id', data['orderDetailsData'][i]['sellsname']);
          //对接服务人员
          Obj["technician_name"] = technician_name;
          Obj["technician_id"] = data["orderDetailsData"][i]["technicians"];
          Obj["is_open_product_jishi"] =
            data["orderDetailsData"][i]["type"] == 2 ? true : false;
          //对接销售
          Obj["salesmen"] = data["orderDetailsData"][i]["sells_id"];
          Obj["saler_name"] = saler_name;
          //itemId
          Obj["itemId"] = data["orderDetailsData"][i]["itemId"];
          Obj["itemImgId"] = data["orderDetailsData"][i]["itemImgId"];
          //对接数量和价格
          Obj["num"] = data["orderDetailsData"][i]["num"];
          Obj["price"] = data["orderDetailsData"][i]["price"] / 100;
          Obj["member_price"] =
            data["orderDetailsData"][i]["member_price"] / 100;
          //对接规格相关数据
          Obj["sku"] = data["orderDetailsData"][i]["sku_name"];
          //服务对象的名称和id此处有俩个id
          Obj["service_name"] = data["orderDetailsData"][i]["name"];
          Obj["product_id"] = data["orderDetailsData"][i]["couponGoodsId"];
          Obj["goodsId"] = data["orderDetailsData"][i]["goodsId"];
          Obj["id"] = data["orderDetailsData"][i]["goodsId"];
          //判断改对象是服务还是产品;
          Obj["zhonglei"] = data["orderDetailsData"][i]["type"];
          //总的价格
          Obj["subtotal"] = data["orderDetailsData"][i]["price"];
          //权益相关
          Obj["manualDiscount"] = data["orderDetailsData"][i]["equity_type"];
          Obj["card_id"] = data["orderDetailsData"][i]["card_id"];
          //需加字段
          Obj["manualDiscountCard"]["cardName"] =
            data["orderDetailsData"][i]["cardName"];
          Obj["manualDiscountCard"]["membercard_id"] =
            data["orderDetailsData"][i]["card_id"];
          Obj["manualDiscountCard"]["id"] =
            data["orderDetailsData"][i]["card_details_id"];
          Obj["manualDiscountCard"]["discount"] =
            data["orderDetailsData"][i]["discount"];
          //console.log("跳转到收银台开单的折扣卡数据")
          //console.log(data)

          //耗卡信息加进去
          //判断是不是耗卡  1：不是    2：是
          if (data["orderDetailsData"][i].consumeCard == 1) {
            if (data["orderDetailsData"][i].cardName == "会员等级折扣") {
              Obj["isActiveCostCard"] = 1;
              Obj["judgeCard"] = 0;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] = "不使用耗卡";
            } else {
              Obj["isActiveCostCard"] = 1;
              Obj["judgeCard"] = 1;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] =
                data["orderDetailsData"][i]["consumeCardName"];
              Obj["costCard"]["membercard_id"] =
                data["orderDetailsData"][i]["consumeCardId"];
            }
          } else {
            Obj["isActiveCostCard"] = 2;
            Obj["judgeCard"] = 2;
            Obj["costCard"] = {};
            Obj["costCard"]["card_info"] =
              data["orderDetailsData"][i]["consumeCardName"];
            Obj["costCard"]["membercard_id"] =
              data["orderDetailsData"][i]["consumeCardId"];
            Obj["manualDiscountCard"]["discount"] =
              data["orderDetailsData"][i]["discount"];
            Obj["costCard"]["manualDiscount"] = 2;
          }
          //额外必需字段
          Obj["add_give_preferential"] = true;
          Obj["add_preferential_id"] = data["orderDetailsData"][i]["card_id"];
          Obj["add_preferential_type"] = 2;
          Obj["add_preferential_discount"] =
            data["orderDetailsData"][i]["smallTotal"];
          //小计
          //console.log("dadadadadadasdasdasdsadasdsadsadsadsa")
          //console.log(Number(data['orderDetailsData'][i]['discount'] / 10).toFixed(3))
          Obj["subtotal"] = (
            data["orderDetailsData"][i]["smallTotal"] / 100
          ).toFixed(2);
          if (data["orderDetailsData"][i].promoterId) {
            Obj["promoterId"] = data["orderDetailsData"][i].promoterId;
          } else {
            Obj["promoterId"] = 0;
          }
          _self.C_open_order_specifications_save.push(Obj);
          /*// Obj['manualDiscount'] = 1;
					// Obj['manualDiscountCard']['cardName'] = '';
					// Obj['manualDiscountCard']['membercard_id'] = 0;
					// Obj['manualDiscountCard']['discount']='10.0';
					if (data['orderDetailsData'][i]['showtimes'] == 1) {
					    //console.log("走了没？？？？？？？？？？？？？----------222222222");
					    var chioced_id = data['orderDetailsData'][i]['goodsId'];
					    // $.ajax({
					    //     url: _self.url + "/android/vip/getMSEquityDetail",
					    //     async: false,
					    //     type: "post",
					    //     data: {
					    //         storeid: _self.loginInfo.storeid,
					    //         merchantid: _self.loginInfo.merchantid,
					    //         memberid: _self.member_id, //会员id
					    //         serviceid: chioced_id,
					    //         type: 1
					    //     },
					    //     success: function (res) {
					    //         // var res = JSON.parse(res);
					    //         Obj['showtimes'] = data['orderDetailsData'][i]['showtimes'];
					    //         for (let j = 0; j < _self.balanceCard.length; j++) {
					    //             if (_self.balanceCard['id'] == data['orderDetailsData'][i]['card_id']) {
					    //                 if (_self.balanceCard['residuebalance'] >= data['orderDetailsData'][i]['smallTotal']) {
					    //                     _self.balanceCard['residuebalance'] -= data['orderDetailsData'][i]['smallTotal'];
					    //                     //额外必需字段
					    //                     Obj['add_give_preferential'] = true;
					    //                     Obj['add_preferential_id'] = data['orderDetailsData'][i]['card_id'];
					    //                     Obj['add_preferential_type'] = 2;
					    //                     Obj['add_preferential_discount'] = data['orderDetailsData'][i]['smallTotal'];
					    //                     //需加字段
					    //                     Obj['manualDiscountCard']['cardName'] = data['orderDetailsData'][i]['cardName'];
					    //                     Obj['manualDiscountCard']['membercard_id'] = data['orderDetailsData'][i]['card_id'];
					    //                     Obj['manualDiscountCard']['id'] = data['orderDetailsData'][i]['card_details_id'];
					    //                     Obj['manualDiscountCard']['discount'] = data['orderDetailsData'][i]['discount'];
					    //                     //小计
					    //                     Obj['subtotal'] = (data['orderDetailsData'][i]['smallTotal'] / 100 * data['orderDetailsData'][i]['num']).toFixed(2);
					    //                 } else {
					    //                     Obj['failfont'] = '充值卡内余额不足，请重新选择';
					    //                 }
					    //             }
					    //         }
					    //         Obj['give_preferential'] = res.data;
					    //         arr.push(Obj);
					    //         _self.C_open_order_specifications_save.push(Obj);
					    //
					    //     },
					    //     error: function (error) {
					    //         _self.loading = false;
					    //         console.log(error);
					    //     }
					    // });
					    _self.C_open_order_specifications_save.push(Obj);
					    _self.Pending();

					} else {

					    // 不是第一次出现该数据 找到第一次出现该对象的那个吧对应的数据的次数减一
					    for (let j = 0; j < _self.balanceCard.length; j++) {
					        if (_self.balanceCard['id'] == data['orderDetailsData'][i]['card_id']) {
					            if (_self.balanceCard['residuebalance'] >= data['orderDetailsData'][i]['smallTotal']) {
					                _self.balanceCard['residuebalance'] -= data['orderDetailsData'][i]['smallTotal'];
					                //额外必需字段
					                Obj['add_give_preferential'] = true;
					                Obj['add_preferential_id'] = data['orderDetailsData'][i]['card_id'];
					                Obj['add_preferential_type'] = 2;
					                Obj['add_preferential_discount'] = data['orderDetailsData'][i]['smallTotal'];
					                //需加字段
					                Obj['manualDiscountCard']['cardName'] = data['orderDetailsData'][i]['cardName'];
					                Obj['manualDiscountCard']['membercard_id'] = data['orderDetailsData'][i]['card_id'];
					                Obj['manualDiscountCard']['id'] = data['orderDetailsData'][i]['card_details_id'];
					                Obj['manualDiscountCard']['discount'] = data['orderDetailsData'][i]['discount'];
					                //小计
					                Obj['subtotal'] = (data['orderDetailsData'][i]['smallTotal'] / 100).toFixed(2);
					            } else {
					                Obj['failfont'] = '充值卡内余额不足，请重新选择';
					            }
					        }
					    }
					    Obj['showtimes'] = data['orderDetailsData'][i]['showtimes'];
					    arr.push(Obj);
					    _self.C_open_order_specifications_save.push(Obj);
					}*/
        }
        //equity_type 1 无权益 2折扣 3抵扣 4手动改价
        else if (data["orderDetailsData"][i]["equity_type"] == 3) {
          //console.log('检测顺序', data['orderDetailsData'][i]['showtimes']);
          if (data["orderDetailsData"][i].technicians.length > 0) {
            for (
              var j = 0;
              j < data["orderDetailsData"][i].technicians.length;
              j++
            ) {
              if (j + 1 == data["orderDetailsData"][i].technicians.length) {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname;
              } else {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname + ", ";
              }
            }
          }

          if (data["orderDetailsData"][i].sellsname.length > 0) {
            //console.log("走了没？？？？？？？？？？？？？22222222");
            for (
              var k = 0;
              k < data["orderDetailsData"][i].sellsname.length;
              k++
            ) {
              if (k + 1 == data["orderDetailsData"][i].sellsname.length) {
                saler_name += data["orderDetailsData"][i].sellsname[k].nickname;
              } else {
                saler_name +=
                  data["orderDetailsData"][i].sellsname[k].nickname + ", ";
              }
              salesid.push(data["orderDetailsData"][i]["sellsname"][k]["id"]);
            }
          }
          // var newObj=_self.changeDealonce(Obj,data['orderDetailsData'][i],i);
          //对接服务人员
          Obj["technician_name"] = technician_name;
          Obj["technician_id"] = data["orderDetailsData"][i]["technicians"];
          Obj["is_open_product_jishi"] =
            data["orderDetailsData"][i]["type"] == 2 ? true : false;
          //对接销售
          Obj["salesmen"] = data["orderDetailsData"][i]["sells_id"];
          Obj["saler_name"] = saler_name;
          //itemId
          Obj["itemId"] = data["orderDetailsData"][i]["itemId"];
          Obj["itemImgId"] = data["orderDetailsData"][i]["itemImgId"];
          //对接数量和价格
          Obj["num"] = data["orderDetailsData"][i]["num"];
          Obj["price"] = data["orderDetailsData"][i]["price"] / 100;
          Obj["member_price"] =
            data["orderDetailsData"][i]["member_price"] / 100;
          //对接规格相关数据
          Obj["sku"] = data["orderDetailsData"][i]["sku_name"];
          //服务对象的名称和id此处有俩个id
          Obj["service_name"] = data["orderDetailsData"][i]["name"];
          Obj["product_id"] = data["orderDetailsData"][i]["couponGoodsId"];
          Obj["goodsId"] = data["orderDetailsData"][i]["goodsId"];
          Obj["id"] = data["orderDetailsData"][i]["goodsId"];
          //判断改对象是服务还是产品;
          Obj["zhonglei"] = data["orderDetailsData"][i]["type"];
          //总的价格
          Obj["subtotal"] = data["orderDetailsData"][i]["price"];
          //权益相关
          Obj["manualDiscount"] = 1;
          Obj["manualDiscountCard"]["cardName"] = "";
          Obj["manualDiscountCard"]["membercard_id"] = 0;
          // Obj['manualDiscountCard']['id'] = data['orderDetailsData'][i]['card_details_id'];

          //额外必需字段
          Obj["add_give_preferential"] = true;
          Obj["add_preferential_id"] = data["orderDetailsData"][i]["card_id"];
          Obj["add_preferential_type"] = 1;
          Obj["add_preferential_time"] = 1;

          //耗卡信息加进去
          Obj["isActiveCostCard"] = 1;
          Obj["judgeCard"] = 1;
          Obj["costCard"] = {};
          Obj["costCard"]["card_info"] = "不使用耗卡";

          //需加字段
          Obj["manualDiscount"] = data["orderDetailsData"][i]["equity_type"];
          Obj["manualDiscountCard"]["cardName"] =
            data["orderDetailsData"][i]["cardName"];
          Obj["manualDiscountCard"]["membercard_id"] =
            data["orderDetailsData"][i]["card_id"];
          Obj["manualDiscountCard"]["id"] =
            data["orderDetailsData"][i]["card_details_id"];
          //小计
          Obj["subtotal"] = (
            data["orderDetailsData"][i]["smallTotal"] / 100
          ).toFixed(2);
          if (data["orderDetailsData"][i].promoterId) {
            Obj["promoterId"] = data["orderDetailsData"][i].promoterId;
          } else {
            Obj["promoterId"] = 0;
          }
          _self.C_open_order_specifications_save.push(Obj);
          /*//判断该服务在数组中第几次出现
					// console.log('判断该服务在数组中是不是第' + i + '次出现', data['orderDetailsData'][i]['showtimes'])
					if (data['orderDetailsData'][i]['showtimes'] == 1) {

					    var chioced_id = data['orderDetailsData'][i]['goodsId'];
					    // $.ajax({
					    //     url: _self.url + "/android/vip/getMSEquityDetail",
					    //     // async: false,
					    //     type: "post",
					    //     data: {
					    //         storeid: _self.loginInfo.storeid,
					    //         merchantid: _self.loginInfo.merchantid,
					    //         memberid: _self.member_id, //会员id
					    //         serviceid: chioced_id,
					    //         type: 1
					    //     },
					    //     success: function (res) {
					    //         console.log("接口成功了没？？？？？？？？？？？？？");
					    //         // var res = JSON.parse(res);
					    //         Obj['showtimes'] = data['orderDetailsData'][i]['showtimes'];
					    //         for (let j = 0; j < res.data['once'].length; j++) {
					    //             console.log('次数检测', res.data['once'][j]['maxnum']);
					    //             if (res.data['once'][j]['membercard_id'] == data['orderDetailsData'][i]['card_id']) {
					    //                 if (res.data['once'][j]['maxnum'] > 0) {
					    //                     console.log('现在看次数大于0吗');
					    //                     res.data['once'][j]['maxnum']--;
					    //                     //额外必需字段
					    //                     Obj['add_give_preferential'] = true;
					    //                     Obj['add_preferential_id'] = data['orderDetailsData'][i]['card_id'];
					    //                     Obj['add_preferential_type'] = 1;
					    //                     Obj['add_preferential_time'] = 1;
					    //                     //需加字段
					    //                     Obj['manualDiscount'] = data['orderDetailsData'][i]['equity_type'];
					    //                     Obj['manualDiscountCard']['cardName'] = data['orderDetailsData'][i]['cardName'];
					    //                     Obj['manualDiscountCard']['membercard_id'] = data['orderDetailsData'][i]['card_id'];
					    //                     Obj['manualDiscountCard']['id'] = data['orderDetailsData'][i]['card_details_id'];
					    //                     //小计
					    //                     Obj['subtotal'] = (data['orderDetailsData'][i]['smallTotal'] / 100 * data['orderDetailsData'][i]['num']).toFixed(2);
					    //                     break;
					    //                 } else if (res.data['once'][j]['maxnum'] == 0) {
					    //                     console.log('走了一次');
					    //                     Obj['failfont'] = '所选次卡的权益抵扣次数不足，请重新选择';
					    //                 } else {
					    //                     //当前的次卡次数为-1 表示是无限次卡
					    //                     //额外必需字段
					    //                     Obj['add_give_preferential'] = true;
					    //                     Obj['add_preferential_id'] = data['orderDetailsData'][i]['card_id'];
					    //                     Obj['add_preferential_type'] = 1;
					    //                     Obj['add_preferential_time'] = 1;
					    //                     //需加字段
					    //                     Obj['manualDiscount'] = data['orderDetailsData'][i]['equity_type'];
					    //                     Obj['manualDiscountCard']['cardName'] = data['orderDetailsData'][i]['cardName'];
					    //                     Obj['manualDiscountCard']['membercard_id'] = data['orderDetailsData'][i]['card_id'];
					    //                     Obj['manualDiscountCard']['id'] = data['orderDetailsData'][i]['card_details_id'];
					    //                     //小计
					    //                     Obj['subtotal'] = (data['orderDetailsData'][i]['smallTotal'] / 100 * data['orderDetailsData'][i]['num']).toFixed(2);
					    //                     break;
					    //                 }
					    //             }
					    //         }
					    //         Obj['give_preferential'] = res.data;
					    //         arr.push(Obj);
					    //         console.log('push完第一次', arr);
					    //         _self.C_open_order_specifications_save.push(Obj);
					    //     },
					    //     error: function (error) {
					    //         _self.loading = false;
					    //         console.log(error);
					    //     }
					    // });
					    _self.C_open_order_specifications_save.push(Obj);
					    _self.Pending();
					} else {
					    for (let k = 0; k < arr.length; k++) {
					        if (arr[k]['showtimes'] == 1 && arr[k]['id'] == data['orderDetailsData'][i]['goodsId']) {
					            for (let j = 0; j < arr[k]['give_preferential']['once'].length; j++) {
					                if (data['orderDetailsData'][i]['card_id'] == arr[k]['give_preferential']['once'][j]['membercard_id']) {
					                    if (arr[k]['give_preferential']['once'][j]['maxnum'] > 0) {
					                        arr[k]['give_preferential']['once'][j]['maxnum']--;
					                        //额外必需字段
					                        Obj['add_give_preferential'] = true;
					                        Obj['add_preferential_id'] = data['orderDetailsData'][i]['card_id'];
					                        Obj['add_preferential_type'] = 1;
					                        Obj['add_preferential_time'] = 1;
					                        //需加字段
					                        Obj['manualDiscount'] = data['orderDetailsData'][i]['equity_type'];
					                        Obj['manualDiscountCard']['cardName'] = data['orderDetailsData'][i]['cardName'];
					                        Obj['manualDiscountCard']['membercard_id'] = data['orderDetailsData'][i]['card_id'];
					                        Obj['manualDiscountCard']['id'] = data['orderDetailsData'][i]['card_details_id'];
					                        //小计
					                        Obj['subtotal'] = (data['orderDetailsData'][i]['smallTotal'] / 100).toFixed(2);
					                        break;
					                    } else if (arr[k]['give_preferential']['once'][j]['maxnum'] == 0) {
					                        Obj['failfont'] = '所选次卡的权益抵扣次数不足，请重新选择';
					                    } else {
					                        Obj['add_give_preferential'] = true;
					                        Obj['add_preferential_id'] = data['orderDetailsData'][i]['card_id'];
					                        Obj['add_preferential_type'] = 1;
					                        Obj['add_preferential_time'] = 1;
					                        //需加字段
					                        Obj['manualDiscount'] = data['orderDetailsData'][i]['equity_type'];
					                        Obj['manualDiscountCard']['cardName'] = data['orderDetailsData'][i]['cardName'];
					                        Obj['manualDiscountCard']['membercard_id'] = data['orderDetailsData'][i]['card_id'];
					                        Obj['manualDiscountCard']['id'] = data['orderDetailsData'][i]['card_details_id'];
					                        //小计
					                        Obj['subtotal'] = (data['orderDetailsData'][i]['smallTotal'] / 100).toFixed(2);
					                        break;
					                    }
					                }
					            }
					        }
					    }
					    Obj['showtimes'] = data['orderDetailsData'][i]['showtimes'];
					    arr.push(Obj);

					    _self.C_open_order_specifications_save.push(Obj);
					    _self.Pending();
					    Obj = {};
					}*/
        }
        //equity_type 1 无权益 2折扣 3抵扣 4手动改价
        else {
          //优惠金额
          if (data["orderDetailsData"][i].technicians.length > 0) {
            for (
              var j = 0;
              j < data["orderDetailsData"][i].technicians.length;
              j++
            ) {
              if (j + 1 == data["orderDetailsData"][i].technicians.length) {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname;
              } else {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname + ", ";
              }
            }
          }
          if (data["orderDetailsData"][i].sellsname.length > 0) {
            for (
              var k = 0;
              k < data["orderDetailsData"][i].sellsname.length;
              k++
            ) {
              if (k + 1 == data["orderDetailsData"][i].sellsname.length) {
                saler_name += data["orderDetailsData"][i].sellsname[k].nickname;
              } else {
                saler_name +=
                  data["orderDetailsData"][i].sellsname[k].nickname + ", ";
              }
              salesid.push(data["orderDetailsData"][i]["sellsname"][k]["id"]);
            }
          }
          //对接服务人员
          //console.log('销售id', data['orderDetailsData'][i]['sellsname']);
          Obj["technician_name"] = technician_name;
          Obj["technician_id"] = data["orderDetailsData"][i]["technicians"];
          Obj["is_open_product_jishi"] =
            data["orderDetailsData"][i]["type"] == 2 ? true : false;
          //对接销售
          Obj["salesmen"] = data["orderDetailsData"][i]["sells_id"];
          Obj["saler_name"] = saler_name;
          //itemId
          Obj["itemId"] = data["orderDetailsData"][i]["itemId"];
          Obj["itemImgId"] = data["orderDetailsData"][i]["itemImgId"];
          //对接数量和价格
          Obj["num"] = data["orderDetailsData"][i]["num"];
          Obj["price"] = data["orderDetailsData"][i]["price"] / 100;
          Obj["member_price"] =
            data["orderDetailsData"][i]["member_price"] / 100;
          //对接规格相关数据
          Obj["sku"] = data["orderDetailsData"][i]["sku_name"];
          //服务对象的名称和id此处有俩个id
          Obj["service_name"] = data["orderDetailsData"][i]["name"];
          Obj["product_id"] = data["orderDetailsData"][i]["couponGoodsId"];
          Obj["goodsId"] = data["orderDetailsData"][i]["goodsId"];
          Obj["id"] = data["orderDetailsData"][i]["goodsId"];
          //判断改对象是服务还是产品;
          //权益相关
          Obj["manualDiscount"] = data["orderDetailsData"][i]["equity_type"];
          Obj["manualDiscountCard"]["cardName"] =
            data["orderDetailsData"][i]["cardName"];
          Obj["manualDiscountCard"]["membercard_id"] =
            data["orderDetailsData"][i]["card_id"];
          Obj["zhonglei"] = data["orderDetailsData"][i]["type"];

          //耗卡信息加进去
          //判断是不是耗卡  1：不是    2：是
          // console.log(data['orderDetailsData'][i].consumeCard==1)
          if (data["orderDetailsData"][i].consumeCard == 1) {
            Obj["isActiveCostCard"] = 1;
            Obj["judgeCard"] = 1;
            Obj["costCard"] = {};
            Obj["costCard"]["card_info"] =
              data["orderDetailsData"][i]["consumeCardName"];
            Obj["costCard"]["membercard_id"] =
              data["orderDetailsData"][i]["consumeCardId"];
          } else {
            // console.log("走的是耗卡")
            Obj["isActiveCostCard"] = 2;
            Obj["judgeCard"] = 2;
            Obj["costCard"] = {};
            Obj["costCard"]["card_info"] =
              data["orderDetailsData"][i]["consumeCardName"];
            Obj["costCard"]["membercard_id"] =
              data["orderDetailsData"][i]["consumeCardId"];
            Obj["manualDiscountCard"]["discount"] = 0;
            Obj["costCard"]["manualDiscount"] = 2;
            // Obj['manualDiscountCard']={}
            // Obj['costCard']["card_info"]=data['orderDetailsData'][i]['cardName'];
          }

          //总的价格
          Obj["subtotal"] = (
            data["orderDetailsData"][i]["smallTotal"] / 100
          ).toFixed(2);

          if (data["orderDetailsData"][i].promoterId) {
            Obj["promoterId"] = data["orderDetailsData"][i].promoterId;
          } else {
            Obj["promoterId"] = 0;
          }

          //arr.push(Obj);
          _self.C_open_order_specifications_save.push(Obj);
        }
      }
      _self.loading = false;
      //console.log(data);
      if (data.presentData && data.presentData.length > 0) {
        let giftData = JSON.parse(JSON.stringify(data.presentData));
        giftData.forEach((item) => {
          item.originPrice = item.price;
          item.itemName = item.name;
          delete item.name;
        });
        _self.chooseGiftData(giftData);
      }
      _self.Pending();
      //console.log('最终的服务数据', _self.C_open_order_specifications_save);
    },

    //取单，展示取单的内容
    displayOrderOrdinary: function (data) {
      //获取店铺的赠送额度
      this.getPresentSet(data.id);

      // console.log('取单，展示取单的内容', data);
      var _self = this;
      // 协助接待
      var _self = this;
      if (this.istbz) {
        let myPromise = new Promise((resolve, reject) => {
          if (this.helpStaffAll.length == 0) {
            $.ajax({
              url: _self.url + "/android/Staff/sellsman",
              type: "post",
              data: {
                merchantid: _self.loginInfo.merchantid,
                storeid: _self.loginInfo.storeid,
              },
              success: function (res) {
                // var res = JSON.parse(res);
                if (res.code == 1) {
                  _self.helpStaffAll = res.data;
                  _self.helpStaffArr[_self.isactive1] = [];
                  resolve();
                } else {
                  _self.$message({
                    type: "error",
                    message: res.msg,
                    duration: 1500,
                  });
                }
              },
            });
          } else {
            resolve();
          }
        });
        myPromise.then(() => {
          if (typeof data.help_staff_ids !== "undefined") {
            _self.helpStaffArr[_self.isactive1] = [];
            data.help_staff_ids.forEach((id) => {
              _self.helpStaffAll.some((item) => {
                if (item["id"] == id) {
                  _self.helpStaffArr[_self.isactive1].push(item);
                }
              });
            });
          } else {
            _self.helpStaffArr[_self.isactive1] = [];
          }
        });
      }

      if (data.customer_remark) {
        _self.beizhu_info = data.customer_remark;
      } else {
        _self.beizhu_info = "";
      }
      _self.loading = true;
      var arr = []; //赋值给所有服务数组的临时数组
      //为了求出_self.changeOrderinfos结果定义的变量
      var Array = []; //记录出现服务的id  重复的多次记录
      var onlyArray = []; //记录出现服务的id  重复的记录一次

      //获取现金券
      if (data.member_coupon != 0 && data.member_counpon_money != 0) {
        let couponArr = function () {
          return $.ajax({
            url: _self.url + "/android/Member/getCouponInfo",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid, // 商户id
              storeid: _self.loginInfo.storeid, // 商户id
              // "type": req.body.type,              // 1 未使用 2已使用 3已过期，在会员详情中必须传，会员开单选择时不传此字段
              limit: 20, // 分页 每页几条
              page: 1, // 分页 第几页
              uid: data.buyer.id, // 用户id
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 0) {
                _self.couponInfo = res.data;
              } else {
                _self.$message({
                  type: "warning",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            error: function (err) {},
          });
        };
        couponArr().done((res) => {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.couponInfo = res.data;
            //获取现金券信息
            //console.log(_self.couponInfo)
            _self.couponInfo.forEach((item) => {
              if (item.id == data.member_coupon) {
                _self.C_open_order_specifications_save.couponCard = {};
                _self.isCouponCard = true;
                _self.C_open_order_specifications_save.judgeCard = 3;
                _self.C_open_order_specifications_save.couponCard = item;
                if (item.applyGoods == 2 && item.consume == 0) {
                  //console.log("指定商品，无门槛");
                  _self.assignGoods = true;
                  _self.assignPrice = data.member_counpon_money;
                }
                _self.C_open_order_specifications_save.couponCard.coupon_name =
                  item.coupon_name;
                //console.log(_self.C_open_order_specifications_save)
                _self.Pending();
              }
            });
          }
        });
      }

      for (let i = 0; i < data["orderDetailsData"].length; i++) {
        Array.push(data["orderDetailsData"][i]["goodsId"]);
      }
      for (let i = 0; i < Array.length; i++) {
        if (onlyArray.indexOf(Array[i]) == -1) {
          onlyArray.push(Array[i]);
        }
      }
      var times;
      for (let i = 0; i < onlyArray.length; i++) {
        times = 0;
        for (let j = 0; j < data["orderDetailsData"].length; j++) {
          if (onlyArray[i] == data["orderDetailsData"][j]["goodsId"]) {
            data["orderDetailsData"][j]["showtimes"] = times;
            times++;
            data["orderDetailsData"][j]["showtimes"] = times;
          }
        }
      }

      //原有的取单内容结束
      for (let i = 0; i < data["orderDetailsData"].length; i++) {
        let Obj = {};
        Obj["manualDiscountCard"] = {};
        Obj["manualDiscountCard"]["cardName"] = "";
        Obj["manualDiscountCard"]["membercard_id"] = "";
        Obj["manualDiscountCard"]["id"] = "";
        var technician_name = "";
        var saler_name = "";
        var salesid = [];
        //console.log(data['orderDetailsData'][i]['equity_type'])
        //equity_type 1 无权益 2折扣 3抵扣 4手动改价
        if (data["orderDetailsData"][i]["equity_type"] == 1) {
          //不选择优惠权益
          if (data["orderDetailsData"][i].technicians.length > 0) {
            for (
              var j = 0;
              j < data["orderDetailsData"][i].technicians.length;
              j++
            ) {
              if (j + 1 == data["orderDetailsData"][i].technicians.length) {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname;
              } else {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname + ", ";
              }
            }
          }
          if (data["orderDetailsData"][i].sellsname.length > 0) {
            for (
              var k = 0;
              k < data["orderDetailsData"][i].sellsname.length;
              k++
            ) {
              if (k + 1 == data["orderDetailsData"][i].sellsname.length) {
                saler_name += data["orderDetailsData"][i].sellsname[k].nickname;
              } else {
                saler_name +=
                  data["orderDetailsData"][i].sellsname[k].nickname + ", ";
              }
              salesid.push(data["orderDetailsData"][i]["sellsname"][k]["id"]);
            }
          }
          //console.log('销售id', data['orderDetailsData'][i]['sellsname']);
          // //对接服务人员
          //console.log('这是赋值服务人员时候的服务人员name111111', technician_name);
          //console.log('这是赋值服务人员时候的服务人员数组', data['orderDetailsData'][i]['technicians']);
          Obj["technician_name"] = technician_name;
          Obj["technician_id"] = data["orderDetailsData"][i]["technicians"];
          Obj["is_open_product_jishi"] =
            data["orderDetailsData"][i]["type"] == 2 ? true : false;
          //对接销售
          Obj["salesmen"] = data["orderDetailsData"][i]["sells_id"];
          Obj["saler_name"] = saler_name;
          //itemId
          Obj["itemId"] = data["orderDetailsData"][i]["itemId"];
          Obj["itemImgId"] = data["orderDetailsData"][i]["itemImgId"];
          //对接数量和价格
          Obj["num"] = data["orderDetailsData"][i]["num"];
          Obj["price"] = data["orderDetailsData"][i]["price"] / 100;
          Obj["member_price"] =
            data["orderDetailsData"][i]["member_price"] / 100;
          //对接规格相关数据
          Obj["sku"] = data["orderDetailsData"][i]["sku_name"];
          //服务对象的名称和id此处有俩个id
          Obj["service_name"] = data["orderDetailsData"][i]["name"];
          Obj["product_id"] = data["orderDetailsData"][i]["couponGoodsId"];
          Obj["goodsId"] = data["orderDetailsData"][i]["goodsId"];
          Obj["id"] = data["orderDetailsData"][i]["goodsId"];
          //判断改对象是服务还是产品;
          Obj["zhonglei"] = data["orderDetailsData"][i]["type"];
          //权益类型
          //权益相关
          Obj["manualDiscount"] = data["orderDetailsData"][i]["equity_type"];
          Obj["manualDiscountCard"]["cardName"] =
            data["orderDetailsData"][i]["cardName"];
          Obj["manualDiscountCard"]["membercard_id"] =
            data["orderDetailsData"][i]["card_id"];

          //判断是不是耗卡  1：不是    2：是
          if (data["orderDetailsData"][i].consumeCard == 1) {
            if (data["orderDetailsData"][i].cardName == "会员等级折扣") {
              Obj["isActiveCostCard"] = 1;
              Obj["judgeCard"] = 0;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] = "不使用耗卡";
            } else {
              Obj["isActiveCostCard"] = 1;
              Obj["judgeCard"] = 0;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] = "不使用耗卡";
            }
          } else {
            Obj["isActiveCostCard"] = 2;
            Obj["judgeCard"] = 2;
            Obj["costCard"] = {};
            Obj["costCard"]["card_info"] =
              data["orderDetailsData"][i]["consumeCardName"];
            Obj["costCard"]["membercard_id"] =
              data["orderDetailsData"][i]["consumeCardId"];
            Obj["manualDiscountCard"]["discount"] =
              data["orderDetailsData"][i]["discount"];
            Obj["costCard"]["manualDiscount"] = 2;
          }
          //总的价格
          Obj["subtotal"] = (
            data["orderDetailsData"][i]["smallTotal"] / 100
          ).toFixed(2);
          if (data["orderDetailsData"][i].promoterId) {
            Obj["promoterId"] = data["orderDetailsData"][i].promoterId;
          } else {
            Obj["promoterId"] = 0;
          }

          arr.push(Obj);
          _self.C_open_order_specifications_save.push(Obj);
        }
        //equity_type 1 无权益 2折扣 3抵扣 4手动改价
        else if (data["orderDetailsData"][i]["equity_type"] == 2) {
          if (data["orderDetailsData"][i].technicians.length > 0) {
            for (
              var j = 0;
              j < data["orderDetailsData"][i].technicians.length;
              j++
            ) {
              if (j + 1 == data["orderDetailsData"][i].technicians.length) {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname;
              } else {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname + ", ";
              }
            }
          }
          if (data["orderDetailsData"][i].sellsname.length > 0) {
            for (
              var k = 0;
              k < data["orderDetailsData"][i].sellsname.length;
              k++
            ) {
              if (k + 1 == data["orderDetailsData"][i].sellsname.length) {
                saler_name += data["orderDetailsData"][i].sellsname[k].nickname;
              } else {
                saler_name +=
                  data["orderDetailsData"][i].sellsname[k].nickname + ", ";
              }
              salesid.push(data["orderDetailsData"][i]["sellsname"][k]["id"]);
            }
          }
          //console.log('销售id', data['orderDetailsData'][i]['sellsname']);
          //对接服务人员
          Obj["technician_name"] = technician_name;
          Obj["technician_id"] = data["orderDetailsData"][i]["technicians"];
          Obj["is_open_product_jishi"] =
            data["orderDetailsData"][i]["type"] == 2 ? true : false;
          //对接销售
          Obj["salesmen"] = data["orderDetailsData"][i]["sells_id"];
          Obj["saler_name"] = saler_name;
          //itemId
          Obj["itemId"] = data["orderDetailsData"][i]["itemId"];
          Obj["itemImgId"] = data["orderDetailsData"][i]["itemImgId"];
          //对接数量和价格
          Obj["num"] = data["orderDetailsData"][i]["num"];
          Obj["price"] = (data["orderDetailsData"][i]["price"] / 100).toFixed(
            2
          );
          Obj["member_price"] = (
            data["orderDetailsData"][i]["member_price"] / 100
          ).toFixed(2);
          //console.log(Obj['price'])
          //对接规格相关数据
          Obj["sku"] = data["orderDetailsData"][i]["sku_name"];
          //服务对象的名称和id此处有俩个id
          Obj["service_name"] = data["orderDetailsData"][i]["name"];
          Obj["product_id"] = data["orderDetailsData"][i]["couponGoodsId"];
          Obj["goodsId"] = data["orderDetailsData"][i]["goodsId"];
          Obj["id"] = data["orderDetailsData"][i]["goodsId"];
          //判断改对象是服务还是产品;
          Obj["zhonglei"] = data["orderDetailsData"][i]["type"];
          //总的价格
          Obj["subtotal"] = data["orderDetailsData"][i]["price"];
          //权益相关
          Obj["manualDiscount"] = data["orderDetailsData"][i]["equity_type"];
          Obj["card_id"] = data["orderDetailsData"][i]["card_id"];
          //需加字段
          Obj["manualDiscountCard"]["cardName"] =
            data["orderDetailsData"][i]["cardName"];
          Obj["manualDiscountCard"]["membercard_id"] =
            data["orderDetailsData"][i]["card_id"];
          Obj["manualDiscountCard"]["id"] =
            data["orderDetailsData"][i]["card_details_id"];
          Obj["manualDiscountCard"]["discount"] =
            data["orderDetailsData"][i]["discount"];
          //console.log("跳转到收银台开单的折扣卡数据")
          //console.log(data)

          //耗卡信息加进去
          //判断是不是耗卡  1：不是    2：是
          //console.log(data['orderDetailsData'][i])
          if (data["orderDetailsData"][i].consumeCard == 1) {
            if (data["orderDetailsData"][i].cardName == "会员等级折扣") {
              Obj["isActiveCostCard"] = 1;
              Obj["judgeCard"] = 0;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] = "不使用耗卡";
            } else {
              Obj["isActiveCostCard"] = 1;
              Obj["judgeCard"] = 1;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] =
                data["orderDetailsData"][i]["consumeCardName"];
              Obj["costCard"]["membercard_id"] =
                data["orderDetailsData"][i]["consumeCardId"];
            }
          } else {
            // console.log("走的是耗卡")
            Obj["isActiveCostCard"] = 2;
            Obj["judgeCard"] = 2;
            Obj["costCard"] = {};
            Obj["manualDiscountCard"]["cardName"] =
              data["orderDetailsData"][i]["cardName"];
            Obj["costCard"]["card_info"] =
              data["orderDetailsData"][i]["consumeCardName"];
            Obj["costCard"]["membercard_id"] =
              data["orderDetailsData"][i]["consumeCardId"];
            Obj["manualDiscountCard"]["discount"] =
              data["orderDetailsData"][i]["discount"];
            Obj["costCard"]["manualDiscount"] = 2;
          }
          //额外必需字段
          Obj["add_give_preferential"] = true;
          Obj["add_preferential_id"] = data["orderDetailsData"][i]["card_id"];
          Obj["add_preferential_type"] = 2;
          Obj["add_preferential_discount"] =
            data["orderDetailsData"][i]["smallTotal"];
          //小计
          Obj["subtotal"] = (
            data["orderDetailsData"][i]["smallTotal"] / 100
          ).toFixed(2);
          if (data["orderDetailsData"][i].promoterId) {
            Obj["promoterId"] = data["orderDetailsData"][i].promoterId;
          } else {
            Obj["promoterId"] = 0;
          }
          _self.C_open_order_specifications_save.push(Obj);
        }
        //equity_type 1 无权益 2折扣 3抵扣 4手动改价
        else if (data["orderDetailsData"][i]["equity_type"] == 3) {
          if (data["orderDetailsData"][i].technicians.length > 0) {
            for (
              var j = 0;
              j < data["orderDetailsData"][i].technicians.length;
              j++
            ) {
              if (j + 1 == data["orderDetailsData"][i].technicians.length) {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname;
              } else {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname + ", ";
              }
            }
          }
          if (data["orderDetailsData"][i].sellsname.length > 0) {
            for (
              var k = 0;
              k < data["orderDetailsData"][i].sellsname.length;
              k++
            ) {
              if (k + 1 == data["orderDetailsData"][i].sellsname.length) {
                saler_name += data["orderDetailsData"][i].sellsname[k].nickname;
              } else {
                saler_name +=
                  data["orderDetailsData"][i].sellsname[k].nickname + ", ";
              }
              salesid.push(data["orderDetailsData"][i]["sellsname"][k]["id"]);
            }
          }
          //是否有服务人员
          Obj["technician_name"] = technician_name;
          Obj["technician_id"] = data["orderDetailsData"][i]["technicians"];
          Obj["is_open_product_jishi"] =
            data["orderDetailsData"][i]["type"] == 2 ? true : false;

          //对接销售
          Obj["salesmen"] = data["orderDetailsData"][i]["sells_id"];
          Obj["saler_name"] = saler_name;
          //itemId
          Obj["itemId"] = data["orderDetailsData"][i]["itemId"];
          Obj["itemImgId"] = data["orderDetailsData"][i]["itemImgId"];
          //对接数量和价格
          Obj["num"] = data["orderDetailsData"][i]["num"];
          Obj["price"] = data["orderDetailsData"][i]["price"] / 100;
          Obj["member_price"] =
            data["orderDetailsData"][i]["member_price"] / 100;
          //对接规格相关数据
          Obj["sku"] = data["orderDetailsData"][i]["sku_name"];
          //服务对象的名称和id此处有俩个id
          Obj["service_name"] = data["orderDetailsData"][i]["name"];
          Obj["product_id"] = data["orderDetailsData"][i]["couponGoodsId"];
          Obj["goodsId"] = data["orderDetailsData"][i]["goodsId"];
          Obj["id"] = data["orderDetailsData"][i]["goodsId"];
          //判断改对象是服务还是产品;
          Obj["zhonglei"] = data["orderDetailsData"][i]["type"];
          //总的价格
          Obj["subtotal"] = data["orderDetailsData"][i]["price"];
          //权益相关
          Obj["manualDiscount"] = 1;
          Obj["manualDiscountCard"]["cardName"] = "";
          Obj["manualDiscountCard"]["membercard_id"] = 0;
          // Obj['manualDiscountCard']['id'] = data['orderDetailsData'][i]['card_details_id'];

          //额外必需字段
          Obj["add_give_preferential"] = true;
          Obj["add_preferential_id"] = data["orderDetailsData"][i]["card_id"];
          Obj["add_preferential_type"] = 1;
          Obj["add_preferential_time"] = 1;

          //耗卡信息加进去
          Obj["isActiveCostCard"] = 1;
          Obj["judgeCard"] = 1;
          Obj["costCard"] = {};
          Obj["costCard"]["card_info"] = "不使用耗卡";

          //需加字段
          Obj["manualDiscount"] = data["orderDetailsData"][i]["equity_type"];
          Obj["manualDiscountCard"]["cardName"] =
            data["orderDetailsData"][i]["cardName"];
          Obj["manualDiscountCard"]["membercard_id"] =
            data["orderDetailsData"][i]["card_id"];
          Obj["manualDiscountCard"]["id"] =
            data["orderDetailsData"][i]["card_details_id"];
          //小计
          Obj["subtotal"] = (
            data["orderDetailsData"][i]["smallTotal"] / 100
          ).toFixed(2);
          if (data["orderDetailsData"][i].promoterId) {
            Obj["promoterId"] = data["orderDetailsData"][i].promoterId;
          } else {
            Obj["promoterId"] = 0;
          }
          _self.C_open_order_specifications_save.push(Obj);
        }
        //equity_type 1 无权益 2折扣 3抵扣 4手动改价
        else {
          //优惠金额
          if (data["orderDetailsData"][i].technicians.length > 0) {
            for (
              var j = 0;
              j < data["orderDetailsData"][i].technicians.length;
              j++
            ) {
              if (j + 1 == data["orderDetailsData"][i].technicians.length) {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname;
              } else {
                technician_name +=
                  data["orderDetailsData"][i].technicians[j].nickname + ", ";
              }
            }
          }
          if (data["orderDetailsData"][i].sellsname.length > 0) {
            for (
              var k = 0;
              k < data["orderDetailsData"][i].sellsname.length;
              k++
            ) {
              if (k + 1 == data["orderDetailsData"][i].sellsname.length) {
                saler_name += data["orderDetailsData"][i].sellsname[k].nickname;
              } else {
                saler_name +=
                  data["orderDetailsData"][i].sellsname[k].nickname + ", ";
              }
              salesid.push(data["orderDetailsData"][i]["sellsname"][k]["id"]);
            }
          }
          //对接服务人员
          //console.log('销售id', data['orderDetailsData'][i]['sellsname']);
          Obj["technician_name"] = technician_name;
          Obj["technician_id"] = data["orderDetailsData"][i]["technicians"];
          Obj["is_open_product_jishi"] =
            data["orderDetailsData"][i]["type"] == 2 ? true : false;
          //对接销售
          Obj["salesmen"] = data["orderDetailsData"][i]["sells_id"];
          Obj["saler_name"] = saler_name;
          //itemId
          Obj["itemId"] = data["orderDetailsData"][i]["itemId"];
          Obj["itemImgId"] = data["orderDetailsData"][i]["itemImgId"];
          //对接数量和价格
          Obj["num"] = data["orderDetailsData"][i]["num"];
          Obj["price"] = data["orderDetailsData"][i]["price"] / 100;
          Obj["member_price"] =
            data["orderDetailsData"][i]["member_price"] / 100;
          //对接规格相关数据
          Obj["sku"] = data["orderDetailsData"][i]["sku_name"];
          //服务对象的名称和id此处有俩个id
          Obj["service_name"] = data["orderDetailsData"][i]["name"];
          Obj["product_id"] = data["orderDetailsData"][i]["couponGoodsId"];
          Obj["goodsId"] = data["orderDetailsData"][i]["goodsId"];
          Obj["id"] = data["orderDetailsData"][i]["goodsId"];
          //判断改对象是服务还是产品;
          //权益相关
          Obj["manualDiscount"] = data["orderDetailsData"][i]["equity_type"];
          Obj["manualDiscountCard"]["cardName"] =
            data["orderDetailsData"][i]["cardName"];
          Obj["manualDiscountCard"]["membercard_id"] =
            data["orderDetailsData"][i]["card_id"];
          Obj["zhonglei"] = data["orderDetailsData"][i]["type"];

          //耗卡信息加进去
          //判断是不是耗卡  1：不是    2：是
          // console.log(data['orderDetailsData'][i].consumeCard==1)
          if (data["orderDetailsData"][i].consumeCard == 1) {
            Obj["isActiveCostCard"] = 1;
            Obj["judgeCard"] = 1;
            Obj["costCard"] = {};
            Obj["costCard"]["card_info"] =
              data["orderDetailsData"][i]["consumeCardName"];
            Obj["costCard"]["membercard_id"] =
              data["orderDetailsData"][i]["consumeCardId"];
          } else {
            // console.log("走的是耗卡")
            Obj["isActiveCostCard"] = 2;
            Obj["judgeCard"] = 2;
            Obj["costCard"] = {};
            Obj["costCard"]["card_info"] =
              data["orderDetailsData"][i]["consumeCardName"];
            Obj["costCard"]["membercard_id"] =
              data["orderDetailsData"][i]["consumeCardId"];
            Obj["manualDiscountCard"]["discount"] = 0;
            Obj["costCard"]["manualDiscount"] = 2;
          }
          //总的价格
          Obj["subtotal"] = (
            data["orderDetailsData"][i]["smallTotal"] / 100
          ).toFixed(2);
          if (data["orderDetailsData"][i].promoterId) {
            Obj["promoterId"] = data["orderDetailsData"][i].promoterId;
          } else {
            Obj["promoterId"] = 0;
          }
          arr.push(Obj);
          _self.C_open_order_specifications_save.push(Obj);
        }
      }

      //console.log(data);
      if (data.presentData && data.presentData.length > 0) {
        let giftData = JSON.parse(JSON.stringify(data.presentData));
        giftData.forEach((item) => {
          item.originPrice = item.price;
          item.itemName = item.name;
          delete item.name;
        });
        _self.chooseGiftData(giftData);
      }
      _self.Pending();
      _self.loading = false;
    },

    openPay: function (obj) {
      this.buy_receipt = obj.buy_receipt;
      this.orderNo = obj.data.orderNo;
    },

    // 计算购物车小计
    subtotal(index) {
      var subtotal = 0;
      var row = this.C_open_order_specifications_save[index];
      if (row.zhonglei == 3) {
        subtotal = 1 * row.price;
      } else {
        subtotal = row.num * row.price;
      }
      return subtotal.toFixed(2);
    },

    //设置取单表头
    headerClass: function ({ row, rowIndex }) {
      return "background:#f6f6f6;color:#333;fontSize:15px;font-weight:600";
    },
    takeOrderName: function () {
      return "takeOrderName";
    },
    //获取用户信息
    getLoginInfo: function () {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      } else {
        this.$message({
          type: "error",
          message: "请登录",
          duration: 1500,
        });
      }
    },

    // TODO 左侧选择栏目，不能代码用的是index 0123,不能单纯注释选行item

    //左侧导航条
    getmenu: function (id) {
      // this.isactive1 = key;
      //global.flag 1:显示员工业绩 0:不显示员工业绩

      // 记录前一个菜单ID，用于判断导航方向
      let prevId = this.isactive1;
      this.isactive1 = id;

      // 根据导航方向设置动画
      const newIndex = this.leftMenu.findIndex((item) => item.id === id);
      const oldIndex = this.leftMenu.findIndex((item) => item.id === prevId);
      this.transitionDirection =
        newIndex > oldIndex ? "slide-down" : "slide-up";
      if (id == 4) {
        this.handleFoucsInput = true;
        this.handleDirectFoucsInput = false;
        global.flag = 1;
      } else if (id == 1) {
        this.handleDirectFoucsInput = false;
        this.handleFoucsInput = false;
        global.flag = 1;
      } else if (id == 2) {
        this.handleDirectFoucsInput = false;
        this.handleFoucsInput = false;
        global.flag = 1;
      } else if (id == 0) {
        this.handleDirectFoucsInput = false;
        this.handleFoucsInput = false;
        global.flag = 1;
      } else if (id == 5) {
        //核销页面
        this.handleDirectFoucsInput = false;
        this.handleFoucsInput = false;
        // this.isVerify=true;
        global.flag = 1;
      } else {
        this.handleDirectFoucsInput = true;
        this.handleFoucsInput = false;
        global.flag = 0;
      }

      switch (this.isactive1) {
        case 0:
          this.$nextTick(
            function () {
              this.inputFocus(this.$refs.search_keyword);
            }.bind(this)
          );
          break;
        case 1:
          this.$nextTick(
            function () {
              this.inputFocus(this.$refs.memberPhone);
            }.bind(this)
          );
          break;
        case 2:
          this.getRecharge();
          this.$nextTick(
            function () {
              this.inputFocus(this.$refs.cz_search_keyword);
            }.bind(this)
          );
          break;
        case 5:
          this.$nextTick(
            function () {
              this.inputFocus(this.$refs.verifyCode);
            }.bind(this)
          );
          break;
      }
      //切换页面清空赠送
      this.billGiftData = [];
      this.showGiftData = {
        allPrice: 0,
        serverNum: 0,
        productNum: 0,
      };
    },

    /**
     *     开单
     *
     * */

    // 清空页面
    over_open: function () {
      // this.cashier_open_order_details_server_name = [];
      this.memberInfo = {};
      this.C_open_order_specifications_save = [];
      this.queryMember = "";
      this.is_dynamic = false;
      this.orderSourceType = 1;
      this.orderCount = 0;
      this.billingType = 0;
      this.couponCardCode = "";
      this.isCouponCard = false;
      ((this.billGiftData = []),
        (this.showGiftData = {
          allPrice: 0,
          serverNum: 0,
          productNum: 0,
        }));
      this.helpStaffArr[this.isactive1] = [];
      this.$nextTick(
        function () {
          this.inputFocus(this.$refs.search_keyword);
        }.bind(this)
      );
      this.Pending();
      localStorage.removeItem("billing");
    },

    // 清空开单，不清除会员
    clearOpenOrder: function () {
      this.C_open_order_specifications_save = [];

      this.queryMember = "";
      this.is_dynamic = false;
      this.orderSourceType = 1;
      this.orderCount = 0;
      // this.billingType = 0;
      this.couponCardCode = "";
      this.isCouponCard = false;
      ((this.billGiftData = []),
        (this.showGiftData = {
          allPrice: 0,
          serverNum: 0,
          productNum: 0,
        }));

      this.helpStaffArr[this.isactive1] = [];
      this.Pending();
      localStorage.removeItem("billing");
    },

    //查看耗材
    seeCostMaterial: function () {
      let _self = this;
      let serviceArr = [];
      _self.C_open_order_specifications_save.forEach((item) => {
        if (item.zhonglei != 2) {
          serviceArr.push({
            sku_id: item.sku_val_id ? item.sku_val_id : 0,
            service_id: item.id,
          });
        }
      });

      let couponArr = function () {
        return $.ajax({
          url: _self.url + "/api/helper/getServiceConsumable",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            storeid: _self.loginInfo.storeid, // 商户id
            serviceArr: JSON.stringify(serviceArr), // 1 未使用 2已使用 3已过期，在会员详情中必须传，会员开单选择时不传此字段
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
            } else {
              _self.$message({
                type: "warning",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (err) {},
        });
      };
      couponArr().done((res) => {
        // var res = JSON.parse(res);
        if (res.code == 1) {
          _self.costMaterialData = res.data;
          _self.isCostMaterial = true;
        }
      });
    },
    seeCostMaterialPrint: function (row) {
      let _self = this;

      let orderId = row.id;
      let orderNo = row.order_number;
      let couponArr = function () {
        return $.ajax({
          url: _self.url + "/api/helper/getorderservicelist",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            storeid: _self.loginInfo.storeid, // 商户id
            orderNo: orderNo, // 订单号
            orderId: orderId, //订单Id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              _self.costMaterialData = res.data;
            } else {
              _self.$message({
                type: "warning",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (err) {},
        });
      };
      couponArr().done((res) => {
        // var res = JSON.parse(res);
        if (res.code == 1) {
          _self.isCostMaterial = true;
        }
      });
    },

    //取单，打印耗材
    takeOrderPrintMaterial: function () {
      if (!LODOPbol) {
        this.noPrint();
        return;
      }
      var vm = this;
      if (this.printSet.length == 0) {
        Preview1();
      } else {
        // vm.printorderinfo = res.info;
        var str = $(vm.$refs.printorderstr).html();
        Preview2(str);
      }
    },

    //打印耗材
    printCostMaterial: function (orderNo, orderId) {
      let _self = this;
      let couponArr = function () {
        return $.ajax({
          url: _self.url + "/api/helper/getorderservicelist",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            storeid: _self.loginInfo.storeid, // 商户id
            orderNo: orderNo, // 订单号
            orderId: orderId, //订单Id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              _self.costMaterialData = res.data;
            } else {
              _self.$message({
                type: "warning",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (err) {},
        });
      };
      couponArr().done((res) => {
        // var res = JSON.parse(res);
        if (res.code == 1) {
          // _self.isCostMaterial=true;

          setTimeout(() => {
            if (!LODOPbol) {
              this.noPrint();
              return;
            }
            var vm = this;
            if (this.printSet.length == 0) {
              Preview1();
            } else {
              // vm.printorderinfo = res.info;
              var str = $(vm.$refs.printorderstr).html();
              Preview2(str);
            }
          }, 100);
        }
      });
    },

    switchFunction(flag) {
      switch (this.billingType) {
        case "0":
        case 0:
          // 服务
          if (!this.cashier_open_order_service_name.length > 0 || flag) {
            this.serverPage = 1;
            this.isServerScroll = false;
            this.serviceList();
          }
          this.$nextTick(() => {
            this.$refs.serverWrap.scrollTop = 0;
            this.inputFocus(this.$refs.search_keyword);
          });
          break;
        case "1":
          // 产品
          if (!this.cashier_open_order_product_name.length > 0 || flag) {
            this.productPage = 1;
            this.isComboScroll = false;
            this.productList();
          }
          if (!this.product_label.length > 0 || flag) {
            this.getProductLabelClass();
          }
          this.$nextTick(() => {
            this.$refs.productWrap.scrollTop = 0;
            this.inputFocus(this.$refs.search_product);
          });
          break;
        case "2":
          // 扣卡
          if (!this.newMemberCardInfo.length > 0 || flag) {
            this.getMemberStampCardInfo();
          }
          this.$nextTick(() => {
            this.$refs.cardWrap.scrollTop = 0;
          });
          break;
      }
      this.search_product = this.search_keyword = "";
    },

    // billingInquiry: function () {
    //     var billingType = Number(this.billingType);
    //     switch (billingType) {
    //         case 0:
    //             if (this.search_keyword == '') {
    //                 this.serverPage = 1;
    //                 this.serviceList()
    //             }
    //             break;
    //         case 1:
    //             if (this.search_product == '') {
    //                 this.productPage = 1;
    //                 this.productList();
    //             }
    //             break;
    //     }
    // },

    billingInquiryEnter: function () {
      var billingType = Number(this.billingType);
      var _self = this;
      switch (billingType) {
        case 0:
          _self.serverPage = 1;
          _self.serviceList();
          break;
        case 1:
          if (!this.search_product) return;
          _self.productPage = 1;
          _self.productList();
          break;
      }
    },

    //设置计时器
    setTime: function () {
      // console.log("计时器输入的内容",this.search_product)
      let _self = this;

      //清空计时器
      if (_self.setSearchTimeOut) {
        clearTimeout(_self.setSearchTimeOut);
      }

      //添加计时器
      _self.setSearchTimeOut = setTimeout(function () {
        var billingType = Number(_self.billingType);
        // var _self = this;
        switch (billingType) {
          case 0:
            _self.serverPage = 1;
            _self.serviceList();
            break;
          case 1:
            if (!_self.search_product) return;
            _self.productPage = 1;
            _self.productList();
            break;
        }
      }, 500);
    },

    //产品扫码
    scanProductCode: function () {
      // this.loading = true;
      var _self = this;
      if (_self.search_product == "") {
        _self.productPage = 1;
        _self.productList();
      } else {
        $.ajax({
          url: _self.url + "/api/Product/scanCode",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid,
            storeid: _self.loginInfo.storeid,
            code: _self.search_product,
            type: 2,
          },
          success: function (res) {
            // var res = JSON.parse(res);

            _self.autoAddProduct(res.data);
            _self.search_product = "";
          },
        });
      }
    },

    //扫码自动添加产品
    autoAddProduct: function (data) {
      let _self = this;
      var Obj = {};
      Obj["manualDiscountCard"] = {};
      Obj["manualDiscountCard"]["cardName"] = "";
      Obj["manualDiscountCard"]["membercard_id"] = "";
      Obj["manualDiscountCard"]["id"] = "";
      var technician_name = "";
      var saler_name = "";
      var salesid = [];

      //判断产品的id和规格是否相同
      if (_self.C_open_order_specifications_save.length >= 0) {
        let flag = true;
        for (
          let i = 0;
          i < _self.C_open_order_specifications_save.length;
          i++
        ) {
          let item = _self.C_open_order_specifications_save[i];
          if (data.goodsId == item.id) {
            if (data.skuId == 0 && data.sku_name == "") {
              item.num = item.num + 1;
              item.subtotal = (
                (Number(item.subtotal) * 100 + Number(item.price) * 100) /
                100
              ).toFixed(2);
              flag = false;
              break;
            } else if (
              data.skuId == item.sku_val_id &&
              data.sku_name == item.sku
            ) {
              item.num = item.num + 1;
              item.subtotal = (
                (Number(item.subtotal) * 100 + Number(item.price) * 100) /
                100
              ).toFixed(2);
              flag = false;
              break;
            }
          }
        }
        if (flag) {
          if (data["equity_type"] == 1) {
            Obj["technician_name"] = "";
            Obj["technician_id"] = data["technicians"];
            Obj["is_open_product_jishi"] = data["type"] == 2 ? true : false;
            //对接销售
            Obj["salesmen"] = data["sells_id"];
            Obj["saler_name"] = saler_name;
            //对接数量和价格
            Obj["num"] = data["num"];
            Obj["price"] = data["price"] / 100;
            Obj["member_price"] = data["member_price"] / 100;
            //对接规格相关数据
            Obj["sku"] = data["sku_name"];
            Obj["sku_val_id"] = data["skuId"];
            //服务对象的名称和id此处有俩个id
            Obj["service_name"] = data["name"];
            Obj["product_id"] = data["couponGoodsId"];
            Obj["goodsId"] = data["goodsId"];
            Obj["id"] = data["goodsId"];
            //判断改对象是服务还是产品;
            Obj["zhonglei"] = data["type"];
            //权益类型
            //权益相关
            Obj["manualDiscount"] = data["equity_type"];
            Obj["manualDiscountCard"]["cardName"] = data["cardName"];
            Obj["manualDiscountCard"]["membercard_id"] = data["card_id"];

            //判断是不是耗卡  1：不是    2：是
            if (data.consumeCard == 1) {
              if (data.cardName == "会员等级折扣") {
                Obj["isActiveCostCard"] = 1;
                Obj["judgeCard"] = 0;
                Obj["costCard"] = {};
                Obj["costCard"]["card_info"] = "不使用耗卡";
              } else {
                Obj["isActiveCostCard"] = 1;
                Obj["judgeCard"] = 0;
                Obj["costCard"] = {};
                Obj["costCard"]["card_info"] = "不使用耗卡";
              }
            } else {
              Obj["isActiveCostCard"] = 2;
              Obj["judgeCard"] = 2;
              Obj["costCard"] = {};
              Obj["costCard"]["card_info"] = data["consumeCardName"];
              Obj["costCard"]["membercard_id"] = data["consumeCardId"];
              Obj["manualDiscountCard"]["discount"] = data["discount"];
              Obj["costCard"]["manualDiscount"] = 2;
            }
            //总的价格
            Obj["subtotal"] = (data["smallTotal"] / 100).toFixed(2);
            // arr.push(Obj);
            _self.C_open_order_specifications_save.push(Obj);
          }
        }
      }
      _self.Pending();
    },

    // tab 切换
    billingChangeType: function () {
      this.clearOpenOrder();
      this.switchFunction();
    },

    // 开单点击标签
    bind_choice_server: function (data, key) {
      let allWidth = 0; //所有标签的总接口
      let count = 0; //所有标签的总数量
      let scrollWidth = 0; //滚动条移动的距离
      this.$refs.chooseLiTag.forEach((item) => {
        allWidth = allWidth + item.clientWidth + 10;
        count = count + 1;
      });
      // console.log("count的数量是："+count)
      // console.log("count的数量是："+key)

      if (allWidth > this.$refs.chooseTag.clientWidth) {
        if (key == 0) {
          scrollWidth = 0;
        } else {
          scrollWidth = (allWidth - this.$refs.chooseTag.clientWidth) / count;
        }
      }
      this.isActiveServer = key;
      this.serverLabelid = data.id || 0;
      this.serverPage = 1;
      this.serviceList();
      // this.search_keyword = '';
      this.$nextTick(() => {
        this.$refs.serverWrap.scrollTop = 0;
        this.$refs.chooseTag.scrollLeft = scrollWidth * (key + 1);
      });
    },
    bind_choice_product: function (data, key) {
      let allWidth = 0; //所有标签的总接口
      let count = 0; //所有标签的总数量
      let scrollWidth = 0; //滚动条移动的距离
      this.$refs.productLiTag.forEach((item) => {
        allWidth = allWidth + item.clientWidth + 10;
        count = count + 1;
      });
      if (allWidth > this.$refs.productUlTag.clientWidth) {
        if (key == 0) {
          scrollWidth = 0;
        } else {
          scrollWidth =
            (allWidth - this.$refs.productUlTag.clientWidth) / count;
        }
      }
      this.isActiveProduct = key;
      this.productLabelid = data.id || 0;
      this.productPage = 1;
      this.productList();
      // this.search_product = '';
      this.$nextTick(() => {
        this.$refs.productWrap.scrollTop = 0;
        this.$refs.productUlTag.scrollLeft = scrollWidth * (key + 1);
      });
    },

    // uuai 首页--服务查询  添加销售和服务人员的key还有数量都在这个函数里面
    serviceList: function (flag) {
      //console.log("ssssss")
      this.loading = true;
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Service/serviceList",
        type: "post",
        data: {
          keyword: _self.search_keyword,
          labelid: _self.serverLabelid,
          sort: 1, // 默认正序，1正序 2倒序
          status: 1, // 1上架。  2下架
          storeid: _self.loginInfo.storeid,
          page: _self.serverPage,
          limit: _self.serverLimit,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.serverAllCount = res.count;
            if (_self.serverAllCount < 10) {
              _self.busy = false;
            }
            if (flag == 1) {
              _self.cashier_open_order_service_name =
                _self.cashier_open_order_service_name.concat(res.data);
              if (res.count == 0) {
                _self.isServerScroll = true;
              } else {
                _self.isServerScroll = false;
              }
            } else {
              _self.cashier_open_order_service_name = res.data;
              _self.isServerScroll = false;
            }
            // console.log('起始数据');
            // console.log(_self.cashier_open_order_service_name);
            //获取数据后然后对数据赋值这个时候将查不到数据的div属性换掉
            //以下在加载服务时候给没一个服务添加一个服务人员id用来存储后面要穿的值
            for (
              let i = 0;
              i < _self.cashier_open_order_service_name.length;
              i++
            ) {
              _self.cashier_open_order_service_name[i]["technician_id"] = [];
              _self.cashier_open_order_service_name[i]["salesmen"] = [];
              _self.cashier_open_order_service_name[i]["num"] = 1;
              _self.cashier_open_order_service_name[i]["manualDiscount"] = 1;
              _self.cashier_open_order_service_name[i]["discount"] = 1;
              _self.cashier_open_order_service_name[i]["manualDiscountCard"] =
                {};
              _self.cashier_open_order_service_name[i]["zhonglei"] = 1; //添加一个字段用来判断种类
            }
          } else {
            this.cashier_open_order_service_name = [];
            _self.$message({
              type: "error",
              message: "暂无数据",
              duration: 1500,
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },

    getProductLabelClass: function () {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Goodsclass/goodsClassLabel",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          type: 2,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.product_label = res.data;
            _self.product_label.unshift({
              name: "所有标签",
            });
            //console.log(res);
          }
        },
      });
    },

    //选择产品接口
    productList: function (flag) {
      var _self = this;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/android/Product/getProductData",
        type: "post",
        data: {
          class: "",
          keyword: _self.search_product,
          label: _self.productLabelid,
          limit: _self.productLimit,
          merchantid: _self.loginInfo.merchantid,
          order: "1",
          page: _self.productPage,
          status: "1",
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            // 接口上是0成功 其他都是1
            _self.productAllCount = res.count;
            if (_self.productAllCount < 10) {
              _self.busyProduct = false;
            }
            if (flag == 1) {
              _self.cashier_open_order_product_name =
                _self.cashier_open_order_product_name.concat(res.data);
              if (res.count == 0) {
                _self.isComboScroll = true;
              } else {
                _self.isComboScroll = false;
              }
            } else {
              _self.cashier_open_order_product_name = res.data;
            }
            for (
              let i = 0;
              i < _self.cashier_open_order_product_name.length;
              i++
            ) {
              _self.cashier_open_order_product_name[i]["manualDiscount"] = 1;
              _self.cashier_open_order_product_name[i]["manualDiscountCard"] =
                {};
              _self.cashier_open_order_product_name[i]["zhonglei"] = 2;
              _self.cashier_open_order_product_name[i]["discount"] = 1;
              _self.cashier_open_order_product_name[i].goodsType = 2;
            }
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },

    //产品接口添加到开单详情在这里面为产品的字段更改和添加，注意尽量统一一级添加的位置（此处就是）
    //在此判断是否有规格  有则请求数据接口
    bind_add_product: function (data, index) {
      this.is_product_server = true;
      this.index_server_product = index;
      this.data_server_product = JSON.parse(JSON.stringify(data));
      this.loading = true;
      this.cashier_open_order_service_choice[0] =
        this.cashier_open_order_product_name[index];

      let newobj = new Object();
      let is_repeat = false;
      //创建临时的值用来替换接口中的key
      let id = data["id"].toString();
      this.C_open_order_specifications_name = []; //将要循环的数值和对比的清空
      this.C_open_order_specifications_attr = [];
      var _self = this;
      // console.log(this.C_open_order_specifications_save);
      // console.log(id);
      //以下将名字  价格  服务人员  服务人员展示销售 销售展示等其他数据重新进行深拷贝复制

      newobj["id"] = data["id"];
      newobj["product_id"] = data["product_id"];
      newobj["imgarr"] = data["imgarr"];
      newobj["issku"] = data["issku"];
      if (data["issku"] == 1) {
        newobj["realPrice"] = (data["price"] / 100).toFixed(2);
        newobj["price"] = data["realPrice"];
        if (data["memberPriceData"]) {
          newobj["memberPriceData"] = data["memberPriceData"];
        }
      } else {
        /* 会员价开始 */
        let memberPriceData = [];
        let levelId = 0;
        let price = (data["price"] / 100).toFixed(2);
        if (
          _self.memberInfo &&
          _self.memberInfo.levelInfo &&
          _self.memberInfo.levelInfo.id
        ) {
          levelId = _self.memberInfo.levelInfo.id;
        }
        if (data["memberPriceData"]) {
          memberPriceData = data["memberPriceData"];
        }
        memberPriceData.some((item) => {
          if (item["sku_id"] == 0 && item["member_level_id"] == levelId) {
            price = parseFloat((item["price"] / 100).toFixed(2));

            return true;
          }
          return false;
        });
        /* 会员价结束 */
        newobj["realPrice"] = price;
        newobj["price"] = price;
      }
      newobj["member_price"] = data["member_price"];
      newobj["itemImgId"] = data["itemImgId"];
      newobj["totalnum"] = data["totalnum"];
      newobj["subtotal"] = newobj["realPrice"] * 1;
      newobj["totalsell"] = data["totalsell"];
      newobj["service_name"] = data["product_name"];
      newobj["unit"] = data["unit"];
      newobj["salesmen"] = [];
      newobj["technician_id"] = [];
      newobj["saler_name"] = "";
      newobj["technician_name"] = "";
      newobj["num"] = 1;
      newobj["sku_val_id"] = "";
      newobj["zhonglei"] = 2;
      newobj["manualDiscount"] = data["manualDiscount"];
      newobj["manualDiscountCard"] = data["manualDiscountCard"];

      this.C_open_order_Specifications = JSON.parse(JSON.stringify(newobj));
      this.C_open_order_Specifications["imgurl"] =
        this.cashier_open_order_product_name[index]["imgarr"][0];
      // console.log(this.C_open_order_Specifications);
      if (data["issku"] == 1) {
        // bind_add_product
        _self.cashier_open_order_Specifications = true;
        $.ajax({
          url: _self.url + "/android/Product/getProductInfo",
          type: "post",
          data: {
            productId: id,
            merchantid: _self.loginInfo.merchantid,
            storeid: _self.loginInfo.storeid,
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              _self.C_open_order_specifications_name =
                res["data"]["skuinfo"]["skuattr"];
              _self.C_open_order_specifications_attr =
                res["data"]["skuinfo"]["skulist"];
              for (
                let i = 0;
                i < _self.C_open_order_specifications_name.length;
                i++
              ) {
                for (
                  let j = 0;
                  j < _self.C_open_order_specifications_name[i]["item"].length;
                  j++
                ) {
                  _self.C_open_order_specifications_name[i]["item"][j].is_show =
                    false;
                }
              }
              _self.loading = false;
            } else {
              // console.log(1);
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          complete: () => {
            _self.loading = false;
          },
        });
      } else {
        newobj["is_open_product_jishi"] = true;
        newobj["isActiveCostCard"] = 1;
        newobj["judgeCard"] = 0;
        // console.log('no');
        if (this.C_open_order_specifications_save.length > 0) {
          let productMerge = false; // 默认不能合并
          _self.C_open_order_specifications_save.some((item, i) => {
            if (
              item["id"] == id &&
              item["zhonglei"] == 2 &&
              item["issku"] == 2
            ) {
              // 判断是否可以合并  非规格产品
              // item['manualDiscount'] 1 无权益 2折扣 3抵扣 4手动改价 0 无权益
              if (
                item["manualDiscount"] == 1 &&
                item["judgeCard"] == 0 &&
                item["isActiveCostCard"] == 1
              ) {
                // 购物车中相同产品未使用权益 --- 能合并
                this.C_open_order_specifications_save[i]["num"] += 1;
                this.C_open_order_specifications_save[i]["subtotal"] = (
                  this.C_open_order_specifications_save[i]["num"] *
                  this.C_open_order_specifications_save[i]["price"]
                ).toFixed(2);
                productMerge = true;
                return true;
              } else {
                // 购物车中相同产品已使用权益 --- 不能合并
              }
            }
          });
          // 购物车已有产品未找到能合并的
          if (!productMerge) {
            this.C_open_order_specifications_save.unshift(
              JSON.parse(JSON.stringify(newobj))
            );
          }
        } else {
          this.C_open_order_specifications_save.unshift(
            JSON.parse(JSON.stringify(newobj))
          );
        }
        //添加产品时，优惠券信息清空
        if (this.C_open_order_specifications_save.judgeCard == 3) {
          this.C_open_order_specifications_save.couponCard = {};
          this.isCouponCard = false;
          this.C_open_order_specifications_save.judgeCard = 0;
          _self.assignGoods = false;
        }
        this.handleUseMemberPrice(0);
        this.Pending();
        _self.$forceUpdate();
        this.loading = false;
      }
    },

    //选择产品规格
    specificationBtn_product: function (value1id, value2id, index1, index2) {
      let _self = this;
      let time_true = 0; //计算选的规格个数
      let specifications = []; //存储最终确定时候保存的选中规格id
      let specifications_atrr = ""; //存储对比属性的sku_val_id值
      //这个循环添加按钮点击样式，然后更改对错

      let memberPriceData = [];
      if (_self.C_open_order_Specifications["memberPriceData"]) {
        memberPriceData = _self.C_open_order_Specifications["memberPriceData"];
      }
      for (let i = 0; i < _self.C_open_order_specifications_name.length; i++) {
        for (
          let j = 0;
          j < _self.C_open_order_specifications_name[i]["item"].length;
          j++
        ) {
          if (index1 == i) {
            if (index2 == j) {
              if (
                _self.C_open_order_specifications_name[i]["item"][j][
                  "is_show"
                ] == false
              ) {
                _self.C_open_order_specifications_name[i]["item"][j][
                  "is_show"
                ] = true;
              } else {
                _self.C_open_order_specifications_name[i]["item"][j][
                  "is_show"
                ] = false;
              }
            } else {
              _self.C_open_order_specifications_name[i]["item"][j]["is_show"] =
                false;
            }
          } else {
          }
        }
      }
      //因为要动态将选择规格的价格改变所以添加循环为判断是否全选了
      for (let i = 0; i < _self.C_open_order_specifications_name.length; i++) {
        for (
          let j = 0;
          j < _self.C_open_order_specifications_name[i]["item"].length;
          j++
        ) {
          if (
            _self.C_open_order_specifications_name[i]["item"][j]["is_show"] ==
            true
          ) {
            time_true++;
            specifications.push(
              _self.C_open_order_specifications_name[i]["item"][j]["id"]
            );
          }
        }
      }
      let memberPrice = 0;
      let levelId = 0;
      if (
        _self.memberInfo &&
        _self.memberInfo.levelInfo &&
        _self.memberInfo.levelInfo.id
      ) {
        levelId = _self.memberInfo.levelInfo.id;
      }
      //全选了为其匹配赋值
      if (time_true == _self.C_open_order_specifications_name.length) {
        for (
          let i = 0;
          i < _self.C_open_order_specifications_attr.length;
          i++
        ) {
          specifications_atrr =
            _self.C_open_order_specifications_attr[i]["sku_val_id"];
          if (specifications_atrr == specifications.join(",")) {
            // console.log(_self.C_open_order_specifications_attr[i],'选中的规格');
            let sku_id =
              _self.C_open_order_specifications_attr[i]["product_sku_id"];
            memberPriceData.some((item) => {
              if (
                item["sku_id"] == sku_id &&
                item["member_level_id"] == levelId
              ) {
                memberPrice = (item["price"] / 100).toFixed(2);
              }
              return false;
            });
            _self.C_open_order_Specifications["price"] = parseFloat(
              _self.C_open_order_specifications_attr[i]["price"] / 100
            ).toFixed(2);
            _self.C_open_order_Specifications["subtotal"] = (
              _self.C_open_order_Specifications["price"] *
              _self.C_open_order_Specifications["num"]
            ).toFixed(2);
          }
        }
      } else {
        // _self.C_open_order_Specifications['price'] = _self.data_server_product['realPrice']
        _self.C_open_order_Specifications["price"] =
          _self.data_server_product["s_price"];
      }
      _self.C_open_order_Specifications["memberPrice"] = memberPrice;
      _self.$forceUpdate();
    },
    //首页--标签接口
    labelList: function () {
      this.loading = true;
      var _self = this;
      let type = 0;
      if (_self.billingType == 0) {
        type = 4;
      } else if (_self.billingType == 1) {
        type = 2;
      }
      $.ajax({
        url: _self.url + "/android/Goodsclass/goodsClassLabel",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          type: type,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.cashier_open_order_service_label = res.data;
            _self.cashier_open_order_service_label.unshift({
              label_name: "所有标签",
            });
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },

    loadMoreProduct: function () {
      var _self = this;
      _self.isServerScroll = true;
      if (_self.serverAllCount == this.cashier_open_order_service_name.length) {
        _self.loadingtip = "数据已全部加载";
        return false;
      } else {
        _self.busy = true;
        _self.loadingtip = "加载中···";
        _self.serverPage++;
        _self.serviceList(1);
      }
    },

    //选择服务
    bind_add_server: function (data, index) {
      this.is_product_server = false;
      this.index_server_product = index;
      this.data_server_product = JSON.parse(JSON.stringify(data));
      let data1 = data;
      this.C_open_order_Specifications =
        this.cashier_open_order_service_name[index];
      this.cashier_open_order_service_choice[0] =
        this.cashier_open_order_service_name[index];

      if (data1.status == 1) {
        //上架and下架
        if (data1.Specifications == 1) {
          //规格
          this.C_open_order_Specifications = JSON.parse(JSON.stringify(data1));
          let id = data1.id;
          let _self = this;
          _self.loading = true;
          $.ajax({
            url: _self.url + "/android/Service/serviceSku",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              serviceid: id,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              _self.cashier_open_order_Specifications = true;
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.C_open_order_specifications_name = res.data["sku"];
                _self.C_open_order_specifications_attr = res.data["sku_attr"];
                for (
                  let i = 0;
                  i < _self.C_open_order_specifications_name.length;
                  i++
                ) {
                  for (
                    let j = 0;
                    j <
                    _self.C_open_order_specifications_name[i]["sku_val"].length;
                    j++
                  ) {
                    _self.C_open_order_specifications_name[i]["sku_val"][
                      j
                    ].is_show = false;
                  }
                }
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            complete: () => {
              _self.loading = false;
            },
          });
        } else {
          let _self = this;
          this.cashier_open_order_service_choice[0]["specifications_id"] = 0;
          let new0bj = this.deepCopy(this.cashier_open_order_service_choice[0]);
          /* 会员价开始 */
          let memberPriceData = [];
          let levelId = 0;
          let price = new0bj.price;
          if (
            _self.memberInfo &&
            _self.memberInfo.levelInfo &&
            _self.memberInfo.levelInfo.id
          ) {
            levelId = _self.memberInfo.levelInfo.id;
          }
          if (data["memberPriceData"]) {
            memberPriceData = data["memberPriceData"];
          }
          memberPriceData.some((item) => {
            if (item["sku_id"] == 0 && item["member_level_id"] == levelId) {
              price = parseFloat((item["price"] / 100).toFixed(2));
              return true;
            }
            return false;
          });
          new0bj.price = price;
          /* 会员价结束 */
          new0bj["subtotal"] = (new0bj.num * new0bj.price).toFixed(2);
          new0bj["isActiveCostCard"] = 1;
          new0bj["judgeCard"] = 0;
          this.C_open_order_specifications_save.unshift(
            JSON.parse(JSON.stringify(new0bj))
          );
          if (this.C_open_order_specifications_save.judgeCard == 3) {
            this.C_open_order_specifications_save.couponCard = {};
            this.isCouponCard = false;
            this.C_open_order_specifications_save.judgeCard = 0;
            this.assignGoods = false;
          }
          this.handleUseMemberPrice(0);
          this.loading = false;
          this.Pending();
        }
      } else {
        this.$message({
          message: "该商品已经下架",
          center: true,
          duration: 1500,
        });
        this.loading = false;
      }
    },

    //选择服务规格
    specificationBtn_server: function (value1id, value2id, index1, index2) {
      let _self = this;
      let time_true = 0; //计算选的规格个数
      let specifications = []; //存储最终确定时候保存的选中规格id
      let specifications_atrr = ""; //存储对比属性的sku_val_id值
      //这个循环添加按钮点击样式，然后更改对错

      let memberPriceData = [];
      if (_self.C_open_order_Specifications["memberPriceData"]) {
        memberPriceData = _self.C_open_order_Specifications["memberPriceData"];
      }
      for (let i = 0; i < _self.C_open_order_specifications_name.length; i++) {
        for (
          let j = 0;
          j < _self.C_open_order_specifications_name[i]["sku_val"].length;
          j++
        ) {
          if (index1 == i) {
            if (index2 == j) {
              if (
                _self.C_open_order_specifications_name[i]["sku_val"][j][
                  "is_show"
                ] == false
              ) {
                _self.C_open_order_specifications_name[i]["sku_val"][j][
                  "is_show"
                ] = true;
              } else {
                _self.C_open_order_specifications_name[i]["sku_val"][j][
                  "is_show"
                ] = false;
              }
            } else {
              _self.C_open_order_specifications_name[i]["sku_val"][j][
                "is_show"
              ] = false;
            }
          }
        }
      }
      //因为要动态将选择规格的价格改变所以添加循环为判断是否全选了
      for (let i = 0; i < _self.C_open_order_specifications_name.length; i++) {
        for (
          let j = 0;
          j < _self.C_open_order_specifications_name[i]["sku_val"].length;
          j++
        ) {
          if (
            _self.C_open_order_specifications_name[i]["sku_val"][j][
              "is_show"
            ] == true
          ) {
            time_true++;
            specifications.push(
              _self.C_open_order_specifications_name[i]["sku_val"][j]["id"]
            );
          }
        }
      }
      let memberPrice = 0;
      let levelId = 0;
      if (
        _self.memberInfo &&
        _self.memberInfo.levelInfo &&
        _self.memberInfo.levelInfo.id
      ) {
        levelId = _self.memberInfo.levelInfo.id;
      }
      //全选了为其匹配赋值
      if (time_true == _self.C_open_order_specifications_name.length) {
        // 选择完善了
        for (
          let i = 0;
          i < _self.C_open_order_specifications_attr.length;
          i++
        ) {
          specifications_atrr =
            _self.C_open_order_specifications_attr[i]["sku_val_id"];
          if (specifications_atrr == specifications.join(",")) {
            // console.log(_self.C_open_order_specifications_attr[i],'选中的规格');
            let sku_id = _self.C_open_order_specifications_attr[i]["id"];
            memberPriceData.some((item) => {
              if (
                item["sku_id"] == sku_id &&
                item["member_level_id"] == levelId
              ) {
                memberPrice = (item["price"] / 100).toFixed(2);
              }
              return false;
            });
            _self.C_open_order_Specifications["price"] =
              _self.C_open_order_specifications_attr[i]["price"];
            if (_self.C_open_order_Specifications["zhonglei"] == 3) {
              _self.C_open_order_Specifications["subtotal"] = (
                _self.C_open_order_Specifications["price"] *
                _self.C_open_order_Specifications["buyNum"]
              ).toFixed(2);
            } else {
              _self.C_open_order_Specifications["subtotal"] = (
                _self.C_open_order_Specifications["price"] *
                _self.C_open_order_Specifications["num"]
              ).toFixed(2);
            }
          }
        }
      } else {
        _self.C_open_order_Specifications["price"] =
          _self.data_server_product["price"];
      }
      _self.C_open_order_Specifications["memberPrice"] = memberPrice;
      _self.$forceUpdate();
    },
    //意外关闭选择规格弹框的事件
    handleCloseSpecification: function () {
      let _self = this;
      this.cashier_open_order_Specifications = false;
      this.serviceSpecificationStatus = 1;

      // let collapseIndex = this.membercardChangeIndex;
      //判断当前折叠面板的数组长度是否大于1
      // if (_self.membercardChangeIndex >= 0) {
      //     if (_self.newMemberCardInfo[collapseIndex].goodsInfo.length > 1) {
      //         _self.newMemberCardInfo[collapseIndex].goodsInfo.forEach(item => {
      //             if (item.id == _self.data_server_product.id && item.equityTypes == 3) {
      //                 //infinite 1：是无限次卡 2：不是无限次卡
      //                 if (item.infinite == 2) {
      //                     console.log("删除时把服务次数还回去")
      //                     item.num = item.num + 1;//删除时把服务次数还回去
      //                 }
      //             }
      //         });
      //     }
      // }
    },

    //确定服务或者产品规格
    save_specifications: function () {
      // console.log("确认规格++++++++++++++++++++++++++++++++++++++++++++");
      let _self = this;
      let time_true = 0; //计算选的规格个数
      let specifications = []; //存储最终确定时候保存的选中规格id
      let specifications_atrr = ""; //存储对比属性的sku_val_id值
      let change_id = null; //因为选择规格后的id和选择服务的id重了，定义中间量 另外specifications_id是选择规格后匹配的id
      let is_repeat = true;
      // return console.log(_self.chooseProjuctFlag);
      //确定产品规格
      if (_self.is_product_server) {
        //确定产品的规格

        let id = _self.cashier_open_order_service_choice[0]["id"];

        // 循环获取已选择的规格项目个数，选择详情
        _self.C_open_order_specifications_name.forEach((item) => {
          // 子项二次循环，记录选中的规格
          item["item"].forEach((sItem) => {
            if (sItem["is_show"] == true) {
              time_true++;
              specifications.push(sItem["id"]);
            }
          });
        });
        // console.log();
        // 判断已选规格个数 是否满足要求
        if (time_true == _self.C_open_order_specifications_name.length) {
          // 检测合并
          let productMerge = false; // 默认不能合并
          specifications.sort(function (a, b) {
            return a - b;
          });
          let sku_val_id = specifications.join(",");
          let new0bj = {};
          if (_self.chooseProjuctFlag) {
            let data = _self.chooseProjuctService["data"];
            let cardInfo = _self.chooseProjuctService["cardInfo"];
            let balanceCardItem = {};
            if (data.equityType == 3) {
              let item = {
                cardName: cardInfo["card_name"],
                cardSource: cardInfo["cardSource"],
                card_type: cardInfo["cardtype"],
                discount: data["discount"] ? data["discount"] : "1",
                id: data["card_details_id"],
                indate: cardInfo["indateName"],
                isgive: data["givenum"] > 0 ? 1 : 2,
                maxnum: data["num"],
                membercard_id: cardInfo["card_id"],
                once_cardtype: cardInfo["once_cardtype"],
              };
              new0bj["manualDiscount"] = 3;
              new0bj["manualDiscountCard"] = item;
              new0bj["discount"] = item.discount;
              new0bj["choosemembercardId"] = data.card_details_id;
              new0bj["chooseMemberCardCount"] = 0;
              new0bj["isActiveCostCard"] = 1;
              new0bj["subtotal"] = 0;
              new0bj.judgeCard = 1;
              new0bj.costCard = {};
              new0bj.costCard.card_info = "不使用耗卡";
            } else {
              return _self.$message("产品不支持卡项折扣权益");
            }
          } else {
            _self.C_open_order_specifications_save.some((item, i) => {
              if (
                item["id"] == id &&
                item["zhonglei"] == 2 &&
                item["issku"] == 1
              ) {
                // 判断是否可以合并  规格产品
                let skuValArr = (item["sku_val_id"] + "").split(",");
                skuValArr.sort(function (a, b) {
                  return a - b;
                });
                if (sku_val_id == skuValArr.join(",")) {
                  // item['manualDiscount'] 1 无权益 2折扣 3抵扣 4手动改价 0 无权益
                  if (
                    item["manualDiscount"] == 1 &&
                    item["judgeCard"] == 0 &&
                    item["isActiveCostCard"] == 1
                  ) {
                    // 购物车中相同产品未使用权益 --- 能合并
                    this.C_open_order_specifications_save[i]["num"] += 1;
                    this.C_open_order_specifications_save[i]["subtotal"] = (
                      this.C_open_order_specifications_save[i]["num"] *
                      this.C_open_order_specifications_save[i]["price"]
                    ).toFixed(2);
                    productMerge = true;
                    return true;
                  } else {
                    // 购物车中相同产品已使用权益 --- 不能合并
                  }
                }
              }
            });
          }
          if (!productMerge) {
            // 不支持合并
            //现在输出选中产品或者服务并且有规格的最后对比的规格属性
            let findSkuProdoct = _self.C_open_order_specifications_attr.some(
              (item) => {
                let specifications_atrr = (item["sku_val_id"] + "").split(",");
                specifications_atrr.sort(function (a, b) {
                  return a - b;
                });
                if (sku_val_id == specifications_atrr.join(",")) {
                  let price = parseFloat((item["price"] / 100).toFixed(2));
                  if (_self.C_open_order_Specifications["memberPrice"]) {
                    price = parseFloat(
                      _self.C_open_order_Specifications["memberPrice"]
                    );
                  }
                  _self.cashier_open_order_service_choice[0]["price"] = price;
                  _self.cashier_open_order_service_choice[0]["sku"] =
                    item["sku"];
                  _self.cashier_open_order_service_choice[0]["sku_val_id"] =
                    item["sku_val_id"];
                  _self.cashier_open_order_service_choice[0]["num"] = 1;
                  _self.cashier_open_order_service_choice[0]["zhonglei"] = 2;
                  _self.cashier_open_order_service_choice[0]["subtotal"] =
                    price;
                  _self.cashier_open_order_service_choice[0][
                    "is_open_product_jishi"
                  ] = true;
                  _self.cashier_open_order_service_choice[0][
                    "isActiveCostCard"
                  ] = 1;
                  _self.cashier_open_order_service_choice[0]["judgeCard"] = 0;
                  _self.cashier_open_order_service_choice[0][
                    "specifications_id"
                  ] = item["id"]; // 规格id
                  if (_self.chooseProjuctFlag) {
                    _self.cashier_open_order_service_choice[0] = {
                      ..._self.cashier_open_order_service_choice[0],
                      ...new0bj,
                    };
                  }
                  _self.C_open_order_specifications_save.unshift(
                    JSON.parse(
                      JSON.stringify(_self.cashier_open_order_service_choice[0])
                    )
                  );
                  return true;
                }
              }
            );
            if (!findSkuProdoct) {
              return _self.$message("未找到当前规格产品");
            }
          }
          _self.cashier_open_order_Specifications = false;
        } else {
          return _self.$message("请选择完整规格");
        }
        this.Pending();
      }
      //确认服务的规格
      else {
        // 检测规格
        //  console.log(_self.C_open_order_specifications_name);
        // 循环获取已选择的规格项目个数，选择详情
        _self.C_open_order_specifications_name.forEach((item) => {
          // 子项二次循环，记录选中的规格
          item["sku_val"].forEach((sItem) => {
            if (sItem["is_show"] == true) {
              time_true++;
              specifications.push(sItem["id"]);
            }
          });
        });

        if (time_true != _self.C_open_order_specifications_name.length) {
          return _self.$message("请选择完整规格");
        }
        specifications.sort(function (a, b) {
          return a - b;
        });
        let skuData;
        let sku_val_id = specifications.join(",");
        // 获取规格数据
        _self.C_open_order_specifications_attr.some((item) => {
          let specifications_atrr = (item["sku_val_id"] + "").split(",");
          specifications_atrr.sort(function (a, b) {
            return a - b;
          });
          if (specifications_atrr.join(",") == sku_val_id) {
            skuData = this.deepCopy(item);
            return true;
          }
          return false;
        });
        if (!skuData) {
          return _self.$message("未找到当前规格服务");
        }
        // console.log(skuData);
        let new0bj = {};
        change_id = skuData["id"];
        /* 会员价开始 */
        let memberPrice = _self.C_open_order_Specifications["memberPrice"];
        skuData["price"] = memberPrice
          ? parseFloat(memberPrice)
          : skuData["price"];
        /* 会员价结束 */
        new0bj["price"] = skuData["price"];
        new0bj["sku"] = skuData["sku"];
        new0bj["sku_val_id"] = skuData["sku_val_id"];
        new0bj["issku"] = skuData["sku"];
        new0bj["id"] = _self.cashier_open_order_service_choice[0]["id"];
        new0bj["specifications_id"] = change_id; // 规格id
        new0bj["service_name"] =
          _self.cashier_open_order_service_choice[0]["service_name"];
        new0bj["zhonglei"] = 3;
        new0bj["num"] = 1;
        new0bj["buyNum"] = 1;
        new0bj["manualDiscount"] =
          _self.C_open_order_Specifications["manualDiscount"];
        new0bj["manualDiscountCard"] =
          _self.C_open_order_Specifications["manualDiscountCard"];
        new0bj.judgeCard = 0;
        // new0bj.costCard = {};
        // new0bj.costCard.card_info = "折扣卡？";
        new0bj["subtotal"] = new0bj["price"].toFixed(2);
        new0bj["isActiveCostCard"] = 1;
        new0bj["status"] = _self.serviceSpecificationStatus;
        _self.serviceSpecificationStatus = 1;
        new0bj.membercardChangeIndex = _self.membercardChangeIndex;
        //把当前折叠面板的index值存到数组中去
        if (_self.chooseProjuctFlag) {
          let data = _self.chooseProjuctService["data"];
          let cardInfo = _self.chooseProjuctService["cardInfo"];
          let balanceCardItem = {};
          console.warn(data.equityType);
          // 折扣权益
          if (data.equityType == 2) {
            // 导入权益
            if (cardInfo.cardSource < 0) {
              item = {};
              item.discount = data.discount;
              item.cardName = cardInfo.card_name;
              item.membercard_id = cardInfo.card_id;
              item.cardDetailsId = data.card_details_id;
              item.cardSource = cardInfo.cardSource;
              new0bj["manualDiscount"] = data.equityTypes;
              new0bj["manualDiscountCard"] = item;

              new0bj["discount"] = data.discount;
              new0bj["choosemembercardId"] = data.card_details_id;
              new0bj["chooseMemberCardCount"] = 0;
              new0bj["isActiveCostCard"] = 1;
              new0bj["subtotal"] = (
                new0bj["price"] * new0bj["discount"]
              ).toFixed(2);
              new0bj.judgeCard = 0;
              new0bj.costCard = {};
              new0bj.costCard.card_info = "不使用耗卡";
              _self.C_open_order_specifications_save.unshift(
                this.deepCopy(new0bj)
              );
              _self.carIndex = 0;
              _self.subtotalAmount();
              _self.loading = false;
            } else {
              // 充值卡权益
              // 判断金额是否充足
              let cardId = cardInfo["card_id"];
              let cardDetailsId = data["card_details_id"];
              let hasUsedMoney = 0;
              let balance = 0;
              this.balanceCard.some((item) => {
                if (item["id"] == cardId) {
                  balance = item["residuebalance"];
                  balanceCardItem = item;
                  return true;
                }
              });
              if (!balanceCardItem.id) {
                return this.$message({
                  message: "充值卡不存在",
                  center: true,
                  duration: 1500,
                });
              }
              this.balanceCardUse.forEach((item) => {
                // 耗卡id 等于当前id
                if (item["consumeCardId"] == cardId) {
                  // console.log(item);
                  hasUsedMoney += item["money"];
                }
              });
              let item = balanceCardItem;
              item.discount = data.discount;
              item.cardName = item.card_info;
              item.membercard_id = item.id;
              item.cardDetailsId = data.card_details_id;
              new0bj["manualDiscount"] = data.equityTypes;
              new0bj["manualDiscountCard"] = item;

              new0bj["discount"] = data.discount;
              new0bj["choosemembercardId"] = data.card_details_id;
              new0bj["chooseMemberCardCount"] = 0;
              new0bj["isActiveCostCard"] = 1;
              new0bj["subtotal"] = (
                new0bj["price"] * new0bj["discount"]
              ).toFixed(2);
              new0bj.judgeCard = 1;
              new0bj.costCard = {};
              new0bj.costCard.card_info = item.card_info;
              let needMoney = (
                new0bj["price"] *
                new0bj["discount"] *
                100
              ).toFixed(0);
              // console.log(balance, hasUsedMoney, needMoney, new0bj);
              if (!(balance - hasUsedMoney > needMoney)) {
                return this.$message({
                  message: "充值卡余额不足",
                  center: true,
                  duration: 1500,
                });
              }
              _self.C_open_order_specifications_save.unshift(
                this.deepCopy(new0bj)
              );
              //设置carIndex
              _self.carIndex = 0;
              _self.subtotalAmount();
              _self.loading = false;
            }
          } else if (data.equityType == 3) {
            // 抵扣权益
            let item = {
              cardName: cardInfo["card_name"],
              cardSource: cardInfo["cardSource"],
              card_type: cardInfo["cardtype"],
              discount: data["discount"] ? data["discount"] : "1",
              id: data["card_details_id"],
              indate: cardInfo["indateName"],
              isgive: data["givenum"] > 0 ? 1 : 2,
              maxnum: data["num"],
              membercard_id: cardInfo["card_id"],
              once_cardtype: cardInfo["once_cardtype"],
            };
            new0bj["manualDiscount"] = 3;
            new0bj["manualDiscountCard"] = item;
            new0bj["discount"] = item.discount;
            new0bj["choosemembercardId"] = data.card_details_id;
            new0bj["chooseMemberCardCount"] = 0;
            new0bj["isActiveCostCard"] = 1;
            new0bj["subtotal"] = (new0bj["price"] * new0bj["discount"]).toFixed(
              2
            );
            new0bj.judgeCard = 1;
            new0bj.costCard = {};
            new0bj.costCard.card_info = "不使用耗卡";
            // console.log(new0bj);
            _self.C_open_order_specifications_save.unshift(
              this.deepCopy(new0bj)
            );
            //设置carIndex
            _self.carIndex = 0;
            _self.subtotalAmount();
            _self.loading = false;
          }
          _self.cashier_open_order_Specifications = false;
          _self.chooseProjuctFlag = false;
        } else {
          _self.C_open_order_specifications_save.unshift(new0bj);
          //判断是否使用了优惠券
          if (_self.C_open_order_specifications_save.judgeCard == 3) {
            // console.log("现在在添加服务，添加服务")
            _self.C_open_order_specifications_save.couponCard = {};
            _self.isCouponCard = false;
            _self.C_open_order_specifications_save.judgeCard = 0;
            _self.assignGoods = false;
          }
          _self.cashier_open_order_Specifications = false;
          _self.chooseProjuctFlag = false;
        }
        return this.Pending();
      }
    },

    //取消规格
    over_specifications: function (data) {
      let _self = this;
      _self.chooseProjuctFlag = false;
      this.cashier_open_order_Specifications = false;
      this.serviceSpecificationStatus = 1;
      // 清除扣卡数据
      _self.chooseProjuctService = {};
    },

    //开单详情的每个订单删除按钮
    open_details_price_del: function (index) {
      this.$message.success("删除成功");
      this.C_open_order_specifications_save.splice(index, 1);
      return this.Pending();
      // 只需删除即可 无需其他操作；
    },

    //解决删除耗卡时归还金额的问题
    returnCostMoney: function (index) {
      let _self = this;
      var carData = _self.C_open_order_specifications_save;
      _self.balanceCard.forEach((item) => {
        if (item.id == carData[index].costCard.id) {
          item.residuebalance =
            item.residuebalance + Number(carData[index].subtotal) * 100;
        }
      });
      carData.splice(index, 1);
    },

    //选择服务人员的点击事件  ji_shi_id用来判断从保存技术数组中删除服务人员的值
    //ji_shi_zhanshi数组用来存放在选择服务人员时候被选中的服务人员信息，按照点击的顺序来排序
    chioce_ji_shi_name: function (index, data, ji_shi_id) {
      let _self = this;
      let item = _self.ji_shis[index];
      if (data) {
        // 选中
        _self.ji_shi_zhanshi.push(item);
      } else {
        // 取消
        _self.ji_shi_zhanshi.some((it, j) => {
          if (it["id"] == ji_shi_id) {
            _self.ji_shi_zhanshi.splice(j, 1);
            return true;
          }
        });
      }
      _self.$forceUpdate();
    },

    //选择点客的点击事件ji_shi_id用来判断
    chioce_ji_shi_guest: function (index, data, ji_shi_id) {
      let _self = this;
      for (let i = 0; i < _self.ji_shis.length; i++) {
        if (index == i) {
          if (_self.ji_shis[i]["is_choice_jishi"] == false) {
            _self.ji_shis[i]["is_choice_jishi"] = data;
            _self.ji_shis[i]["is_guest"] = data;
            _self.ji_shi_zhanshi.push(_self.ji_shis[i]);
          } else {
            for (let j = 0; j < _self.ji_shi_zhanshi.length; j++) {
              if (_self.ji_shi_zhanshi[j]["id"] == ji_shi_id) {
                _self.ji_shi_zhanshi[j]["is_guest"] = data;
              }
            }
          }
        }
      }
      _self.ji_shi_zhanshi.some((it, j) => {
        if (it["id"] == ji_shi_id) {
          _self.ji_shi_zhanshi[j]["is_guest"] = data;
          return true;
        }
      });
      _self.$forceUpdate();
    },

    //确认服务人员
    //ji_shi_zhanshi_name是存入C_open_order_specifications_save的key预存内容，有展示的服务人员的name组合（分顺序）
    open_save_technician: function () {
      // console.log("确认服务人员信息")
      let _self = this;
      _self.ji_shi = false;
      let index = _self.which_server_technician; //index是哪一个服务的替换值
      let ji_shi_zhanshi_name = "";
      let technicianArr = [];
      for (let i = 0; i < _self.ji_shi_zhanshi.length; i++) {
        let item = {};
        if (_self.ji_shi_zhanshi[i]["is_guest"] == true) {
          item["id"] = _self.ji_shi_zhanshi[i]["id"];
          item["nickname"] = _self.ji_shi_zhanshi[i]["nickname"];
          item["dot"] = 1;
        } else {
          item["id"] = _self.ji_shi_zhanshi[i]["id"];
          item["nickname"] = _self.ji_shi_zhanshi[i]["nickname"];
          item["dot"] = 0;
        }
        ji_shi_zhanshi_name += item["nickname"] + "、";
        technicianArr.push(item);
      }
      ji_shi_zhanshi_name = ji_shi_zhanshi_name.substr(
        0,
        ji_shi_zhanshi_name.length - 1
      );
      _self.pi_is_ji_name = ji_shi_zhanshi_name;
      _self.C_open_order_specifications_save[index]["technician_id"] =
        technicianArr;
      _self.C_open_order_specifications_save[index]["technician_name"] =
        ji_shi_zhanshi_name;
      _self.ji_shi_zhanshi = [];
      _self.ji_shi_save = [];
    },

    //取消确认服务人员
    open_over_technician: function () {
      let _self = this;
      _self.ji_shi = false;
      _self.ji_shi_zhanshi = [];
      _self.ji_shi_save = [];
      for (let i = 0; i < _self.ji_shis.length; i++) {
        _self.ji_shis[i]["is_choice_jishi"] = false;
        _self.ji_shis[i]["is_guest"] = false;
      }
      _self.$forceUpdate();
    },

    //选择销售获取销售接口-开单--不用
    sellsman: function () {
      this.loading = false;
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Staff/sellsman",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
      });
    },

    //开单--选择销售
    change_xiaoshou: function (index, isMerge = false) {
      this.loading = false;
      if (!isMerge) {
        this.xiao_shou = true;
      }
      var _self = this;
      let salemenArr = _self.C_open_order_specifications_save[index].salesmen;
      _self.xiao_shou_zhanshi = [];
      _self.which_server_Sale = index;
      $.ajax({
        url: _self.url + "/android/Staff/sellsman",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            // console.log(res);
            _self.xiaoshous = res["data"];
            if (salemenArr?.length != 0) {
              let obj = {};
              for (let i = 0; i < _self.xiaoshous.length; i++) {
                let flag = true;
                for (let j = 0; j < salemenArr.length; j++) {
                  if (salemenArr[j] == _self.xiaoshous[i].id) {
                    _self.xiaoshous[i]["is_choice_xiaoshou"] = true;
                    flag = false;
                    obj[salemenArr[j]] = _self.xiaoshous[i];
                    break;
                  }
                }
                if (flag) {
                  _self.xiaoshous[i]["is_choice_xiaoshou"] = false;
                }
              }
              salemenArr.forEach((item) => {
                _self.xiao_shou_zhanshi.push(obj[item]);
              });
            } else {
              for (let i = 0; i < _self.xiaoshous.length; i++) {
                _self.xiaoshous[i]["is_choice_xiaoshou"] = false;
              }
            }
            // console.log(_self.xiaoshous);
            _self.loading = false;
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
      });
    },

    //选择销售姓名
    chioce_xiaoshou: function (index, data, xiao_shou_id) {
      let _self = this;
      let item = _self.xiaoshous[index];
      if (data) {
        _self.xiao_shou_zhanshi.push(item);
      } else {
        _self.xiao_shou_zhanshi.some((it, j) => {
          if (it["id"] == xiao_shou_id) {
            _self.xiao_shou_zhanshi.splice(j, 1);
            return true;
          }
        });
      }
      _self.$forceUpdate();
    },

    //确定销售
    xiaoshou_save: function () {
      let _self = this;
      let index = _self.which_server_Sale; //index是哪一个服务的替换值
      let xiao_shou_zhanshi_name = "";
      for (let i = 0; i < _self.xiao_shou_zhanshi.length; i++) {
        if (i == _self.xiao_shou_zhanshi.length - 1) {
          xiao_shou_zhanshi_name += _self.xiao_shou_zhanshi[i]["nickname"];
        } else {
          xiao_shou_zhanshi_name +=
            _self.xiao_shou_zhanshi[i]["nickname"] + "、";
        }
      }
      _self.pi_is_xiao_name = xiao_shou_zhanshi_name;
      //销售拼接
      _self.C_open_order_specifications_save[index]["saler_name"] =
        xiao_shou_zhanshi_name;
      // _self.xiao_shou_save[i] = [];
      for (let i = 0; i < _self.xiao_shou_zhanshi.length; i++) {
        if (_self.xiao_shou_zhanshi[i]["is_choice_xiaoshou"] == true) {
          _self.xiao_shou_save.push(_self.xiao_shou_zhanshi[i]["id"]);
        }
      }
      _self.C_open_order_specifications_save[index]["salesmen"] =
        _self.xiao_shou_save;
      this.xiaoshou_over();
    },

    //取消确定的销售
    xiaoshou_over: function () {
      let _self = this;
      _self.xiao_shou = false;
      _self.xiao_shou_zhanshi = [];
      _self.xiao_shou_save = [];
      for (let i = 0; i < _self.xiaoshous.length; i++) {
        _self.xiaoshous[i]["is_choice_xiaoshou"] = false;
      }
      _self.$forceUpdate();
    },

    //选择批量
    batch_choice: function (index) {
      this.cardIndex = index;
      this.pi_liang = true;
      let _self = this;
      let res_json = _self.C_open_order_specifications_save[index];

      if (res_json["zhonglei"] == 2) {
        _self.order_is_show_jishi = false;
        _self.order_is_show_both = false;
      } else {
        _self.order_is_show_jishi = true;
        _self.order_is_show_both = true;
      }
      _self.pi_is_ji_shi = res_json["technician_id"];
      _self.pi_is_xiaoshou = res_json["salesmen"];
    },

    //应用服务人员到所有服务
    piliang_jishi: function () {
      let _self = this;
      var index = _self.cardIndex;
      let new_ji_shi =
        _self.C_open_order_specifications_save[index]["technician_id"];
      let new_ji_name =
        _self.C_open_order_specifications_save[index]["technician_name"];

      if (new_ji_shi && new_ji_name) {
        for (
          let i = 0;
          i < _self.C_open_order_specifications_save.length;
          i++
        ) {
          if (_self.C_open_order_specifications_save[i]["zhonglei"] == 2) {
          } else {
            _self.C_open_order_specifications_save[i]["technician_id"] =
              JSON.parse(JSON.stringify(new_ji_shi));
            _self.C_open_order_specifications_save[i]["technician_name"] =
              JSON.parse(JSON.stringify(new_ji_name));
          }
        }
        _self.pi_liang = false;
      } else {
        _self.$message({
          type: "error",
          message: "该服务未选择" + globalTechnicianName,
          duration: 1500,
        });
      }
      _self.$forceUpdate();
    },

    //开单应用销售到所有服务
    piliang_xiaoshou: function () {
      let _self = this;
      var index = _self.cardIndex;
      let new_xiao_shou =
        _self.C_open_order_specifications_save[index]["salesmen"];
      let new_xiao_name =
        _self.C_open_order_specifications_save[index]["saler_name"];
      // let new_xiao_shou=_self.pi_is_xiaoshou;
      if (new_xiao_shou && new_xiao_shou) {
        for (
          let i = 0;
          i < _self.C_open_order_specifications_save.length;
          i++
        ) {
          _self.C_open_order_specifications_save[i]["salesmen"] = JSON.parse(
            JSON.stringify(new_xiao_shou)
          );
          _self.C_open_order_specifications_save[i]["saler_name"] = JSON.parse(
            JSON.stringify(new_xiao_name)
          );
        }
        _self.pi_liang = false;
      } else {
        _self.$message({
          type: "error",
          message: "该服务未选择销售",
          duration: 1500,
        });
      }

      _self.$forceUpdate();
    },

    //开单应用销售和服务人员到所有服务
    piliang_both: function () {
      var _self = this;
      var index = _self.cardIndex;
      let new_ji_shi =
        _self.C_open_order_specifications_save[index]["technician_id"];
      let new_ji_name =
        _self.C_open_order_specifications_save[index]["technician_name"];
      let new_xiao_shou =
        _self.C_open_order_specifications_save[index]["salesmen"];
      let new_xiao_name =
        _self.C_open_order_specifications_save[index]["saler_name"];
      if (new_ji_name && new_ji_shi && new_xiao_name && new_xiao_shou) {
        for (
          let i = 0;
          i < _self.C_open_order_specifications_save.length;
          i++
        ) {
          if (_self.C_open_order_specifications_save[i]["zhonglei"] == 2) {
            _self.C_open_order_specifications_save[i]["salesmen"] = JSON.parse(
              JSON.stringify(new_xiao_shou)
            );
            _self.C_open_order_specifications_save[i]["saler_name"] =
              JSON.parse(JSON.stringify(new_xiao_name));
          } else {
            _self.C_open_order_specifications_save[i]["technician_id"] =
              JSON.parse(JSON.stringify(new_ji_shi));
            _self.C_open_order_specifications_save[i]["technician_name"] =
              JSON.parse(JSON.stringify(new_ji_name));
            _self.C_open_order_specifications_save[i]["salesmen"] = JSON.parse(
              JSON.stringify(new_xiao_shou)
            );
            _self.C_open_order_specifications_save[i]["saler_name"] =
              JSON.parse(JSON.stringify(new_xiao_name));
          }
        }
        _self.pi_liang = false;
      } else {
        _self.$message({
          type: "error",
          message: "该服务未选" + globalTechnicianName + "或销售",
          duration: 1500,
        });
      }

      _self.$forceUpdate();
    },

    // 搜索会员接口 在生命周期里调用，不在0
    getIndex: function (key) {
      this.isactive = key;
    },

    vue_cashier_open_order_choice_server: function (key) {
      this.isactive2 = key;
    },

    show_choice: function (index, value) {
      // alert(index)
      this.seen1 = index;
      this.seen2 = index;
    },

    //弹框的方法

    chioce_dianke: function (index) {
      // alert(index)
    },

    over_save_over: function () {
      this.ji_shi = false;
    },

    piliang_over: function () {
      this.pi_liang = false;
    },

    piliang_save: function () {},

    //左侧选单到右侧
    kd_not_use_youhui: function () {},
    kd_add_use_youhui: function () {},
    kd_ka_shoukuan: function () {
      // alert('开单的页面');
    },

    //以上是收银台普通开单函数

    //    还缺收款，选择会员还需添加标题

    //以下是办卡的新加的函数
    //办卡的函数

    bk_ka_jian: function () {
      if (this.bk_server_number_ka > 1) {
        this.bk_server_number_ka -= 1;
      }
    },
    bk_ka_zeng: function () {
      if (this.bk_server_number_ka < 10) {
        this.bk_server_number_ka += 1;
      }
    },

    //办卡添加服务的函数
    bk_xuanze_tianjia_fuwu: function (index) {
      this.bk_isAddfuwu = index;
      if (index == 0) {
        this.bk_server_biaoti_name = this.bk_server_biaoti_name1;
      } else if (index == 1) {
        this.bk_server_biaoti_name = this.bk_server_biaoti_name2;
      } else if (index == 2) {
        this.bk_server_biaoti_name = this.bk_server_biaoti_name3;
      } else {
        this.bk_server_biaoti_name = this.bk_server_biaoti_name4;
      }
    },

    bk_tianjia_zengson: function () {
      this.bk_tianjia_fuwu = true;
    },
    bk_tianjia_over: function () {
      this.bk_tianjia_fuwu = false;
    },
    bk_tianjia_save: function () {},
    bk_xuanze_youhui_power: function () {
      this.bk_youhui_power = true;
    },

    bk_xuanze_youhui: function () {
      this.bk_is_xuanze_youhui = true;
    },
    bk_quxiao_xuanze_youhui: function () {
      this.bk_is_xuanze_youhui = false;
    },
    bk_xuanze_youhui_save: function () {
      this.bk_is_xuanze_youhui = false;
    },
    bk_change_xiaoshou: function () {
      this.bk_xiao_shou = true;
      var _self = this;
      let salemenArr = _self.bk_xiao_shou_zhanshi;
      _self.xiao_shou_zhanshi = salemenArr;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/android/Staff/sellsman",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.xiaoshous = res.data;
            // for (let i = 0; i < _self.xiaoshous.length; i++) {
            //     _self.xiaoshous[i]['is_choice_xiaoshou'] = false;
            // }

            if (salemenArr?.length != 0) {
              for (let i = 0; i < _self.xiaoshous.length; i++) {
                let flag = true;
                for (let j = 0; j < salemenArr.length; j++) {
                  if (salemenArr[j].id == _self.xiaoshous[i].id) {
                    _self.xiaoshous[i]["is_choice_xiaoshou"] = true;
                    flag = false;
                    break;
                  }
                }
                if (flag) {
                  _self.xiaoshous[i]["is_choice_xiaoshou"] = false;
                }
              }
            } else {
              for (let i = 0; i < _self.xiaoshous.length; i++) {
                _self.xiaoshous[i]["is_choice_xiaoshou"] = false;
              }
            }
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },

    bk_chioce_xiaoshou: function (index) {
      alert(index);
    },
    bk_xiaoshou_over: function () {
      this.bk_xiao_shou = false;
      var _self = this;
      //上来给销售页面展示清空
      _self.SalesShow = "";
      var arrlength = _self.bk_xiao_shou_zhanshi.length;
      // _self.zhkxiaoshous=[];
      if (_self.bk_xiao_shou_zhanshi) {
        for (let i = 0; i < arrlength; i++) {
          if (i == arrlength - 1) {
            _self.SalesShow += _self.bk_xiao_shou_zhanshi[i]["nickname"];
          } else {
            _self.SalesShow += _self.bk_xiao_shou_zhanshi[i]["nickname"] + "、";
          }
        }
      } else {
        _self.SalesShow = "";
      }
      _self.bk_xiao_shou = false;
    },
    bk_xiaoshou_save: function () {
      var _self = this;
      this.bk_xiao_shou = false;
      //确定之前清空销售页面展示。
      _self.SalesShow = "";
      // console.log('复制的销售', _self.xiao_shou_zhanshi);
      if (_self.xiao_shou_zhanshi) {
        _self.bk_xiao_shou_zhanshi = [];
        _self.bk_xiao_shou_zhanshi = JSON.parse(
          JSON.stringify(_self.xiao_shou_zhanshi)
        ); //深拷贝
        var arrlength = _self.bk_xiao_shou_zhanshi.length;
        if (_self.bk_xiao_shou_zhanshi) {
          for (let i = 0; i < arrlength; i++) {
            if (i == arrlength - 1) {
              _self.SalesShow += _self.bk_xiao_shou_zhanshi[i]["nickname"];
            } else {
              _self.SalesShow +=
                _self.bk_xiao_shou_zhanshi[i]["nickname"] + "、";
            }
          }
        } else {
          _self.SalesShow = "";
        }
      } else {
        _self.bk_xiao_shou_zhanshi = _self.xiao_shou_zhanshi;
        _self.SalesShow = "";
      }
      _self.xiao_shou_zhanshi = [];
      _self.zhk_xiao_shou = false;
    },

    //办卡会员信息删除按钮
    delVipCardMemberInfo: function () {
      let _self = this;
      _self.isVipCardMember = false;
      _self.isVipCouponCard = false; //现金券回复初始设置
      _self.memberObjData = [];
      _self.memberObj = {};
      _self.billGiftData = [];
      _self.showGiftData = {
        allPrice: 0,
        serverNum: 0,
        productNum: 0,
      };
      this.helpStaffArr[this.isactive1] = [];
      //把手动改价的价格付给总价
      _self.paymentMoney = _self.manualPrice;
    },

    manualPriceInput: function (value) {
      let _self = this;
      if (_self.isVipCouponCard) {
        _self.isVipCouponCard = false;
        _self.cardTemp.couponCard = {};
      }
    },

    // //办卡选择耗卡
    // cardChooseCostCard:function(){
    //     console.log("收银台-办卡，选择耗卡")
    //     let _self=this;
    //     if(this.memberObj.phone){
    //         this.isCardChooseCostCard=true;
    //         $.ajax({
    //             url: _self.url + "/android/vip/getBalanceCard",
    //             type: 'post',
    //             data: {
    //                 storeid: _self.loginInfo.storeid,
    //                 merchantid: _self.loginInfo.merchantid,
    //                 memberid: _self.memberObj.id, //会员id
    //             },
    //             success: function (res) {
    //                 // var res = JSON.parse(res);
    //                 if (res.code == 1) {
    //                     console.log('1234', res);
    //                     _self.balanceCard = res.data;
    //                 }
    //             },
    //             error: function (error) {
    //                 _self.loading = false;
    //                 console.log(error);
    //             }
    //         })
    //     }else{
    //         _self.$message({
    //             type: "warning",
    //             message: "请先登会员",
    //             duration: 1500
    //         })
    //     }

    // },

    // cardCostCard:function(num, item, index){
    //     //num 1不使用耗卡 0 默认账户  2耗卡
    //     console.log("办卡使用耗卡");
    //     switch(num){
    //         case 1:
    //             this.handleDefaultCostCard(num);
    //             break;
    //         case 0:
    //             this.handleDefaultCostCard(num);
    //             break;
    //         case 2:
    //             this.handleCardCostCard(item,index);
    //             break;
    //     }
    // },

    // //办卡处理不使用耗卡和默认账户
    // handleDefaultCostCard:function(num){
    //     console.log("处理办卡不使用耗卡和默认账户"+num);
    //     //num  1不使用耗卡  0默认账户
    //     if(num==0){
    //         this.isShowCostCard=num;
    //         this.isCardChooseCostCard=false;
    //         this.cardTemp.costCard={};
    //         this.cardTemp.costCard.consumeCardId=0;
    //         this.cardTemp.costCard.consumeCard=2;
    //         console.log(this.cardTemp);
    //     }else{
    //         this.isShowCostCard=num;
    //         this.cardTemp.costCard={};
    //         this.isCardChooseCostCard=false;
    //     }
    // },

    // handleCardCostCard:function(item,index){
    //     console.log(typeof this.paymentMoney);
    //     if(item.residuebalance>Number(this.paymentMoney)*100){
    //         this.isShowCostCard=2;
    //         this.cardTemp.costCard={};
    //         this.cardTemp.costCard.consumeCardId=item.id;
    //         this.cardTemp.costCard.consumeCard=2;
    //         this.cardTemp.costCard.costName=item.card_info;
    //         this.isCardChooseCostCard=false;
    //     }else{
    //         this.$message({
    //             type: "warning",
    //             message: "耗卡余额不足",
    //             duration: 1500
    //         })
    //     }
    // },

    // cancelCardCostCard:function(){
    //     this.isCardChooseCostCard=false;
    // },

    //充值选择销售弹框

    cz_chongzhi_xuanze_xiaoshou: function () {
      var _self = this;
      _self.cz_ischongxiao = true;
      _self.loading = true;
      let salemenArr = _self.rechange_xiao_shou_zhanshi;
      _self.xiao_shou_zhanshi = salemenArr;
      $.ajax({
        url: _self.url + "/android/Staff/sellsman",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            // console.log('销售', res);
            _self.changexiaoshous = res.data;
            if (salemenArr?.length != 0) {
              for (let i = 0; i < _self.changexiaoshous.length; i++) {
                let flag = true;

                for (let j = 0; j < salemenArr.length; j++) {
                  if (salemenArr[j].id == _self.changexiaoshous[i].id) {
                    _self.changexiaoshous[i]["is_choice_xiaoshou"] = true;
                    flag = false;
                    break;
                  }
                }
                if (flag) {
                  _self.changexiaoshous[i]["is_choice_xiaoshou"] = false;
                }
              }
            } else {
              for (let i = 0; i < _self.changexiaoshous.length; i++) {
                _self.changexiaoshous[i]["is_choice_xiaoshou"] = false;
              }
            }
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },
    //充卡确定销售
    cz_xiaoshou_save: function () {
      var _self = this;
      //确定之前清空销售页面展示。
      _self.rechangeSalesShow = "";
      if (_self.xiao_shou_zhanshi) {
        _self.rechange_xiao_shou_zhanshi = [];
        _self.rechange_xiao_shou_zhanshi = JSON.parse(
          JSON.stringify(_self.xiao_shou_zhanshi)
        ); //深拷贝
        var arrlength = _self.rechange_xiao_shou_zhanshi.length;
        if (_self.rechange_xiao_shou_zhanshi) {
          for (let i = 0; i < arrlength; i++) {
            if (i == arrlength - 1) {
              _self.rechangeSalesShow +=
                _self.rechange_xiao_shou_zhanshi[i]["nickname"];
            } else {
              _self.rechangeSalesShow +=
                _self.rechange_xiao_shou_zhanshi[i]["nickname"] + "、";
            }
          }
        } else {
          _self.rechangeSalesShow = "";
        }
      } else {
        _self.rechange_xiao_shou_zhanshi = _self.xiao_shou_zhanshi;
        _self.rechangeSalesShow = "";
      }

      _self.xiao_shou_zhanshi = [];
      _self.cz_ischongxiao = false;
    },
    //充值取消销售
    cz_chongxiao_over: function () {
      var _self = this;
      //上来给销售页面展示清空
      _self.rechangeSalesShow = "";
      var arrlength = _self.rechange_xiao_shou_zhanshi.length;
      // _self.zhkxiaoshous=[];
      if (_self.rechange_xiao_shou_zhanshi) {
        for (let i = 0; i < arrlength; i++) {
          if (i == arrlength - 1) {
            _self.rechangeSalesShow +=
              _self.rechange_xiao_shou_zhanshi[i]["nickname"];
          } else {
            _self.rechangeSalesShow +=
              _self.rechange_xiao_shou_zhanshi[i]["nickname"] + "、";
          }
        }
      } else {
        _self.rechangeSalesShow = "";
      }
      for (let i = 0; i < _self.changexiaoshous.length; i++) {
        _self.changexiaoshous[i]["is_choice_xiaoshou"] = false;
      }
      _self.$forceUpdate();
      _self.cz_ischongxiao = false;
    },
    //选择销售框
    cz_chioce_xiaoshou: function (index, data, xiao_shou_id) {
      let _self = this;
      for (let i = 0; i < _self.changexiaoshous.length; i++) {
        if (index == i) {
          if (data == true) {
            _self.changexiaoshous[i]["is_choice_xiaoshou"] = data;
            _self.xiao_shou_zhanshi.push(_self.changexiaoshous[i]);
          } else {
            for (let j = 0; j < _self.xiao_shou_zhanshi.length; j++) {
              if (_self.xiao_shou_zhanshi[j]["id"] == xiao_shou_id) {
                _self.xiao_shou_zhanshi.splice(j, 1);
              }
            }
          }
        }
      }
      // console.log(_self.xiao_shou_zhanshi);
      _self.$forceUpdate();
    },

    //充值页面添加服务
    cz_chongzhi_tianjia_zengson: function () {
      this.cz_chong_add_server = true;
    },
    cz_xuanze_tianjia_fuwu: function (index) {
      this.cz_ischong_Addfuwu = index;
      if (index == 0) {
        this.cz_server_biaoti_name = this.cz_server_biaoti_name1;
      } else if (index == 1) {
        this.cz_server_biaoti_name = this.cz_server_biaoti_name2;
      } else if (index == 2) {
        this.cz_server_biaoti_name = this.cz_server_biaoti_name3;
      } else {
        this.cz_server_biaoti_name = this.cz_server_biaoti_name4;
      }
    },
    cz_chongzhi_tianjia_over: function () {
      this.cz_chong_add_server = false;
    },
    cz_chongzhi_tianjia_save: function () {
      this.cz_chong_add_server = false;
    },

    //    选择销售
    // cz_change_xiaoshou: function () {
    //     this.cz_xiao_shou = true;
    // },

    kd_list_chongzhika: function (index, item) {
      this.kd_czk_list = index;
    },

    // 收银台充值卡结束

    //以下是充卡的函数
    zhk_jianshao: function () {
      if (this.zhk_server_number > 1) {
        this.zhk_server_number -= 1;
      }
    },
    zhk_zengjia: function () {
      if (this.zhk_server_number < 10) {
        this.zhk_server_number += 1;
      }
    },

    zhk_chioce_server: function (key) {
      this.zhk_isactive2 = key;
    },

    zhk_over_open: function () {
      this.zhk_server_name = [];
    },

    zhk_add_server: function (index) {
      try {
        let server_name_get = this.zhk_todos4.slice(index, index + 1);
        if (this.zhk_server_name.length == 0) {
          server_name_get[0]["open_num"] = 1;
          this.zhk_server_name.push(server_name_get[0]);
        } else {
          let server = false;
          for (let i of this.zhk_server_name) {
            if (i.word == server_name_get[0].word) {
              server = true;
            }
          }
          if (server) {
            for (let j of this.zhk_server_name) {
              if (j.word == server_name_get[0].word) {
                j.open_num = j.open_num + 1;
              }
            }
          } else {
            server_name_get[0]["open_num"] = 1;
            this.zhk_server_name.push(server_name_get[0]);
          }
        }
        this.zhk_server_name.push(0);
        this.zhk_server_name.splice(this.zhk_server_name.length - 1, 1);
      } catch (error) {}
    },

    zhk_open_details_price_del: function (index) {
      // alert(index);
      this.zhk_server_name.splice(index, 1);
    },
    zhk_change_xiaoshou: function () {
      this.zhk_xiao_shou = true;
    },
    zhk_chioce_xiaoshou: function (index) {
      alert(index);
    },
    zhk_xiaoshou_over: function () {
      this.zhk_xiao_shou = false;
    },
    zhk_xiaoshou_save: function () {
      this.zhk_xiao_shou = false;
    },
    zhk_xuanzhong_color: function (index) {
      this.zhk_seen1 = index;
    },
    zhk_change_sex_color1: function () {
      this.is_zhk_sex = true;
    },
    zhk_change_sex_color2: function () {
      this.is_zhk_sex = false;
    },
    zhk_kd_shoukuan: function () {
      alert("充卡开单收款");
    },

    /**
     *
     * 收银台 开单 办卡 充值 收款 会员搜索查询
     *
     ***/

    //实体卡登录  0527502818
    loadEntityCard: function (card) {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/Member/readEntityCard",
        type: "post",
        data: {
          card_voucher: card,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          //定义的变量赋值，给
          if (res.code == 1 && res.data.length != 0) {
            // console.log('会员信息', res);
            _self.memberInformation = res.data[0];
            switch (_self.isactive1) {
              case 0:
                _self.memberInfo = res.data[0];
                // _self.is_dynamic = true;
                _self.getBalanceCard();
                _self.getMemberStampCardInfo();
                _self.billingType = 2;
                break;
              case 1:
                _self.memberObjData = res.data;
                _self.memberObj.name = res.data[0].member_name; //办卡显示新样式
                _self.isVipCardMember = true;
                break;
              case 2:
                _self.cz_search_keyword = "";
                _self.cz_huiyuanxinxi = _self.memberInformation;
                _self.getVipRechargeData(_self.memberInformation.phone);
                break;
              case 3:
                _self.receiptMember = res.data[0];
                break;
            }
            _self.loading = false;
          } else {
            if (_self.isactive1 != 3) {
              _self.loading = false;
              _self.$message.error({
                message: "未找到此会员信息",
                duration: 1500,
              });
            }
          }
        },
        error: function (e) {},
      });
    },

    // enter
    bindInquire: function (phone) {
      var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!phone) {
        this.$message.error({
          message: "输入手机号,登录会员",
          duration: 1500,
        });
      } else if (phone.length != "11" && !reg.test(phone)) {
        if (phone.length == 10) {
          this.loadEntityCard(phone);
        } else {
          // this.$message.error({
          //     message: '会员手机号输入有误',
          //     duration: 1500,
          // })

          this.memberSearch(phone);
        }
      } else {
        this.memberSearch(phone);
      }
    },

    //  输入搜索
    //isactive1 0: 开单 1: 办卡  2: 充值  3:直接收款   4: 充卡   5: 核销
    bindInquireMember: function (phone) {
      // console.log(phone)
      var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      // var reg=/^[0-9]{7}$/;
      if (phone.length == 11 && reg.test(phone)) {
        this.memberSearch(phone);
      } else {
        if (this.isactive1 == 2 && phone.length != "11") {
          this.cz_chongzhika = [];
        } else {
          if (phone.length == 10 && this.isactive1 == 3) {
            this.bindInquire(phone);
          }
        }
      }
    },

    directInputMember: function (phone) {
      let _self = this;
      // var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (phone.length == 7) {
        $.ajax({
          url: _self.url + "/android/vip/memberSearch",
          type: "post",
          data: {
            keyword: phone,
            merchantid: _self.loginInfo.merchantid,
            storeid: _self.loginInfo.storeid,
          },
          success: function (res) {
            // var res = JSON.parse(res);
            //定义的变量赋值，给

            if (res.code == 1 && res.data.length != "") {
              _self.receiptMember = res.data[0];
              _self.loading = false;
            } else {
              _self.loading = false;
              // _self.receiptMember={}
              // _self.$message.error({
              //     message: '会员手机号输入有误',
              //     duration: 1500,
              // })
            }
          },
          error: function (error) {},
        });
      }
      if (phone.length == 0) {
        _self.receiptMember = {};
      }
    },
    directEnterMember: function (phone) {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/vip/memberSearch",
        type: "post",
        data: {
          keyword: phone,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          //定义的变量赋值，给

          if (res.code == 1 && res.data.length != "") {
            _self.receiptMember = res.data[0];
            _self.loading = false;
          } else {
            _self.loading = false;
            _self.receiptMember = {};
            _self.$message.error({
              message: "会员手机号输入有误",
              duration: 1500,
            });
          }
        },
        error: function (error) {},
      });
    },

    querySearchAsync: function (querystring, cb) {
      let _self = this;
      if (typeof querystring == "undefined") {
        return cb([]);
      }
      this.loading = true;

      var reg0 = /^1(3|4|5|6|7|8|9)\d{8}$/;
      var reg = /^[0-9]{10}$/;
      var reg1 = /^[0-9]{14}$/;
      if (querystring.length == 10 && !reg0.test(querystring)) {
        this.loadEntityCard(querystring);
        cb([]);
        return false;
      }
      if (reg1.test(querystring)) {
        this.memberSearch(querystring);
        cb([]);
        return false;
      }
      if (querystring.length >= 4) {
        $.ajax({
          url: _self.url + "/android/vip/memberSearch",
          type: "post",
          data: {
            keyword: querystring,
            merchantid: _self.loginInfo.merchantid,
            storeid: _self.loginInfo.storeid,
          },
          success: function (res) {
            // var res = JSON.parse(res);
            //定义的变量赋值，给

            _self.memberInformation = res.data;
            if (res.code == 1 && res.data.length != "") {
              cb(res.data);
            } else {
              cb([]);
              // _self.$message.error({
              //     message: '未找到此会员信息',
              //     duration: 1500,
              // })
            }
          },
          error: function (error) {
            cb([]);
          },
          complete: () => {
            _self.loading = false;
          },
        });
      } else {
        cb([]);
        _self.loading = false;
      }
    },
    //会员搜索
    bindSearch: function () {
      if (!this.vipSearch) return;
      this.searchCurrentPage = 1;
      this.getMember();
    },

    queryEnterMember: function (querystring) {
      var reg = /^[0-9]{10}$/;
      var reg1 = /^[0-9]{14}$/;
      if (reg.test(querystring)) {
        this.loadEntityCard(querystring);
        return false;
      } else if (reg1.test(querystring)) {
        this.memberSearch(querystring);
        return false;
      } else {
        this.memberSearch(querystring);
      }
    },

    //自动完成选择会员列表
    handleMemberSelect: function (data) {
      let _self = this;
      this.bindDelMemberInfo();
      switch (_self.isactive1) {
        case 0:
          // 开单
          _self.memberInfo = data;
          _self.memberSearchSuccess = 1;
          // 获取余额卡信息
          _self.getBalanceCard(1);
          // 获取扣卡信息
          _self.getMemberStampCardInfo(1);
          /* if (_self.C_open_order_specifications_save?.length <= 0) {
            // 获取权益信息
            _self.billingType = 2;
          } */
          _self.billingType = 2;
          break;
        case 1:
          // 办卡
          // _self.memberObjData = data;
          // _self.memberObj.name = data.member_name;
          _self.memberObjData = [data];
          _self.isVipCardMember = true;
          _self.memberObj.name = data.member_name;
          _self.memberObj.phone = data.phone;
          _self.memberObj.id = data.id;
          break;
        case 2:
          // 充值
          _self.cz_huiyuanxinxi = data;
          _self.is_sechargeMember = true;
          _self.getVipRechargeData(data.phone, () => {
            if (_self.cz_chongzhika.length > 0) {
              _self.cz_list_getPhoneInfo(_self.cz_chongzhika[0]);
            }
          });
          break;
        case 3:
          // 收银台直接收款
          _self.receiptMember = data;
          break;
      }
    },

    //输入搜索
    bindInquireMember11: function (keyword) {
      let _self = this;
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/vip/memberSearch",
        type: "post",
        data: {
          keyword: keyword,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          //定义的变量赋值，给

          _self.memberInformation = res.data[0];
          if (res.code == 1 && res.data.length != "") {
            // switch (_self.isactive1) {
            //     case 0:
            //         console.log('现在测有没有给memberInfo赋值');
            //         _self.memberInfo = res.data[0];
            //         // _self.is_dynamic = true;
            //         _self.getBalanceCard();
            //         _self.getMemberStampCardInfo();
            //         _self.billingType = 2;
            //         break;
            //     case 1:
            //         _self.memberObjData = res.data;
            //         _self.memberObj.name = res.data[0].member_name;
            //         break;
            //     case 3:
            //         _self.receiptMember = res.data[0];
            //         break
            // }
          } else {
            _self.$message.error({
              message: "未找到此会员信息",
              duration: 1500,
            });
          }
        },
        error: function (error) {},
        complete: () => {
          _self.loading = false;
        },
      });
    },

    // 清除 充值 开单会员信息
    bindDelMemberInfo: function () {
      switch (this.isactive1) {
        case 0:
          if (JSON.stringify(this.memberInfo) != "{}") {
            this.memberInfo = {};
            this.queryMember = "";
            this.billingType = 0;
            this.is_dynamic = false;
            this.couponCardCode = "";
          }
          break;
        case 2:
          if (this.cz_huiyuanxinxi.length != 0) {
            this.cz_search_keyword = "";
            this.cz_huiyuanxinxi = {};
            this.cz_chongzhika = {};
            this.rechange_xiao_shou_zhanshi = [];
            this.rechangeSalesShow = "";
            this.cz_chongzhi.benjin = this.cz_chongzhi.zengsong = "";
            this.is_sechargeMember = false;
            this.is_recharge_card = false;
          }
          break;
      }

      this.C_open_order_specifications_save = [];
      this.helpStaffArr[this.isactive1] = [];
      this.Pending();
    },

    // 办卡充值 选择 性别
    bindGender: function (sex) {
      switch (this.isactive1) {
        case 1:
          this.memberObj.is_sex = sex;
          break;
        case 2:
          this.is_cz_sex = sex;
          break;
      }
    },
    //取单
    fetchOrder: function (startTime, endTime, keyword) {
      this.isTakeOrder = true;
      this.loading = true;
      this.couponCardCode = "";
      var _self = this;
      _self.takeOrderPage = 1;
      _self.takeOrderLimit = 10;
      this.takeMoreOrderLoading = true;
      $.ajax({
        url: _self.url + "/android/order/fetchOrder",
        type: "post",
        data: {
          limit: _self.takeOrderLimit,
          page: _self.takeOrderPage,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          shift_no: "",
          keyword: keyword,
          startTime: startTime,
          endTime: endTime,
          type: 1,
        },
        success: function (res) {
          _self.loading = false;
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.takeOrder = res.data;
            _self.takeMoreOrderLoading = false;
          }
        },
        error: function (xhr) {},
        complete: () => {
          _self.loading = false;
        },
      });
    },
    // 触底加载取单单据
    takeMoreOrder() {
      // console.log('加载中',this.takeMoreOrderLoading);
      if (this.takeMoreOrderLoading) {
        return false;
      }
      this.takeMoreOrderLoading = true;
      var _self = this;
      _self.takeOrderPage++;
      let startTime = 0;
      let endTime = 0;
      let keyword = _self.fetchOrderKeyword;
      if (this.fetchOrderTime) {
        startTime = this.getUnixTime(this.fetchOrderTime[0]);
        endTime = this.getUnixTime(this.fetchOrderTime[1]);
      }
      $.ajax({
        url: _self.url + "/android/order/fetchOrder",
        type: "post",
        data: {
          limit: _self.takeOrderLimit,
          page: _self.takeOrderPage,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          shift_no: "",
          keyword: keyword,
          startTime: startTime,
          endTime: endTime,
          type: 1,
        },
        success: function (res) {
          // console.log('resresresresresresres');
          // var res = JSON.parse(res);
          if (res.code == 0) {
            if (res.data.length > 0) {
              res.data.forEach((item, index) => {
                _self.takeOrder.push(item);
              });
              _self.takeMoreOrderLoading = false;
            } else {
              _self.takeMoreOrderLoading = 1;
            }
          }
        },
        error: function (xhr) {},
      });
    },
    //取单选择时间,按选择的时间查询
    queryFetchOrder: function (value) {
      let _self = this;
      // console.log(value);
      // console.log(this.fetchOrderTime);
      if (value) {
        let startTime = this.getUnixTime(value[0]);
        let endTime = this.getUnixTime(value[1]);
        this.fetchOrder(startTime, endTime, this.fetchOrderKeyword);
      } else {
        let startTime = 0;
        let endTime = 0;
        this.fetchOrder(startTime, endTime, this.fetchOrderKeyword);
      }
    },

    //处理时间的方法
    getUnixTime: function (dateStr) {
      var date = new Date(dateStr);
      var time_str = date.getTime().toString();
      return time_str.substr(0, 10);
    },
    //取单input的查询方法
    inputQueryFetchOrder: function () {
      let _self = this;
      //清空计时器
      if (_self.setQueryFetchOrderTimeOut) {
        clearTimeout(_self.setQueryFetchOrderTimeOut);
      }
      let startTime = 0;
      let endTime = 0;
      if (this.fetchOrderTime) {
        // console.log("取单input查询方法")
        startTime = _self.getUnixTime(_self.fetchOrderTime[0]);
        endTime = _self.getUnixTime(_self.fetchOrderTime[1]);
      } else {
        startTime = 0;
        endTime = 0;
      }
      //添加计时器
      _self.setQueryFetchOrderTimeOut = setTimeout(function () {
        _self.fetchOrder(startTime, endTime, _self.fetchOrderKeyword);
      }, 500);
    },

    //取单input的查询方法
    inputQueryEnterFetchOrder: function () {
      //清空计时器
      if (this.setQueryFetchOrderTimeOut) {
        clearTimeout(this.setQueryFetchOrderTimeOut);
      }
      let startTime = 0;
      let endTime = 0;

      if (this.fetchOrderTime) {
        startTime = this.getUnixTime(this.fetchOrderTime[0]);
        endTime = this.getUnixTime(this.fetchOrderTime[1]);
      } else {
        startTime = 0;
        endTime = 0;
      }
      this.fetchOrder(startTime, endTime, this.fetchOrderKeyword);
    },

    //会员扣卡折叠面板change事件
    bind_membercard_change(val) {
      this.membercardChangeIndex = val;
    },
    // 开单----保存  and  收款
    save_order: function (flag) {
      var _self = this;
      var save_orderData = _self.C_open_order_specifications_save;
      // console.log(save_orderData)
      if (save_orderData.length <= 0) {
        return this.$message({
          type: "warning",
          message: "订单信息不能为空",
          duration: 1500,
        });
      }
      // _self.loading = true;
      var buyerId = 0;
      if (this.memberInfo && this.memberInfo.id) {
        buyerId = this.memberInfo.id;
      }
      var orderItems = [];
      let isCostServer = false; //判断购物车里有没有服务
      for (var i = 0; i < save_orderData.length; i++) {
        var equityTypestatus = save_orderData[i].manualDiscount;
        if (save_orderData[i].zhonglei != 2) {
          isCostServer = true;
        }
        // var costCardStatus = save_orderData[i].costCard ?save_orderData[i].costCard.manualDiscount:0;
        orderItems.push({
          cardName: save_orderData[i].manualDiscountCard["cardName"] || "",
          cardDetailsId:
            save_orderData[i].manualDiscountCard.cardSource != -1
              ? save_orderData[i].manualDiscountCard["cardDetailsId"] ||
                save_orderData[i].manualDiscountCard["id"] ||
                0
              : save_orderData[i].manualDiscount == 3
                ? save_orderData[i].manualDiscountCard["cardDetailsId"] ||
                  save_orderData[i].manualDiscountCard["id"] ||
                  0
                : 0, // (开单使用)使用卡项详情的id
          cardId:
            save_orderData[i].manualDiscountCard.cardSource != -1
              ? save_orderData[i].manualDiscountCard["membercard_id"] || 0
              : save_orderData[i].manualDiscount == 3
                ? save_orderData[i].manualDiscountCard["membercard_id"] || 0
                : 0, // 卡项id

          discount: save_orderData[i].manualDiscountCard["discount"] || "10", // 折扣（开单选择充值卡有）

          consumeCard:
            save_orderData[i].costCard &&
            save_orderData[i].costCard.manualDiscount == 2
              ? 2
              : 1, //是否耗卡（1,不是，2，是）
          consumeCardId:
            save_orderData[i].costCard &&
            save_orderData[i].costCard["membercard_id"]
              ? save_orderData[i].costCard["membercard_id"]
              : 0, //耗卡的卡项id（consumeCard==2时有效）默认账户传0，其他他传卡项id
          equityType: save_orderData[i].manualDiscount, // 1 无权益 2折扣 3抵扣 4手动改价

          goodsId: save_orderData[i].id, // 商品id 服务，产品卡项都填写id
          itemId: save_orderData[i].itemId || 0, //取单时候传的itemId
          itemImgId: save_orderData[i].itemImgId || "0", // 预览图id
          itemName:
            save_orderData[i].service_name || save_orderData[i].product_name, // 商品名称
          itemType: save_orderData[i].zhonglei == 2 ? 2 : 1, // 1 服务 2产品 3卡项 4充值
          // num: save_orderData[i].zhonglei == 2 ? save_orderData[i].num : 1, // 数量，除产品外，其他都填写1
          // num调整，扣卡num会单个出现大于1的情况
          num:
            save_orderData[i].zhonglei == 2
              ? save_orderData[i].num
              : save_orderData[i].num > 1
                ? save_orderData[i].num
                : 1,
          originPrice: Math.round(save_orderData[i].price * 100), // 充值金额  原价( 分 )
          recharge_money: 0, // 充值金额（本金）金额 （分） 手动充值时必传
          realPay: Math.round(save_orderData[i].subtotal * 100), // 充值金额真实支付（分）
          present_money: 0, // 充值（赠送）金额 (分) 手动充值时必传
          salesmen: save_orderData[i].salesmen || [], // 选择的销售id
          skuId:
            save_orderData[i].specifications_id ||
            save_orderData[i].sku_val_id ||
            0, // 规格id，非规格天写0
          skuName: save_orderData[i].sku || "", // 规格名称（如：红色,大）没有填空字符串
          stage: "1", // 当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
          technicians: save_orderData[i].technician_id || [], // 服务人员id
          promoterId: save_orderData[i].promoterId
            ? save_orderData[i].promoterId
            : 0, //推广员id
        });
      }

      let couponData = {};
      if (save_orderData.judgeCard == 3) {
        couponData.id = save_orderData.couponCard.id;
        couponData.counpon_money = (
          save_orderData.couponCard.counpon_money * 100
        ).toFixed(0);
      }

      let presentData = [];
      if (this.billGiftData.length > 0) {
        presentData = JSON.parse(JSON.stringify(this.billGiftData));
        presentData.forEach((item) => {
          delete item.price;
        });
      }
      let extraData = {};
      if (this.helpStaffArr[this.isactive1]) {
        extraData["help_staff"] = this.helpStaffArr[this.isactive1].map(
          (item) => {
            return item["id"];
          }
        );
      }
      let orderType = 1;
      //billingType：0：服务；1：产品；2：扣卡
      //orderType：1：服务；2：产品；3：办卡；4：充值 5：充卡；6 直接收款
      if (this.billingType == 1) {
        // 产品
        orderType = 2;
      } else {
        // 扣卡、服务
        orderType = 1;
      }
      $.ajax({
        url: _self.url + "/android/order/orderSave",
        type: "post",
        data: {
          addressInfo: "", // 收货地址信息
          bookerid:
            _self.orderSourceType == 2
              ? _self.fetchOrderDetailData.bookerid
              : 0, // 预约id 来源是预约时使用
          buyerId: buyerId, // 用户id
          cashierId: _self.loginInfo.id, // 收银员id
          dispatchFee: 0, // 运费 （分）
          dispatchType: 0, // 配送类型： 1，到店自提，2，配送，0，非配送
          merchantid: _self.loginInfo.merchantid, //  商户id
          orderGiftItems: [], //订单礼品数组（预留字段）
          orderNo: _self.orderSourceType == 2 ? 0 : _self.orderCount || 0, // 订单号（取单时需要传）
          orderType: orderType, // 1：服务；2：产品；3：办卡；4：充值 5：充卡；6 直接收款
          promotions: [], // 预留字段（优惠信息）
          remark: _self.beizhu_info, // 订单备注
          sourceType: _self.orderSourceType, // 来源类型 1,开单，2,预约，3,取单
          storeid: _self.loginInfo.storeid, // 店铺id
          totalPay: Math.round(_self.payTotal * 100), // 订单总价（分）
          orderItems: JSON.stringify(orderItems),
          shift_no: _self.loginInfo.shift_no,
          couponData:
            save_orderData.judgeCard == 3 ? JSON.stringify(couponData) : 0, //现金券额外参数
          presentData: JSON.stringify(presentData),
          extraData: JSON.stringify(extraData),
        },
        success: function (res) {
          // var res = JSON.parse(res);
          _self.loading = false;
          if (res.code == 1) {
            // 清空协助接待信息
            _self.helpStaffArr[_self.isactive1] = [];
            if (flag == "receipt") {
              //收款
              //使用耗卡
              orderItems.forEach((item) => {
                //使用折扣卡
                if (item.equityType == 2) {
                  if (
                    item.cardDetailsId == 0 &&
                    item.cardId == 0 &&
                    item.consumeCard == 1
                  ) {
                    _self.isPayStatus = 4;
                  } else {
                    _self.isPayStatus = 0;
                    // _self.isPayStatus = 4;
                  }
                }
                if (item.consumeCard == 2) {
                  //consumeCard 是否耗卡（1,不是，2，是）
                  // isPayStatus 收银台--充卡收款跳转到收银台。 0:会员余额,1:支付宝/微信,2:现金,4:自定义收款
                  _self.isPayStatus = 0;
                  // _self.isPayStatus = 4;
                }
              });
              if (isCostServer) {
                if (_self.isPrintCostMaterial == 1) {
                  _self
                    .$confirm("是否打印耗材?", "提示", {
                      confirmButtonText: "打印",
                      cancelButtonText: "取消",
                      type: "warning",
                    })
                    .then(() => {
                      _self.printCostMaterial(res.data.orderNo, res.data.id);
                      setTimeout(function () {
                        _self.buy_receipt = true;
                        _self.loading = false;
                        _self.isCouponCard = false;
                        _self.orderNo = res.data.orderNo;
                        _self.orderId = res.data.id;
                        _self.isRechargeCard = true;
                        _self.C_open_order_specifications_save = [];
                        _self.orderSourceType = 1;
                        _self.orderCount = 0;
                        _self.couponCardCode = "";
                        _self.billGiftData = [];
                        _self.showGiftData = {
                          allPrice: 0,
                          serverNum: 0,
                          productNum: 0,
                        };
                      }, 1000);
                    })
                    .catch(() => {
                      _self.buy_receipt = true;
                      _self.loading = false;
                      _self.isCouponCard = false;
                      _self.orderNo = res.data.orderNo;
                      _self.orderId = res.data.id;
                      _self.isRechargeCard = true;
                      _self.C_open_order_specifications_save = [];
                      _self.orderSourceType = 1;
                      _self.orderCount = 0;
                      _self.couponCardCode = "";
                      _self.billGiftData = [];
                      _self.showGiftData = {
                        allPrice: 0,
                        serverNum: 0,
                        productNum: 0,
                      };
                    });
                } else if (_self.isPrintCostMaterial == 2) {
                  _self.printCostMaterial(res.data.orderNo, res.data.id);
                  setTimeout(function () {
                    _self.buy_receipt = true;
                    _self.loading = false;
                    _self.isCouponCard = false;
                    _self.orderNo = res.data.orderNo;
                    _self.orderId = res.data.id;
                    _self.isRechargeCard = true;
                    _self.C_open_order_specifications_save = [];
                    _self.orderSourceType = 1;
                    _self.orderCount = 0;
                    _self.couponCardCode = "";
                    _self.billGiftData = [];
                    _self.showGiftData = {
                      allPrice: 0,
                      serverNum: 0,
                      productNum: 0,
                    };
                  }, 1000);
                } else {
                  _self.buy_receipt = true;
                  _self.loading = false;
                  _self.isCouponCard = false;
                  _self.orderNo = res.data.orderNo;
                  _self.orderId = res.data.id;
                  _self.isRechargeCard = true;
                  _self.C_open_order_specifications_save = [];
                  _self.orderSourceType = 1;
                  _self.orderCount = 0;
                  _self.couponCardCode = "";
                  _self.billGiftData = [];
                  _self.showGiftData = {
                    allPrice: 0,
                    serverNum: 0,
                    productNum: 0,
                  };
                }
              } else {
                _self.buy_receipt = true;
                _self.loading = false;
                _self.isCouponCard = false;
                _self.orderNo = res.data.orderNo;
                _self.orderId = res.data.id;
                _self.isRechargeCard = true;
                _self.C_open_order_specifications_save = [];
                _self.orderSourceType = 1;
                _self.orderCount = 0;
                _self.couponCardCode = "";
                _self.billGiftData = [];
                _self.showGiftData = {
                  allPrice: 0,
                  serverNum: 0,
                  productNum: 0,
                };
              }
            } else if (flag == "save") {
              if (isCostServer) {
                if (_self.isPrintCostMaterial == 1) {
                  _self
                    .$confirm("是否打印耗材?", "提示", {
                      confirmButtonText: "打印",
                      cancelButtonText: "取消",
                      type: "warning",
                    })
                    .then(() => {
                      _self.printCostMaterial(res.data.orderNo, res.data.id);
                      setTimeout(function () {
                        _self.$message({
                          type: "success",
                          message: res.msg,
                          duration: 1500,
                        });
                        _self.C_open_order_specifications_save = [];
                        _self.memberInfo = [];
                        _self.queryMember = "";
                        _self.isCouponCard = false;
                        _self.is_dynamic = false;
                        _self.billingType = 0;
                        _self.orderSourceType = 1;
                        _self.orderCount = 0;
                        _self.couponCardCode = "";
                        _self.billGiftData = [];
                        _self.showGiftData = {
                          allPrice: 0,
                          serverNum: 0,
                          productNum: 0,
                        };
                        _self.Pending();
                      }, 1000);
                    })
                    .catch(() => {
                      _self.$message({
                        type: "success",
                        message: res.msg,
                        duration: 1500,
                      });
                      _self.C_open_order_specifications_save = [];
                      _self.memberInfo = [];
                      _self.queryMember = "";
                      _self.isCouponCard = false;
                      _self.is_dynamic = false;
                      _self.billingType = 0;
                      _self.orderSourceType = 1;
                      _self.orderCount = 0;
                      _self.couponCardCode = "";
                      _self.billGiftData = [];
                      _self.showGiftData = {
                        allPrice: 0,
                        serverNum: 0,
                        productNum: 0,
                      };
                      _self.Pending();
                    });
                } else if (_self.isPrintCostMaterial == 2) {
                  _self.printCostMaterial(res.data.orderNo, res.data.id);
                  setTimeout(function () {
                    _self.$message({
                      type: "success",
                      message: res.msg,
                      duration: 1500,
                    });
                    _self.C_open_order_specifications_save = [];
                    _self.memberInfo = [];
                    _self.queryMember = "";
                    _self.isCouponCard = false;
                    _self.is_dynamic = false;
                    _self.billingType = 0;
                    _self.orderSourceType = 1;
                    _self.orderCount = 0;
                    _self.couponCardCode = "";
                    _self.billGiftData = [];
                    _self.showGiftData = {
                      allPrice: 0,
                      serverNum: 0,
                      productNum: 0,
                    };
                    _self.Pending();
                  }, 1000);
                } else {
                  _self.$message({
                    type: "success",
                    message: res.msg,
                    duration: 1500,
                  });
                  _self.C_open_order_specifications_save = [];
                  _self.memberInfo = [];
                  _self.queryMember = "";
                  _self.isCouponCard = false;
                  _self.is_dynamic = false;
                  _self.billingType = 0;
                  _self.orderSourceType = 1;
                  _self.orderCount = 0;
                  _self.couponCardCode = "";
                  _self.billGiftData = [];
                  _self.showGiftData = {
                    allPrice: 0,
                    serverNum: 0,
                    productNum: 0,
                  };
                  _self.Pending();
                }
              } else {
                _self.$message({
                  type: "success",
                  message: res.msg,
                  duration: 1500,
                });
                _self.C_open_order_specifications_save = [];
                _self.memberInfo = [];
                _self.queryMember = "";
                _self.isCouponCard = false;
                _self.is_dynamic = false;
                _self.billingType = 0;
                _self.orderSourceType = 1;
                _self.orderCount = 0;
                _self.couponCardCode = "";
                _self.billGiftData = [];
                _self.showGiftData = {
                  allPrice: 0,
                  serverNum: 0,
                  productNum: 0,
                };
                _self.Pending();
              }
            }
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
            _self.loading = false;
          }
          _self.deductionPrice = "";
          _self.input_dis = "";
        },
        error: function (error) {
          _self.loading = false;
        },
      });
    },

    //  取单--开单收款(获取取单详情)
    bindBillingReceipt: function (data) {
      var _self = this;
      _self.C_open_order_specifications_save = [];
      _self.loading = true;
      _self.orderSourceType = 3;
      var orderNo = data.order_number;
      _self.orderCount = orderNo;
      var result1;
      var result2;
      var res1;
      let promise = new Promise(function (resolve, reject) {
        // console.log('走到promise');
        // console.log('id', _self.loginInfo.merchantid, orderNo, _self.loginInfo.storeid);
        $.ajax({
          url: _self.url + "/android/order/fetchOrderDetail",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid,
            orderNo: orderNo,
            storeid: _self.loginInfo.storeid,
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              _self.fetchOrderKeyword = "";
              _self.fetchOrderTime = "";
              resolve(res);
            }
          },
          error: function (error) {
            reject(error);
          },
          complete: () => {
            _self.loading = false;
          },
        });
      });

      promise.then(function (res) {
        // console.log(res);
        // console.log('resvip——id', res.data['vip_id']);
        if (res.data["vip_id"] == 0) {
          result1 = res.data;
          _self.displayOrderOrdinary(result1);
        }
      });
      promise.then(function (res) {
        if (res.data["vip_id"] != 0) {
          result2 = res.data["buyer"]["phone"];
          let result1 = res.data["buyer"]["id"];
          _self.memberSearch(result2, result1);
        }
      });
      promise.then(function (res) {
        if (res.data["vip_id"] != 0) {
          _self.getBalanceCard();
        }
      });
      promise.then(function (res) {
        if (res.data["vip_id"] != 0) {
          result1 = res.data;
          _self.displayOrderOrdinary(result1);
        }
      });
      _self.isTakeOrder = false;
    },

    // 预约---服务开单从预约页面跳过来
    orderReservation: function () {
      var _self = this;
      try {
        parent.window.parentMethod();
      } catch (e) {}

      let url = location.search;
      let orderNo = url.split("=")[1];
      _self.orderCount = orderNo;
      $.ajax({
        url: _self.url + "/android/order/bookingOrderDetail",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderNo: orderNo,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.fetchOrderDetailData = res.data;
            _self.fetchOrderDetailData.toBePay =
              _self.fetchOrderDetailData.toBePay -
              _self.fetchOrderDetailData.net_receipts;

            _self.memberSearch(res.data["buyer"]["phone"]);
            _self.displayOrder(_self.fetchOrderDetailData);
            _self.orderSourceType = 2;
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (error) {},
      });
    },

    //关闭收款
    bindClosePay: function (flag) {
      this.buy_receipt = flag;
      this.over_open();
    },

    /**
     *
     *  办卡  uuai
     *
     * **/

    bk_over_open: function () {
      this.isVipCardMember = false;
      this.memberObj = {};
      this.memberObjData = [];
      this.manualPrice = "";
      this.bk_beizhu_info = "";
      this.cardTemp = {};
      this.card_server_name = [];
      this.isBeneficial = 1;
      this.bk_xiao_shou_zhanshi = {}; //清空销售数组
      this.SalesShow = ""; //清空销售
      this.couponVipCardCode = "";
      this.billGiftData = [];
      this.showGiftData = {
        allPrice: 0,
        serverNum: 0,
        productNum: 0,
      };

      this.helpStaffArr[this.isactive1] = [];
      // this.helpStaffArr[this.isactive1] = [];
      this.$nextTick(
        function () {
          this.inputFocus(this.$refs.memberPhone);
        }.bind(this)
      );
    },

    bindSearchCard: function () {
      this.cardPage = 1;
      this.getCardDataByPage();
    },

    bindChangeCard: function (val) {
      this.cardPage = 1;
      this.getCardDataByPage();
      this.$nextTick(() => {
        this.$refs.applyCard.scrollTop = 0;
        this.inputFocus(this.$refs.cardKeyword);
      });
    },

    loadMoreCard: function () {
      var _self = this;
      this.isCardScroll = true;
      if (this.cardCount == this.cardArr.length) {
        _self.loadingtip = "数据已全部加载";
        return;
      } else {
        _self.busyCard = true;
        _self.loadingtip = "加载中···";
        this.cardPage++;
        this.getCardDataByPage(1);
      }
    },
    // 开单产品加载更多
    loadMoreCombo: function () {
      var _self = this;
      _self.isComboScroll = true;
      if (
        _self.productAllCount == _self.cashier_open_order_product_name.length
      ) {
        _self.loadingtip = "数据已全部加载";
        return;
      } else {
        _self.busyProduct = true;
        _self.loadingtip = "加载中···";
        _self.productPage++;
        _self.productList(1);
      }
    },

    getCardDataByPage: function (flag) {
      var _self = this;
      // this.loading = true;
      $.ajax({
        url: _self.url + "/android/Card/getCardDataByPage",
        type: "post",
        data: {
          cardType: _self.cardType,
          keyword: _self.cardKeyword,
          limit: _self.cardLimit,
          order: 2,
          page: _self.cardPage,
          status: 1,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.cardCount = res.count;

            if (_self.cardCount < 10) {
              _self.busyCard = false;
            }
            if (flag == 1) {
              // _self.loading = false;
              _self.cardArr = _self.cardArr.concat(res.data);
              if (res.count == 0) {
                _self.isCardScroll = true;
              } else {
                _self.isCardScroll = false;
              }
            } else {
              // _self.loading = false;
              _self.cardArr = res.data;
            }
          } else {
            // _self.loading = false;
            _self.cardArr = [];
          }
        },
      });
    },

    //办理会员卡，页面选择办卡
    bindChooseCard(index, data) {
      if (this.cardIndex == index && this.cardTemp?.id) return;
      this.cardIndex = index;

      // 如果已有卡片，先执行滑出动画
      if (this.cardTemp && this.cardTemp.id) {
        // 标记卡片为正在离开状态，触发滑出动画
        this.$set(this.cardTemp, "isLeaving", true);

        // 等待滑出动画完成后再加载新卡片
        setTimeout(() => {
          // 清空旧数据
          this.cardTemp = {};

          // 加载新卡片并触发滑入动画
          this.$nextTick(() => {
            this.cardTemp = JSON.parse(JSON.stringify(data));
            this.cardTemp.judgeCard = 0;
            this.paymentMoney = this.cardTemp.realPrice;
            this.manualPrice = this.cardTemp.realPrice;
            // this.isShowCostCard=1;
            // this.cardTemp.costCard={};
            this.getCardDetails(data.id);
          });
        }, 350); // 动画时长500ms
      } else {
        // 如果没有已存在的卡片，直接加载新卡片
        this.cardTemp = JSON.parse(JSON.stringify(data));
        this.cardTemp.judgeCard = 0;
        this.paymentMoney = this.cardTemp.realPrice;
        this.manualPrice = this.cardTemp.realPrice;
        this.getCardDetails(data.id);
      }
    },

    //办理会员卡，页面选择办卡
    getCardDetails: function (id) {
      var _self = this;
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/Card/getCardDetails",
        type: "post",
        data: {
          cardId: id,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.card_server_name = res.data;
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },

    bindDelCard: function () {
      this.isVipCouponCard = false; //现金券回复初始设置
      this.isBeneficial = 1; //优惠权益回复初始设置
      this.bk_xiao_shou_zhanshi = {}; //清空销售数组
      this.bk_beizhu_info = ""; //清空备注
      this.manualPrice = ""; //清空手动改价
      this.SalesShow = ""; //清空销售
      this.cardIndex = null;

      // 如果有卡片，先执行滑出动画
      if (this.cardTemp && this.cardTemp.id) {
        // 标记卡片为正在离开状态，触发滑出动画
        this.$set(this.cardTemp, "isLeaving", true);

        // 等待滑出动画完成后再清空卡片数据
        setTimeout(() => {
          // 保持DOM元素不变，但移除所有数据，这样动画可以完成
          for (let key in this.cardTemp) {
            if (key !== "isLeaving") {
              this.$delete(this.cardTemp, key);
            }
          }

          // 再等待一小段时间后完全清空对象
          setTimeout(() => {
            this.cardTemp = {};
            this.paymentMoney = "";
          }, 50);
        }, 350); // 动画时长350ms
      } else {
        this.cardTemp = {}; //清空办卡的信息
        this.paymentMoney = "";
      }
    },

    // 刷新卡片动画效果
    refreshCardTemp: function () {
      if (this.cardTemp && this.cardTemp.id) {
        const tempCard = JSON.parse(JSON.stringify(this.cardTemp));
        this.cardTemp = {};
        this.$nextTick(() => {
          this.cardTemp = tempCard;
        });
      }
    },
    // 优惠and手动折扣
    bindOffer: function (type) {
      let _self = this;
      //归还次卡次数
      let promise = new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/vip/getMSEquityDetail",
          type: "post",
          data: {
            storeid: _self.loginInfo.storeid,
            merchantid: _self.loginInfo.merchantid,
            memberid: _self.memberInfo.id, //会员id
            serviceid: _self.chioced_id,
            type: _self.isGoodsType,
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              resolve(res);
            }
          },
          error: function (error) {
            reject(error);
          },
        });
      }).then(function (res) {
        _self.timeCardCount = res.data; //可用的优惠权益
        var purchaseGoods = _self.C_open_order_specifications_save;
        purchaseGoods[_self.carIndex]["give_preferential"]["once"] =
          _self.timeCardCount.once;
      });
      // console.log("timecardcount-------------------------------")
      // console.log(_self.timeCardCount);
      // console.log('类型', type);
      var carData = this.C_open_order_specifications_save;
      // console.log(carData);
      var carDatalength = carData.length;
      var index = this.carIndex;
      var discountData = this.balanceCard;
      // console.log('折扣权益数据', this.balanceCard);
      // console.log('cardata', carData);
      if (type == 1 && carDatalength != 0) {
        // console.log(carData[index])
        carData[index].choosemembercardId = 0;
        if (carData[index].hasOwnProperty("add_give_preferential")) {
          // if (carData[index]['add_preferential_type'] == 1) {
          //     //以选择的权益是次卡
          //     for (let i = 0; i < carData.length; i++) {
          //         if (carData[i]['id'] == carData[index]['id'] && carData[i].hasOwnProperty('give_preferential')) {
          //             //选中整个服务数组与当前id相同且含有give_preferential字段的对象
          //             for (let j = 0; j < carData[i]['give_preferential']['once'].length; j++) {
          //                 if (carData[index]['add_preferential_id'] == carData[i]['give_preferential']['once'][j]['membercard_id']) {
          //                     carData[i]['give_preferential']['once'][j]['maxnum']++;
          //                     break;
          //                 }
          //             }
          //         }
          //     }
          // } else {
          //     //以选择的权益是折扣卡
          //     console.log(discountData);
          //     console.log('id测试', carData[index]);
          //     for (let i = 0; i < discountData.length; i++) {
          //         if (carData[index]['add_preferential_id'] == discountData[i]['id']) {
          //             discountData[i]['residuebalance'] += carData[index]['add_preferential_discount'];
          //             break;
          //         }
          //     }
          // }
          //选择不使用优惠时，清空卡的详情
          carData[index].manualDiscountCard = {};
          carData[index].manualDiscount = 1;
          delete carData[index]["add_give_preferential"];
          delete carData[index]["add_preferential_id"];
          delete carData[index]["add_preferential_time"];
          delete carData[index]["add_preferential_discount"];
          delete carData[index]["add_preferential_type"];
        } else {
        }
      }
      //这里是手动改价
      else if (type == 4 && carDatalength != 0) {
        if (carData && carData[index].hasOwnProperty("add_give_preferential")) {
          // if (carData[index]['add_preferential_type'] == 1) {
          //     //以选择的权益是此卡
          //     for (let i = 0; i < carData.length; i++) {
          //         if (carData[i]['id'] == carData[index]['id'] && carData[i].hasOwnProperty('give_preferential')) {
          //             //选中整个服务数组与当前id相同且含有give_preferential字段的对象
          //             for (let j = 0; j < carData[i]['give_preferential']['once'].length; j++) {
          //                 if (carData[index]['add_preferential_id'] == carData[i]['give_preferential']['once'][j]['membercard_id']) {
          //                     carData[i]['give_preferential']['once'][j]['maxnum']++;
          //                     break;
          //                 }
          //             }
          //         }
          //     }
          // } else {
          //     //以选择的权益是折扣卡
          //     console.log(discountData);
          //     console.log('id测试', carData[index]);
          //     for (let i = 0; i < discountData.length; i++) {
          //         if (carData[index]['add_preferential_id'] == discountData[i]['id']) {
          //             console.log('还钱');
          //             discountData[i]['residuebalance'] += carData[index]['add_preferential_discount'];
          //             console.log('换完了', discountData);
          //             break;
          //         }
          //     }
          // }
          //选择不使用优惠时，清空卡的详情
          carData[index].costCard = {};
          carData[index].costCard.card_info = "不使用耗卡";
          carData[index].manualDiscountCard = {};
          carData[index].manualDiscount = 4;
          delete carData[index]["add_give_preferential"];
          delete carData[index]["add_preferential_id"];
          delete carData[index]["add_preferential_time"];
          delete carData[index]["add_preferential_discount"];
          delete carData[index]["add_preferential_type"];
        } else {
        }
      }
      //办卡不使用优惠 8   优惠金额  9
      else if (type == 8) {
        if (_self.cardTemp) {
          _self.manualPrice = _self.cardTemp.realPrice;
        }
        _self.getVipCard = 1;
        _self.isVipCouponCard = false;
        _self.isBeneficial = 1;
        _self.cardTemp.couponCard = {};
        _self.paymentMoney = _self.manualPrice;
        _self.bk_is_xuanze_youhui = false;
      } else if (type == 9) {
        _self.getVipCard = 2;
        _self.isVipCouponCard = false;
        _self.isBeneficial = 4;
        _self.cardTemp.couponCard = {};
        _self.paymentMoney = _self.manualPrice;
        _self.bk_is_xuanze_youhui = false;
      }
      if (this.isactive1 == 0) {
        // alert('1111')
        carData[index].manualDiscount = type;
        this.bk_youhui_power = false;

        var Obj = carData[index];

        this.subtotalAmount();
        Vue.set(carData[index], "discount", 1);
        this.$forceUpdate();
      } else {
        // this.isBeneficial = type;
        this.bk_youhui_power = false;
      }

      this.Pending();
      try {
        carData[index].judgeCard = 0;
      } catch (e) {}
    },
    // 选择优惠卡项
    bindOfferCard: function (type, data) {
      //type为1表示次卡 type为2表示折扣卡

      //点击之后先判断为次卡还是折扣卡，在各自判断有没有该id优惠卡。在判断该服务有没有已经选过优惠

      //（有，判断优惠是否和现在选的相同，相同不操作，不同根据已有的id去找第一次添加数据的对象更改内容）

      //（没有，直接根据现在选择的优惠去改变第一次添加的数据，并在自己添加字段，是否选择优惠，什么优惠，优惠多少）

      //is_have_preferential该字段是用来判断是否选择过了优惠权益

      var _self = this;

      // console.log('折扣匹配的余额信息',_self.balanceCard);
      var carData = _self.C_open_order_specifications_save;

      var index = _self.carIndex;
      //定义打折后的钱用来判断
      var discount_money;
      //obj_id是当前选中服务的id
      var obj_id = carData[index]["id"];
      var is_goback = false;
      // _self.oneObj['manualDiscountCard']=data;
      // _self.C_open_order_specifications_save.push(_self.oneObj);
      // console.log('点击的服务对象',carData[index]);
      //点击的服务对象内有没有add_give_preferential字段，有的话表示该服务已经选择过权益了。
      //add_preferential_type该字段表示选择优惠权益是什么类型直接放在相对的服务对象内，1表示次卡，2表示折扣。
      //这个id表示选择权益的卡id  三位数那个id
      var id = data.id;
      //这个membercard_id表示与会员的卡权益数组对应的id
      var membercard_id = data.id;
      //定义一个变量用来存储当更换权益时候存贮之前的权益 注意当只有该服务重选权益时候才会用到
      //判断当前卡的id和会员扣卡是的id是不是同一个id
      // console.log(membercard_id == carData[index]["choosemembercardId"]);
      /*if (membercard_id == carData[index]["choosemembercardId"] && type==1) {
			    if (carData[index].chooseMemberCardCount == 0) {
			        _self.$message({
			            message: '请勿重复选择',
			            type: 'warning',
			            duration: 1500,
			        });
			        return false
			    }
			}*/
      var save_preferential = {};
      //type  1:次卡 2:折扣卡
      if (type == 1) {
        //判断是否有该次卡
        var is_time_card = false;
        for (let i = 0; i < _self.availableEquity["once"].length; i++) {
          if (id == _self.availableEquity["once"][i]["id"]) {
            is_time_card = true;
            break;
          }
        }
        //有该次卡
        if (is_time_card) {
          // 判断次数是否充足
          let hasUseNum = 0; // 已使用生物次数
          if (data.once_cardtype == 2 && data.isgive == 2) {
            // 无限次卡  无限判断次数
          } else {
            hasUseNum = this.cardUseNum(data);
            if (data.maxnum - hasUseNum < 1) {
              // 次数不足

              return _self.$message({
                message: "可用权益次数不足",
                type: "warning",
                duration: 1500,
              });
            }
          }
          // 给购物车数据赋值数据
          carData[index].manualDiscountCard = this.deepCopy(data);
          carData[index].manualDiscount = 3;
          carData[index]["add_give_preferential"] = true;
          carData[index]["add_preferential_type"] = 1; //判断该服务添加的权益是次卡还是折扣卡 1次卡 2折扣卡
          carData[index]["add_preferential_id"] = id; //给该服务添加一个选中的权益的id。
          carData[index]["choosemembercardId"] = membercard_id; //给该服务添加一个选中的权益的id。
          carData[index]["add_preferential_time"] = 1; //给该服务添加一个次卡的次数。可有可无

          //耗卡赋值卡名和卡号
          carData[index].costCard = {};
          carData[index].costCard.card_info = "不使用耗卡";
          carData[index].judgeCard = 1;
          _self.bk_youhui_power = false;
          _self.subtotalAmount();
        } else {
          _self.$message({
            message: "对不起，您没有该项优惠权益",
            type: "warning",
            duration: 1500,
          });
        }
      }
      //type  1:次卡 2:折扣卡
      else {
        //type为2标识选中的是折扣卡
        //判断是否有该折扣卡
        var is_discount_card = false;
        // 是否是会员等级折扣
        var is_vip_group_card = false;
        // console.log(_self.availableEquity)
        //判断折扣卡里是否有此折扣卡，
        _self.availableEquity["dis"].forEach((item) => {
          if (id == item.id) {
            is_discount_card = true;
          }
        });
        if (data.id == 0 && data.membercard_id == 0) {
          // 等级折扣
          is_vip_group_card = true; //
        }
        if (is_discount_card) {
          // 判断折扣权益类型
          if (data.cardSource < 0 || is_vip_group_card) {
            //console.log("导入的会员权益要当会员等级折扣来进行计算");
            carData[index].manualDiscountCard = this.deepCopy(data);
            carData[index].manualDiscount = 2;
            carData[index]["add_give_preferential"] = true;
            carData[index]["add_preferential_type"] = 2;
            carData[index]["add_preferential_id"] = id;
            carData[index]["add_preferential_discount"] = discount_money;
            //导入的会员权益要    add_preferential_membercard_id设为-1
            carData[index]["add_preferential_membercard_id"] = -1;
            if (is_vip_group_card) {
              carData[index]["add_preferential_membercard_id"] = 0;
            }
            //耗卡赋值卡名
            // console.log("赋值耗卡名字")
            carData[index].costCard = {};
            carData[index].costCard.card_info = "不使用耗卡";
            carData[index].judgeCard = 0;
            carData[index].isActiveCostCard = 1;
            // is_back_preferential_dis = true;
            _self.bk_youhui_power = false;
            _self.subtotalAmount();
          } else {
            // 检测余额
            let hasUseMoney = this.costCardLessMoney(data);
            hasUseMoney = (hasUseMoney * 100).toFixed(0);
            // 获取本卡的余额
            let discount_money = Math.round(
              carData[index]["price"] * (data["discount"] / 10) * 100
            );
            let balanceCard;
            this.balanceCard.some((item) => {
              if (item["id"] == data.membercard_id) {
                balanceCard = item;
                return true;
              }
              return false;
            });
            if (!balanceCard) {
              return _self.$message({
                message: "对不起，您没有该项优惠权益",
                type: "warning",
                duration: 1500,
              });
            } else {
              // console.log(balanceCard,hasUseMoney,discount_money,'判断余额是否充足');
              if (
                balanceCard["residuebalance"] - hasUseMoney - discount_money >=
                0
              ) {
                carData[index].manualDiscount = 2;
                carData[index].manualDiscountCard = this.deepCopy(data);
                carData[index]["add_give_preferential"] = true;
                carData[index]["add_preferential_type"] = 2; //判断该服务添加的权益是次卡还是折扣卡 1次卡 2折扣卡
                carData[index]["add_preferential_id"] = id; //给该服务添加一个选中的权益的id。
                carData[index]["choosemembercardId"] = membercard_id; //给该服务添加一个选中的权益的id。
                carData[index]["add_preferential_membercard_id"] =
                  membercard_id; //给该服务添加一个选中的权益与折扣卡匹配的id
                carData[index]["add_preferential_discount"] = discount_money; //给该服务添加折扣的钱
                //耗卡赋值卡名和卡号
                carData[index].costCard = {};
                carData[index].costCard.card_info =
                  carData[index].manualDiscountCard.cardName;
                carData[index].judgeCard = 1;
                // is_back_preferential_dis = true;
                _self.bk_youhui_power = false;
                _self.subtotalAmount();
              } else {
                return _self.$message({
                  message: "卡项余额不足",
                  type: "warning",
                  duration: 1500,
                });
              }
            }
          }
        } else {
          this.$message({
            message: "对不起，您没有该项优惠权益",
            type: "warning",
            duration: 1500,
          });
        }
      }
      /*无需执行归还操作*/
      /*if (carData[index].chooseMemberCardCount == 1) {
			    //删除后恢复会员权益卡的可用次数
			    // _self.C_open_order_specifications_save[index].num = _self.C_open_order_specifications_save[index].num + 1;
			    let collapseIndex = parseInt(_self.C_open_order_specifications_save[index].membercardChangeIndex);


			    //判断当前折叠面板的数组长度是否大于1
			    if (collapseIndex === collapseIndex) {
			        if (_self.newMemberCardInfo[collapseIndex].goodsInfo.length > 1) {
			            _self.newMemberCardInfo[collapseIndex].goodsInfo.forEach(item => {
			                if (item.id == _self.C_open_order_specifications_save[index].id && item.equityTypes == 3) {
			                    // item.num = item.num + 1;//删除时把服务次数还回去
			                }
			                if (item.id == _self.C_open_order_specifications_save[index].id && item.equityTypes == 2) {
			                    _self.balanceCard.forEach(item1 => {
			                        if (item1.id == _self.newMemberCardInfo[collapseIndex].cardInfo.card_id) {

			                            item1.residuebalance = item1.residuebalance + Number(item.price) * 100;
			                            console.log(item1.residuebalance)
			                        }
			                    });
			                }

			            });
			        } else {
			            if (_self.C_open_order_specifications_save[index].equityTypes == 3) {
			                // _self.newMemberCardInfo[collapseIndex].goodsInfo[0].num = _self.C_open_order_specifications_save[index].num;//删除时把服务次数还回去
			            } else {
			                _self.balanceCard.forEach(item1 => {
			                    if (item1.id == _self.newMemberCardInfo[collapseIndex].cardInfo.card_id) {
			                        item1.residuebalance = item1.residuebalance + Number(_self.newMemberCardInfo[collapseIndex].goodsInfo[0].price) * 100;
			                    }
			                });
			            }
			        }
			    }
			}*/
    },

    //耗卡
    bindCostCard: function (value, index) {
      //judgeCard  1：使用优惠权益 2：使用耗卡 0：默认 3:使用现金券
      let _self = this;
      var carData = _self.C_open_order_specifications_save;
      //服务的位置，
      let serverIndex = _self.seen1;
      if (carData[serverIndex].judgeCard == 1 || carData.judgeCard == 3) {
        if (carData[serverIndex].judgeCard == 1) {
          _self.$message({
            type: "warning",
            message: "不使用优惠权益才可以选择耗卡",
            duration: 1500,
          });
        } else {
          _self.Pending();
          _self.isChooseCostCard = true;
        }
      } else {
        _self.isChooseCostCard = true;
      }
    },

    //选择耗卡
    bindChooseCostCard: function (num, item, index) {
      // 选择默认账户 0 1 1
      // 不使用耗卡 1 1 1

      let _self = this;
      switch (num) {
        case 0:
          _self.handleDefaultCount(num);
          break;
        case 1:
          _self.handleDefaultCount(num);
          break;
        case 2:
          _self.handleCostCard(item, index);
          break;
      }
    },

    //处理不使用耗卡和使用默认账户
    handleDefaultCount: function (index) {
      // 1，不使用耗卡 0，使用默认账户
      //judgeCard  1：使用优惠权益 2：使用耗卡 0：默认 3：使用现金券
      let _self = this;
      var carData = _self.C_open_order_specifications_save;

      //服务的位置，
      let serverIndex = _self.seen1;
      if (index == carData[serverIndex].isActiveCostCard) {
        _self.$message({
          type: "warning",
          message: "不能重复选择",
          duration: 1500,
        });
      } else {
      }
      if (index == 1) {
        // 1，不使用耗卡
        carData[serverIndex].isActiveCostCard = index;
        console.error(carData[serverIndex]);
        if (carData[serverIndex].add_preferential_id == 0) {
          carData[serverIndex].judgeCard = 0;
          //carData[serverIndex].manualDiscountCard.cardName = "会员等级折扣";
          carData[serverIndex].costCard = {};
          carData[serverIndex].costCard.manualDiscount = 1;
          carData[serverIndex].costCard.membercard_id = 0;
        } else {
          if (
            carData[serverIndex].add_preferential_membercard_id == -1 ||
            carData[serverIndex].cardSource == -1
          ) {
            carData[serverIndex].judgeCard = 0;
            //carData[serverIndex].manualDiscountCard.cardName = "其他权益折扣卡";
            carData[serverIndex].costCard = {};
            carData[serverIndex].costCard.manualDiscount = 1;
            carData[serverIndex].costCard.membercard_id = 0;
          } else {
            carData[serverIndex].judgeCard = 0;
            carData[serverIndex].manualDiscountCard = {};
            carData[serverIndex].costCard.manualDiscount = 1;
          }
        }
      } else {
        // 1，使用默认账户
        // 检测余额是否充足
        let hasUseMoney = this.costCardLessMoney({
          membercard_id: 0,
        });
        let memberDefaultBalance = this.memberDefaultBalance;
        if (
          Number(carData[serverIndex].subtotal) * 100 >
          memberDefaultBalance - hasUseMoney
        ) {
          return _self.$message({
            type: "warning",
            message: "余额不足",
            duration: 1500,
          });
        }
        carData[serverIndex].isActiveCostCard = 0;
        // console.log('else处理不使用耗卡和默认账户')
        if (
          carData[serverIndex].add_preferential_membercard_id == -1 ||
          carData[serverIndex].cardSource == -1
        ) {
          // console.log("扣卡选择导入的会员权益，切换耗卡")
          carData[serverIndex].judgeCard = 2;
          carData[serverIndex].manualDiscountCard.cardSource = -1;
          carData[serverIndex].costCard = {};
          carData[serverIndex].costCard.card_info = "默认账户";
          carData[serverIndex].costCard.manualDiscount = 2;
          carData[serverIndex].costCard.membercard_id = 0;
        } else {
          carData[serverIndex].judgeCard = 2;
          carData[serverIndex].costCard = {};
          carData[serverIndex].costCard.card_info = "默认账户";
          carData[serverIndex].costCard.manualDiscount = 2;
          carData[serverIndex].costCard.membercard_id = 0;
        }
      }
      _self.isChooseCostCard = false;
    },
    //处理选择耗卡问题
    handleCostCard: function (data, index) {
      let _self = this;
      let flag = true;
      var carData = _self.C_open_order_specifications_save;
      //服务的位置，
      let serverIndex = _self.seen1;
      if (carData[serverIndex].costCard) {
        if (carData[serverIndex].costCard.id == data.id) {
          flag = false;
          _self.$message({
            type: "warning",
            message: "不能重复选择",
            duration: 1500,
          });
        }
        if (flag) {
          let hasUseMoney = this.costCardLessMoney(data);
          // 判断可用余额是否充足
          if (
            data.residuebalance - parseInt(hasUseMoney * 100) >=
            Number(carData[serverIndex].subtotal) * 100
          ) {
            carData[serverIndex].costCard = data;
            carData[serverIndex].costCard.manualDiscount = 2;
            carData[serverIndex].costCard.membercard_id = data.id;
            if (carData[serverIndex].cardSource == -1) {
              carData[serverIndex].manualDiscountCard.cardSource = -1;
            } else {
            }
            carData[serverIndex].isActiveCostCard = 2;
            carData[serverIndex].judgeCard = 2;
            _self.isChooseCostCard = false;
          } else {
            _self.$message({
              type: "warning",
              message: "卡余额不足",
              duration: 1500,
            });
          }
        }
      } else {
        let hasUseMoney = this.costCardLessMoney(data);

        if (
          data.residuebalance - parseInt(hasUseMoney * 100) >=
          Number(carData[serverIndex].subtotal) * 100
        ) {
          carData[serverIndex].costCard = data;
          carData[serverIndex].costCard.manualDiscount = 2;
          carData[serverIndex].costCard.membercard_id = data.id;
          carData[serverIndex].isActiveCostCard = 2;
          carData[serverIndex].judgeCard = 2;
          _self.isChooseCostCard = false;
        } else {
          _self.$message({
            type: "warning",
            message: "卡余额不足",
            duration: 1500,
          });
        }
      }
    },
    //关闭耗卡dialog
    cancelCostCard: function () {
      let _self = this;
      _self.isChooseCostCard = false;
      _self.kd_is_xuanze_youhui = false;
    },
    //获取现金券信息
    kd_xuanze_youhui: function () {
      let _self = this;
      //添加产品时，优惠券信息清空
      if (_self.C_open_order_specifications_save.judgeCard == 3) {
        // console.log("现在在添加服务，添加服务")
        _self.C_open_order_specifications_save.couponCard = {};
        _self.isCouponCard = false;
        _self.C_open_order_specifications_save.judgeCard = 0;
        _self.assignGoods = false;
        _self.Pending();
      }
      _self.kd_is_xuanze_youhui = true;
      // this.kd_is_xuanze_youhui = true;
      $.ajax({
        url: _self.url + "/android/Member/getCouponInfo",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          storeid: _self.loginInfo.storeid, // 商户id
          // "type": req.body.type,              // 1 未使用 2已使用 3已过期，在会员详情中必须传，会员开单选择时不传此字段
          limit: 20, // 分页 每页几条
          page: 1, // 分页 第几页
          coupon_num: "",
          uid: _self.memberInfo.id || 0, // 用户id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.couponInfo = res.data;

            var carData = _self.C_open_order_specifications_save;
            let flag = false; //判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用
            let toBePaidMoney = 0; //将要支付的现金金额
            let toBePaidArr1 = []; //将要支付现金金额的服务id的数组
            if (_self.couponCardCode) {
              _self.scanCouponCard(_self.couponCardCode);
              // console.log("扫描现金券的码")
            }

            //judgeCard  1：使用优惠权益 2：使用耗卡 0：默认 3:使用现金券
            if (carData.length != 0) {
              carData.forEach((item) => {
                //判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用
                if (item.judgeCard == 0) {
                  flag = true;
                  toBePaidMoney = toBePaidMoney + Number(item.subtotal);
                  if (item.zhonglei == 2) {
                    toBePaidArr1.push({
                      id: item.product_id,
                      type: item.zhonglei,
                      price: Number(item.subtotal) * 100,
                    });
                  } else {
                    toBePaidArr1.push({
                      id: item.id,
                      type: item.zhonglei,
                      price: Number(item.subtotal) * 100,
                    });
                  }
                }
              });

              var map = {},
                toBePaidArr = [];
              for (var i = 0; i < toBePaidArr1.length; i++) {
                var ai = toBePaidArr1[i];
                if (!map[ai.id]) {
                  toBePaidArr.push({
                    id: ai.id,
                    type: ai.type,
                    price: ai.price,
                  });
                  map[ai.id] = ai;
                } else {
                  for (var j = 0; j < toBePaidArr.length; j++) {
                    var dj = toBePaidArr[j];
                    if (dj.id == ai.id && dj.type == ai.type) {
                      dj.price = dj.price + ai.price;
                      break;
                    }
                  }
                }
              }
              console.log(
                "判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用,有重复数组的拼接"
              );

              if (flag) {
                _self.couponInfo.forEach((item) => {
                  //coupon_type  优惠券类型：1 现金券 2 兑换券 3 待定。。。
                  //现金券
                  if (item.coupon_type == 1) {
                    //applyGoods  可用商品：1 全部商品 2 指定商品
                    //全部商品
                    if (item.applyGoods == 1) {
                      //consume 最低消费: 0：无门槛 其他数值：满多少元可用
                      //全部商品 无门槛
                      if (item.consume == 0) {
                        item.isAvailable = true;
                      }
                      //全部商品 有门槛 满多少元可用
                      else {
                        // console.log(toBePaidMoney);//将要支付的现金金额
                        if (toBePaidMoney * 100 >= item.consume) {
                          item.isAvailable = true;
                        } else {
                          item.isAvailable = false;
                        }
                      }
                    }
                    //指定商品
                    else {
                      //consume 最低消费: 0：无门槛 其他数值：满多少元可用
                      //指定商品 无门槛

                      if (item.consume == 0) {
                        let itemPrice = 0; //使用这个券的服务和产品的总价格
                        item.goodsInfo.forEach((item1) => {
                          toBePaidArr.forEach((item2) => {
                            if (item1.productid == item2.id) {
                              console.log(
                                "指定产品，有门槛，指定产品门槛的价格"
                              );

                              if (item1.type == 1 && item2.type == 2) {
                                itemPrice = itemPrice + item2.price;
                              }
                              if (item1.type == 2 && item2.type == 1) {
                                itemPrice = itemPrice + item2.price;
                              }
                            }
                          });
                        });

                        if (itemPrice) {
                          item.isAvailable = true;
                        } else {
                          item.isAvailable = false;
                        }
                        itemPrice = 0;
                      }
                      //指定商品 有门槛 满多少元可用
                      else {
                        if (toBePaidMoney * 100 >= item.consume) {
                          let itemPrice = 0; //使用这个券的服务和产品的总价格
                          item.goodsInfo.forEach((item1) => {
                            toBePaidArr.forEach((item2) => {
                              if (item1.productid == item2.id) {
                                console.log(
                                  "指定产品，有门槛，指定产品门槛的价格"
                                );

                                if (item1.type == 1 && item2.type == 2) {
                                  itemPrice = itemPrice + item2.price;
                                }
                                if (item1.type == 2 && item2.type == 1) {
                                  itemPrice = itemPrice + item2.price;
                                }
                              }
                            });
                          });

                          if (itemPrice >= item.consume) {
                            item.isAvailable = true;
                          } else {
                            item.isAvailable = false;
                          }
                          itemPrice = 0;
                        } else {
                          item.isAvailable = false;
                        }
                      }
                    }
                  }
                  //兑换券
                  else if (item.coupon_type == 2) {
                  }
                  //其他待定的
                  else {
                  }
                });
              } else {
                _self.couponInfo.forEach((item) => {
                  item.isAvailable = false;
                });
              }
            }
            //商品数组长度为0的时候，现金券全部为不可用
            else {
              _self.couponInfo.forEach((item) => {
                item.isAvailable = false;
              });
            }
          } else {
            _self.loading = false;
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (err) {},
      });
    },
    //处理选择现金券
    bindCouponCard: function (data, index, active) {
      let _self = this;

      var carData = _self.C_open_order_specifications_save;
      if (active) {
        if (data == 0 && index == 0) {
          carData.couponCard = {};
          carData.judgeCard = 0;
          _self.assignGoods = false;
          _self.isCouponCard = false;
          _self.kd_is_xuanze_youhui = false;
          _self.Pending();
        } else {
          carData.couponCard = data;
          carData.couponCard.coupon_name = data.coupon_name;
          carData.judgeCard = 3;

          if (data.applyGoods == 2) {
            _self.assignGoods = true;
            let toBePaidArr1 = []; //将要支付现金金额的服务id的数组

            //judgeCard  1：使用优惠权益 2：使用耗卡 0：默认 3:使用现金券
            if (carData.length != 0) {
              carData.forEach((item) => {
                //判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用
                if (item.judgeCard == 0) {
                  flag = true;
                  if (item.zhonglei == 2) {
                    toBePaidArr1.push({
                      id: item.product_id,
                      type: item.zhonglei,
                      price: Number(item.subtotal) * 100,
                    });
                  } else {
                    toBePaidArr1.push({
                      id: item.id,
                      type: item.zhonglei,
                      price: Number(item.subtotal) * 100,
                    });
                  }
                }
              });

              var map = {},
                toBePaidArr = [];
              for (var i = 0; i < toBePaidArr1.length; i++) {
                var ai = toBePaidArr1[i];
                if (!map[ai.id]) {
                  toBePaidArr.push({
                    id: ai.id,
                    type: ai.type,
                    price: ai.price,
                  });
                  map[ai.id] = ai;
                } else {
                  for (var j = 0; j < toBePaidArr.length; j++) {
                    var dj = toBePaidArr[j];
                    if (dj.id == ai.id && dj.type == ai.type) {
                      dj.price = dj.price + ai.price;
                      break;
                    }
                  }
                }
              }
            }

            let itemPrice = 0; //使用这个券的服务和产品的总价格
            if (data.consume == 0) {
              data.goodsInfo.forEach((item1) => {
                toBePaidArr.forEach((item2) => {
                  if (item1.productid == item2.id) {
                    if (item1.type == 1 && item2.type == 2) {
                      itemPrice = itemPrice + item2.price;
                    }
                    if (item1.type == 2 && item2.type == 1) {
                      itemPrice = itemPrice + item2.price;
                    }
                  }
                });
              });
            }
            //指定商品 有门槛 满多少元可用
            else {
              data.goodsInfo.forEach((item1) => {
                toBePaidArr.forEach((item2) => {
                  if (item1.productid == item2.id) {
                    if (item1.type == 1 && item2.type == 2) {
                      itemPrice = itemPrice + item2.price;
                    }
                    if (item1.type == 2 && item2.type == 1) {
                      itemPrice = itemPrice + item2.price;
                    }
                  }
                });
              });
            }

            if (itemPrice >= data.consume) {
              _self.assignPrice =
                itemPrice >= Number(data.faceValue) * 100
                  ? Number(data.faceValue) * 100
                  : itemPrice;
            } else {
              _self.assignPrice = itemPrice;
            }
            itemPrice = 0;
          }
          _self.isCouponCard = true;
          _self.kd_is_xuanze_youhui = false;
          _self.Pending();
        }
      } else {
        _self.$message({
          type: "warning",
          message: "选择的优惠券不可用",
          duration: 1500,
        });
      }
    },

    //开单扫码搜索现金券
    scanCouponCard: function (coupon_num) {
      let _self = this;
      // console.log("扫描现金券")
      // _self.kd_is_xuanze_youhui = true;
      // this.kd_is_xuanze_youhui = true;
      if (coupon_num.length == 0 || coupon_num.length == 14) {
        $.ajax({
          url: _self.url + "/android/Member/getCouponInfo",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            storeid: _self.loginInfo.storeid, // 商户id
            // "type": req.body.type,              // 1 未使用 2已使用 3已过期，在会员详情中必须传，会员开单选择时不传此字段
            limit: 20, // 分页 每页几条
            page: 1, // 分页 第几页
            coupon_num: coupon_num || "",
            uid: _self.memberInfo.id || 0, // 用户id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              _self.couponInfo = res.data;

              var carData = _self.C_open_order_specifications_save;
              let flag = false; //判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用
              let toBePaidMoney = 0; //将要支付的现金金额
              let toBePaidArr1 = []; //将要支付现金金额的服务id的数组
              // console.log(carData.length != 0);
              // console.log(carData);
              //judgeCard  1：使用优惠权益 2：使用耗卡 0：默认 3:使用现金券
              if (carData.length != 0) {
                carData.forEach((item) => {
                  //判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用
                  if (item.judgeCard == 0) {
                    flag = true;
                    toBePaidMoney = toBePaidMoney + Number(item.subtotal);
                    if (item.zhonglei == 2) {
                      toBePaidArr1.push({
                        id: item.product_id,
                        type: item.zhonglei,
                        price: Number(item.subtotal) * 100,
                      });
                    } else {
                      toBePaidArr1.push({
                        id: item.id,
                        type: item.zhonglei,
                        price: Number(item.subtotal) * 100,
                      });
                    }
                  }
                });

                var map = {},
                  toBePaidArr = [];
                for (var i = 0; i < toBePaidArr1.length; i++) {
                  var ai = toBePaidArr1[i];
                  if (!map[ai.id]) {
                    toBePaidArr.push({
                      id: ai.id,
                      type: ai.type,
                      price: ai.price,
                    });
                    map[ai.id] = ai;
                  } else {
                    for (var j = 0; j < toBePaidArr.length; j++) {
                      var dj = toBePaidArr[j];
                      if (dj.id == ai.id && dj.type == ai.type) {
                        dj.price = dj.price + ai.price;
                        break;
                      }
                    }
                  }
                }
                console.log(
                  "判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用,有重复数组的拼接"
                );

                if (flag) {
                  _self.couponInfo.forEach((item) => {
                    //coupon_type  优惠券类型：1 现金券 2 兑换券 3 待定。。。
                    //现金券
                    if (item.coupon_type == 1) {
                      //applyGoods  可用商品：1 全部商品 2 指定商品
                      //全部商品
                      if (item.applyGoods == 1) {
                        //consume 最低消费: 0：无门槛 其他数值：满多少元可用
                        //全部商品 无门槛
                        if (item.consume == 0) {
                          item.isAvailable = true;
                        }
                        //全部商品 有门槛 满多少元可用
                        else {
                          // console.log(toBePaidMoney);//将要支付的现金金额
                          if (toBePaidMoney * 100 >= item.consume) {
                            item.isAvailable = true;
                          } else {
                            item.isAvailable = false;
                          }
                        }
                      }
                      //指定商品
                      else {
                        //consume 最低消费: 0：无门槛 其他数值：满多少元可用
                        //指定商品 无门槛

                        if (item.consume == 0) {
                          item.goodsInfo.forEach((item1) => {
                            toBePaidArr.forEach((item2) => {
                              if (item1.productid == item2.id) {
                                if (item1.type == 1 && item2.type == 2) {
                                  item.isAvailable = true;
                                } else if (item1.type == 2 && item2.type == 1) {
                                  item.isAvailable = true;
                                } else {
                                  item.isAvailable = false;
                                }
                                if (
                                  Number(item.faceValue) * 100 >=
                                  item2.price
                                ) {
                                  _self.assignPrice = item2.price;
                                } else {
                                  _self.assignPrice =
                                    Number(item.faceValue) * 100;
                                }
                              }
                            });
                          });
                        }
                        //指定商品 有门槛 满多少元可用
                        else {
                          if (toBePaidMoney * 100 >= item.consume) {
                            let itemPrice = 0; //使用这个券的服务和产品的总价格
                            item.goodsInfo.forEach((item1) => {
                              toBePaidArr.forEach((item2) => {
                                if (item1.productid == item2.id) {
                                  console.log(
                                    "指定产品，有门槛，指定产品门槛的价格"
                                  );

                                  if (item1.type == 1 && item2.type == 2) {
                                    itemPrice = itemPrice + item2.price;
                                  }
                                  if (item1.type == 2 && item2.type == 1) {
                                    itemPrice = itemPrice + item2.price;
                                  }
                                }
                              });
                            });

                            if (itemPrice >= item.consume) {
                              item.isAvailable = true;
                            } else {
                              item.isAvailable = false;
                            }
                            itemPrice = 0;
                          } else {
                            item.isAvailable = false;
                          }
                        }
                      }
                    }
                    //兑换券
                    else if (item.coupon_type == 2) {
                    }
                    //其他待定的
                    else {
                    }
                  });
                } else {
                  _self.couponInfo.forEach((item) => {
                    item.isAvailable = false;
                  });
                }
              }
              //商品数组长度为0的时候，现金券全部为不可用
              else {
                _self.couponInfo.forEach((item) => {
                  item.isAvailable = false;
                });
              }
            } else {
              _self.loading = false;
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (err) {},
        });
      } else {
        _self.couponInfo = [];
      }
    },

    chooseGift: function () {
      let _self = this;
      _self.showGiftData = {
        allPrice: 0,
        serverNum: 0,
        productNum: 0,
      };
      _self.isChooseGift = true;

      /*       if (_self.surplus > 0) {
        _self.isChooseGift = true;
      } else {
        _self.$message({
          type: "warning",
          message: "店铺赠送额度不足，无法赠送",
          duration: 1500,
        });
      } */

      // console.log("choosegift"+_self.isChooseGift)
    },

    showChooseGift: function () {
      // console.log("showChooseGift");
      this.isChooseGift = false;
    },

    chooseGiftData: function (data) {
      this.billGiftData = data;
      if (this.billGiftData.length != 0) {
        for (let i = 0; i < this.billGiftData.length; i++) {
          let item = this.billGiftData[i];
          if (item.itemType == 1) {
            this.showGiftData.serverNum =
              this.showGiftData.serverNum + Number(item.num);
          } else {
            this.showGiftData.productNum =
              this.showGiftData.productNum + Number(item.num);
          }
          this.showGiftData.allPrice =
            this.showGiftData.allPrice + Number(item.originPrice);
        }
      }
    },

    //办理会员卡页面
    handleVipCoupon: function () {
      let _self = this;
      _self.vipChooseCouponCard = true;
      // this.kd_is_xuanze_youhui = true;
      $.ajax({
        url: _self.url + "/android/Member/getCouponInfo",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          storeid: _self.loginInfo.storeid, // 商户id
          // "type": req.body.type,              // 1 未使用 2已使用 3已过期，在会员详情中必须传，会员开单选择时不传此字段
          limit: 20, // 分页 每页几条
          page: 1, // 分页 第几页
          coupon_num: "",
          uid: _self.memberObjData[0]?.id || 0, // 用户id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.couponInfo = res.data;
            if (_self.couponVipCardCode) {
              _self.scanVipCouponCard(_self.couponVipCardCode);
            }

            var carData = _self.cardTemp;
            let flag = false; //判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用
            let toBePaidMoney = Number(_self.paymentMoney); //将要支付的现金金额
            let toBePaidArr = []; //将要支付现金金额的服务id的数组
            // console.log(carData.card_name);

            // console.log(toBePaidMoney);
            //judgeCard  1：使用优惠权益 2：使用耗卡 0：默认 3:使用现金券
            if (carData.card_name) {
              _self.couponInfo.forEach((item) => {
                //coupon_type  优惠券类型：1 现金券 2 兑换券 3 待定。。。
                //现金券
                if (item.coupon_type == 1) {
                  //applyGoods  可用商品：1 全部商品 2 指定商品
                  //全部商品
                  if (item.applyGoods == 1) {
                    //consume 最低消费: 0：无门槛 其他数值：满多少元可用
                    //全部商品 无门槛
                    if (item.consume == 0) {
                      item.isAvailable = true;
                      // console.log(typeof toBePaidMoney);
                      // console.log(toBePaidArr);
                    }
                    //全部商品 有门槛 满多少元可用
                    else {
                      console.log(toBePaidMoney); //将要支付的现金金额
                      if (toBePaidMoney * 100 >= item.consume) {
                        item.isAvailable = true;
                      } else {
                        item.isAvailable = false;
                      }
                    }
                  }
                  //指定商品
                  else {
                    //consume 最低消费: 0：无门槛 其他数值：满多少元可用
                    //指定商品 无门槛
                    if (item.consume == 0) {
                      for (let i = 0; i < item.goodsInfo.length; i++) {
                        if (item.goodsInfo[i].type == 3) {
                          if (item.goodsInfo[i].productid == carData.id) {
                            item.isAvailable = true;
                            break;
                          } else {
                            item.isAvailable = false;
                          }
                        } else {
                          item.isAvailable = false;
                        }
                      }
                    }
                    //指定商品 有门槛 满多少元可用
                    else {
                      if (toBePaidMoney * 100 >= item.consume) {
                        for (let i = 0; i < item.goodsInfo.length; i++) {
                          if (item.goodsInfo[i].type == 3) {
                            if (item.goodsInfo[i].productid == carData.id) {
                              item.isAvailable = true;
                              break;
                            } else {
                              item.isAvailable = false;
                            }
                          } else {
                            item.isAvailable = false;
                          }
                        }
                      } else {
                        item.isAvailable = false;
                      }
                    }
                  }
                }
                //兑换券
                else if (item.coupon_type == 2) {
                }
                //其他待定的
                else {
                }
              });
            }
            //商品数组长度为0的时候，现金券全部为不可用
            else {
              _self.couponInfo.forEach((item) => {
                item.isAvailable = false;
              });
            }
          } else {
            _self.loading = false;
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (err) {},
      });
    },
    // 办理会员卡 处理选择现金券
    bindVipCouponCard: function (data, index, active) {
      let _self = this;

      var carData = _self.cardTemp;
      if (active) {
        if (data == 0 && index == 0) {
          carData.couponCard = {};
          carData.judgeCard = 0;
          _self.isVipCouponCard = false;
          _self.vipChooseCouponCard = false;
          _self.paymentMoney = _self.manualPrice;
          // _self.Pending()
        } else {
          carData.couponCard = data;
          carData.couponCard.coupon_name = data.coupon_name;
          carData.judgeCard = 3;
          _self.isVipCouponCard = true;
          _self.vipChooseCouponCard = false;
          let realPrice = Number(this.paymentMoney) - Number(data.faceValue);
          if (realPrice < 0) {
            this.paymentMoney = 0;
            carData.couponCard.counpon_money = Number(this.paymentMoney) * 100;
          } else {
            this.paymentMoney = realPrice.toFixed(2);
            carData.couponCard.counpon_money = Number(data.faceValue) * 100;
          }
        }
      } else {
        _self.$message({
          type: "warning",
          message: "选择的优惠券不可用",
          duration: 1500,
        });
      }
    },

    //开单扫码搜索现金券
    scanVipCouponCard: function (coupon_num) {
      let _self = this;
      _self.vipChooseCouponCard = true;
      // this.kd_is_xuanze_youhui = true;
      $.ajax({
        url: _self.url + "/android/Member/getCouponInfo",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          storeid: _self.loginInfo.storeid, // 商户id
          // "type": req.body.type,              // 1 未使用 2已使用 3已过期，在会员详情中必须传，会员开单选择时不传此字段
          limit: 20, // 分页 每页几条
          page: 1, // 分页 第几页
          coupon_num: coupon_num || "",
          uid: _self.memberObjData[0].id || 0, // 用户id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.couponInfo = res.data;

            var carData = _self.cardTemp;
            let flag = false; //判断是否有现金，有现金可以使用现金券，没有则现金券全部不可用
            let toBePaidMoney = Number(_self.paymentMoney); //将要支付的现金金额
            let toBePaidArr = []; //将要支付现金金额的服务id的数组
            // console.log(carData.card_name);

            // console.log(toBePaidMoney);
            //judgeCard  1：使用优惠权益 2：使用耗卡 0：默认 3:使用现金券
            if (carData.card_name) {
              _self.couponInfo.forEach((item) => {
                //coupon_type  优惠券类型：1 现金券 2 兑换券 3 待定。。。
                //现金券
                if (item.coupon_type == 1) {
                  //applyGoods  可用商品：1 全部商品 2 指定商品
                  //全部商品
                  if (item.applyGoods == 1) {
                    //consume 最低消费: 0：无门槛 其他数值：满多少元可用
                    //全部商品 无门槛
                    if (item.consume == 0) {
                      item.isAvailable = true;
                      // console.log(typeof toBePaidMoney);
                      // console.log(toBePaidArr);
                    }
                    //全部商品 有门槛 满多少元可用
                    else {
                      console.log(toBePaidMoney); //将要支付的现金金额
                      if (toBePaidMoney * 100 >= item.consume) {
                        item.isAvailable = true;
                      } else {
                        item.isAvailable = false;
                      }
                    }
                  }
                  //指定商品
                  else {
                    //consume 最低消费: 0：无门槛 其他数值：满多少元可用
                    //指定商品 无门槛
                    if (item.consume == 0) {
                      for (let i = 0; i < item.goodsInfo.length; i++) {
                        if (item.goodsInfo[i].type == 3) {
                          if (item.goodsInfo[i].productid == carData.id) {
                            item.isAvailable = true;
                            break;
                          } else {
                            item.isAvailable = false;
                          }
                        } else {
                          item.isAvailable = false;
                        }
                      }
                    }
                    //指定商品 有门槛 满多少元可用
                    else {
                      if (toBePaidMoney * 100 >= item.consume) {
                        for (let i = 0; i < item.goodsInfo.length; i++) {
                          if (item.goodsInfo[i].type == 3) {
                            if (item.goodsInfo[i].productid == carData.id) {
                              item.isAvailable = true;
                              break;
                            } else {
                              item.isAvailable = false;
                            }
                          } else {
                            item.isAvailable = false;
                          }
                        }
                      } else {
                        item.isAvailable = false;
                      }
                    }
                  }
                }
                //兑换券
                else if (item.coupon_type == 2) {
                }
                //其他待定的
                else {
                }
              });
            }
            //商品数组长度为0的时候，现金券全部为不可用
            else {
              _self.couponInfo.forEach((item) => {
                item.isAvailable = false;
              });
            }
          } else {
            _self.loading = false;
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (err) {},
      });
    },

    //办理会员卡 优惠券弹框取消和确定
    cancelVipCoupon: function () {
      this.vipChooseCouponCard = false;
    },

    /**
     *
     * 收银台核销
     *
     **/

    //核销查询
    getVerifyOrder: function () {
      let _self = this;
      if (_self.verifyCode) {
        $.ajax({
          url: _self.url + "/api/Order/getVerifyOrder",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            storeid: _self.loginInfo.storeid, // 商户id
            keyword: _self.verifyCode, //核销码
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              _self.verifyData = res.data;
              if (_self.verifyData.orderDetailsData.length != 0) {
                if (
                  _self.verifyData.orderDetailsData.length != 0 &&
                  _self.verifyData.orderDetailsData[0].technicians.length != 0
                ) {
                  let techniciansList =
                    _self.verifyData.orderDetailsData[0].technicians;
                  let technician_name = "";
                  techniciansList.forEach((item) => {
                    if (item.nickname) {
                      technician_name = technician_name + "";
                    }
                  });
                  verifyData.techniciansName = technician_name;
                }
                _self.isVerify = true;
                _self.helpStaffArr[_self.isactive1] = [];
              } else {
                _self.$message.warning({
                  message: "数据异常",
                  duration: 1500,
                });
              }
            } else {
              _self.$message.error({
                message: res.msg,
                duration: 1500,
              });
            }
          },
        });
      } else {
        _self.$message.warning({
          message: "请输入核销码或使用扫码枪扫描核销",
          duration: 1500,
        });
      }
    },

    //选择服务人员
    verifyChooseStaff: function () {
      // console.log('verifyChooseStaff')
      let _self = this;
      _self.verify_ji_shi = true;
      let technicianArr = _self.verifyData.orderDetailsData[0].technicians;
      $.ajax({
        url: _self.url + "/android/Staff/Craftsman",
        type: "post",
        data: {
          serviceid: _self.verifyData.orderDetailsData[0].goodsId,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            let arr = [];
            res.data.forEach((item) => {
              let flag = arr.some((i) => {
                if (i["id"] == item["id"]) {
                  return true;
                }
              });
              if (!flag) {
                arr.push(item);
              }
            });
            _self.ji_shis = arr;
            if (technicianArr.length != 0) {
              let obj = {};
              for (let i = 0; i < _self.ji_shis.length; i++) {
                for (let j = 0; j < technicianArr.length; j++) {
                  if (technicianArr[j].id == _self.ji_shis[i].id) {
                    if (technicianArr[j].dot == 1) {
                      _self.ji_shis[i]["is_choice_jishi"] = true;
                      _self.ji_shis[i]["is_guest"] = true;
                    } else {
                      _self.ji_shis[i]["is_choice_jishi"] = true;
                      _self.ji_shis[i]["is_guest"] = false;
                    }
                    obj[technicianArr[j].id] = _self.ji_shis[i];
                  }
                }
                if (_self.ji_shis[i].hasOwnProperty("is_choice_jishi")) {
                } else {
                  _self.ji_shis[i]["is_choice_jishi"] = false;
                  _self.ji_shis[i]["is_guest"] = false;
                }
              }
              _self.ji_shi_zhanshi = [];
              technicianArr.forEach((item) => {
                _self.ji_shi_zhanshi.push(obj[item.id]);
              });
            } else {
              for (let i = 0; i < _self.ji_shis.length; i++) {
                _self.ji_shis[i]["is_choice_jishi"] = false;
                _self.ji_shis[i]["is_guest"] = false;
              }
            }
            // console.log(_self.ji_shi_zhanshi);
            // _self.ji_shi_zhanshi=[];
            // console.log("服务人员信息服务人员信息")
            // console.log(_self.ji_shis);
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
      });
    },

    //首页--获取服务人员接口-开单
    change_ji_shi: function (index, isMerge = false) {
      this.loading = false;
      if (!isMerge) {
        this.ji_shi = true;
      }
      var _self = this;
      //在公共地区定义一个变量用来确定点击选择服务人员时候是给哪个服务添加的服务人员
      _self.which_server_technician = index;

      let technicianArr =
        _self.C_open_order_specifications_save[index].technician_id;
      // _self.C_open_order_specifications_save
      _self.ji_shi_zhanshi = [];
      $.ajax({
        url: _self.url + "/android/Staff/Craftsman",
        type: "post",
        data: {
          serviceid: _self.C_open_order_specifications_save[index].id,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            let arr = [];
            res.data.forEach((item) => {
              let flag = arr.some((i) => {
                if (i["id"] == item["id"]) {
                  return true;
                }
              });
              if (!flag) {
                arr.push(item);
              }
            });
            _self.ji_shis = arr;
            if (technicianArr && technicianArr.length != 0) {
              let obj = {};
              for (let i = 0; i < _self.ji_shis.length; i++) {
                for (let j = 0; j < technicianArr.length; j++) {
                  if (technicianArr[j].id == _self.ji_shis[i].id) {
                    if (technicianArr[j].dot == 1) {
                      _self.ji_shis[i]["is_choice_jishi"] = true;
                      _self.ji_shis[i]["is_guest"] = true;
                    } else {
                      _self.ji_shis[i]["is_choice_jishi"] = true;
                      _self.ji_shis[i]["is_guest"] = false;
                    }
                    obj[technicianArr[j].id] = _self.ji_shis[i];
                  }
                }
                if (_self.ji_shis[i].hasOwnProperty("is_choice_jishi")) {
                } else {
                  _self.ji_shis[i]["is_choice_jishi"] = false;
                  _self.ji_shis[i]["is_guest"] = false;
                }
              }
              technicianArr.forEach((item) => {
                _self.ji_shi_zhanshi.push(obj[item.id]);
              });
            } else {
              for (let i = 0; i < _self.ji_shis.length; i++) {
                _self.ji_shis[i]["is_choice_jishi"] = false;
                _self.ji_shis[i]["is_guest"] = false;
              }
            }

            // _self.ji_shi_zhanshi=[];
            // console.log("服务人员信息服务人员信息")
            // console.log(_self.ji_shis);
            _self.loading = false;
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
      });
    },

    //选择服务人员的点击事件  ji_shi_id用来判断从保存技术数组中删除服务人员的值
    //ji_shi_zhanshi数组用来存放在选择服务人员时候被选中的服务人员信息，按照点击的顺序来排序
    verify_chioce_ji_shi_name: function (index, data, ji_shi_id) {
      let _self = this;
      let item = _self.ji_shis[index];
      if (data) {
        // 选中
        _self.ji_shi_zhanshi.push(item);
      } else {
        // 取消
        _self.ji_shi_zhanshi.some((it, j) => {
          if (it["id"] == ji_shi_id) {
            _self.ji_shi_zhanshi.splice(j, 1);
            return true;
          }
        });
      }
      _self.$forceUpdate();
    },

    verify_chioce_all_ji_shi_name: function (index, data, ji_shi_id) {
      let _self = this;
      let item = _self.all_ji_shis[index];
      if (data) {
        // 选中
        _self.all_ji_shi_zhanshi.push(item);
      } else {
        // 取消
        _self.all_ji_shi_zhanshi.some((it, j) => {
          if (it["id"] == ji_shi_id) {
            _self.all_ji_shi_zhanshi.splice(j, 1);
            return true;
          }
        });
      }
      _self.$forceUpdate();
    },

    //选择点客的点击事件ji_shi_id用来判断
    verify_chioce_ji_shi_guest: function (index, data, ji_shi_id) {
      let _self = this;
      for (let i = 0; i < _self.ji_shis.length; i++) {
        if (index == i) {
          if (_self.ji_shis[i]["is_choice_jishi"] == false) {
            _self.ji_shis[i]["is_choice_jishi"] = data;
            _self.ji_shis[i]["is_guest"] = data;
            _self.ji_shi_zhanshi.push(_self.ji_shis[i]);
          } else {
            for (let j = 0; j < _self.ji_shi_zhanshi.length; j++) {
              if (_self.ji_shi_zhanshi[j]["id"] == ji_shi_id) {
                _self.ji_shi_zhanshi[j]["is_guest"] = data;
              }
            }
          }
        }
      }
      _self.ji_shi_zhanshi.some((it, j) => {
        if (it["id"] == ji_shi_id) {
          _self.ji_shi_zhanshi[j]["is_guest"] = data;
          return true;
        }
      });
      _self.$forceUpdate();
    },

    //确认服务人员
    //ji_shi_zhanshi_name是存入C_open_order_specifications_save的key预存内容，有展示的服务人员的name组合（分顺序）
    verify_open_save_technician: function () {
      let _self = this;
      _self.verify_ji_shi = false;
      let ji_shi_zhanshi_name = "";
      for (let i = 0; i < _self.ji_shi_zhanshi.length; i++) {
        if (i == _self.ji_shi_zhanshi.length - 1) {
          ji_shi_zhanshi_name += _self.ji_shi_zhanshi[i]["nickname"];
        } else {
          ji_shi_zhanshi_name += _self.ji_shi_zhanshi[i]["nickname"] + "、";
        }
      }
      _self.pi_is_ji_name = ji_shi_zhanshi_name;
      _self.verifyData["techniciansName"] = ji_shi_zhanshi_name;
      for (let i = 0; i < _self.ji_shi_zhanshi.length; i++) {
        _self.ji_shi_save[i] = {};
        if (_self.ji_shi_zhanshi[i]["is_guest"] == true) {
          _self.ji_shi_save[i]["id"] = _self.ji_shi_zhanshi[i]["id"];
          _self.ji_shi_save[i]["nickname"] =
            _self.ji_shi_zhanshi[i]["nickname"];
          _self.ji_shi_save[i]["dot"] = 1;
        } else {
          _self.ji_shi_save[i]["id"] = _self.ji_shi_zhanshi[i]["id"];
          _self.ji_shi_save[i]["nickname"] =
            _self.ji_shi_zhanshi[i]["nickname"];
          _self.ji_shi_save[i]["dot"] = 0;
        }
      }
      _self.verifyData.orderDetailsData[0]["technicians"] = _self.ji_shi_save;
      _self.ji_shi_zhanshi = [];
      _self.ji_shi_save = [];
    },

    //取消确认服务人员
    verify_open_over_technician: function () {
      let _self = this;
      _self.verify_ji_shi = false;
      _self.ji_shi_zhanshi = [];
      _self.ji_shi_save = [];
      for (let i = 0; i < _self.ji_shis.length; i++) {
        _self.ji_shis[i]["is_choice_jishi"] = false;
        _self.ji_shis[i]["is_guest"] = false;
      }
      _self.$forceUpdate();
    },

    //立即核销
    checkVerify: function () {
      this.$confirm("确认核销？")
        .then((_) => {
          let _self = this;
          let technicians = _self.verifyData.orderDetailsData[0].technicians;
          if (this.helpStaffArr[this.isactive1]) {
            this.helpStaffArr[this.isactive1].forEach((item) => {
              technicians.push({
                id: item["id"],
                nickname: item["nickname"],
                help: 1,
              });
            });
          }

          $.ajax({
            url: _self.url + "/android/order/confirmVerify",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid, // 商户id
              storeid: _self.loginInfo.storeid, // 商户id
              orderNo: _self.verifyData.order_number, //订单号
              cashierId: _self.loginInfo.id, //收银员id
              technicians: JSON.stringify(technicians), //服务人员 + 接待员工
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.helpStaffArr[_self.isactive1] = [];
                _self.$message.success({
                  message: res.msg,
                  duration: 1500,
                });
                _self.isVerify = false;
                _self.isVerifySuccess = true;
                _self.verifyDataOrderNo = _self.verifyData.order_number;
                _self.verifyCode = "";
                _self.verifyData = {};
                _self.getVerifyOrderDetails(res.data.id);
              } else {
                _self.$message.error({
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
          done();
        })
        .catch((_) => {});
    },

    getVerifyOrderDetails: function (orderId) {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/OrderDetails",
        type: "post",
        data: {
          id: orderId,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.verifyOrderDetails = res.data;
            if (res.data.orderInfo.length != 0) {
              _self.verifyDetailsData = res.data.orderInfo[0];
            }
            if (res.data.payment.length != 0) {
              _self.verifyPayment = res.data.payment[0];
            }
          } else {
            _self.$message.error({
              message: res.msg,
              duration: 1500,
            });
          }
        },
      });
    },

    //核销清空页面
    verify_over_open: function () {
      this.verifyCode = "";
      this.verifyData = {};
      this.isVerify = false;
      this.helpStaffArr[this.isactive1] = [];
      // console.log("核销页面，清空页面")
      this.$nextTick(
        function () {
          this.inputFocus(this.$refs.verifyCode);
        }.bind(this)
      );
    },

    // 获取订单内容
    fetchOrderDetail: function () {
      // _self.kd_xinxi_list.orderNo
      var _self = this;
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/order/getOrderDetail",
        type: "post",
        data: {
          merchantid: _self.userInfo.merchantid,
          orderNo: _self.orderNumber,
          storeid: _self.userInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            // _self.isPay = 2;
            _self.kd_xinxi_list = res.data;
            _self.orderId = res.data.id;
            _self.kd_xinxi_list_buyer = res.data.buyer;
            _self.kd_xinxi_list_cashierInfo = res.data.cashierInfo;
            _self.cash = res.data.toBePay;
            //订单号详情
            _self.orderDetails = res.data.orderDetails;
            _self.requisiteCard = res.data.requisiteCard;

            if (res.data.toBePay < 0 && res.data.net_receipts > 0) {
              _self.isPay = 3;
            }
            // _self.orderDetails.forEach(item=>{
            //     if(item.consumeCard==1){
            //         _self.cash=_self.cash+item.smallTotal;

            //     }
            // })
            // if(_self.kd_xinxi_list.member_counpon_money!=0 && _self.kd_xinxi_list.member_coupon!=0){
            //     _self.cash=_self.cash-_self.kd_xinxi_list.member_counpon_money;
            // }

            //billToPay 1：充卡
            // if(_self.$props.billToPay==1){
            //     if(_self.composeMoney<(_self.cash/100)){
            //         _self.discountComposeMoney=_self.cash-(_self.composeMoney*100);
            //         _self.cash=_self.composeMoney*100;
            //     }
            // }
            // if(_self.cash!=0)

            if (_self.requisiteCard.length != 0) {
              for (let i = 0; i < _self.requisiteCard.length; i++) {
                let item = _self.requisiteCard[i];
                if (item.equityType == 2) {
                  _self.cash = _self.cash - item.realPay;
                }
              }
            }

            //现金那个页面的input框中的金额，刨除使用耗卡的付的钱
            _self.cz_shou_qian = String(_self.cash / 100);
            //会员已有卡的全部信息
            _self.payCardInfo = res.data.payCardInfo;
            _self.requisiteCard = res.data.requisiteCard;
            if (_self.cz_qudan) {
              _self.$nextTick(
                function () {
                  _self.$refs.actualHarvest.focus();
                }.bind(this)
              );
            }
            //当请求道数据后，开始进行先直接扣去折扣卡的优惠的价格，在展示还需要支付的钱。
            // _self.deleteDiscountBalance();
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
          }
        },
        error: function (error) {},
        complete: () => {
          _self.loading = false;
        },
      });
    },

    //核销成功页面的关闭按钮
    verify_close: function () {
      this.isVerifySuccess = false;
    },

    //继续核销
    newVerify: function () {
      this.isVerifySuccess = false;
      this.isactive1 = 5;
    },

    // 获取小票样式
    getReceiptSet: function () {
      var _self = this;

      $.ajax({
        url: _self.url + "/android/order/getReceiptSet",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.printSet = res.data;
            _self.paperwidth = res.data.width;
          } else {
            let data =
              '{"id":1,"storeid":1,"merchantid":1,"name":"默认小票","status":1,"addtime":"2020-01-01 08:00","set":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}],"type":1,"width":180,"paperwidth":58,"setInfo":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}]}';
            data = JSON.parse(data);
            _self.printSet = data;
            _self.paperwidth = data.width;
          }
        },
      });
    },

    //核销打印小票
    verifyPrint: function () {
      if (!LODOPbol) {
        this.noPrint();
        return;
      }
      var vm = this;
      if (this.printSet.length == 0) {
        Preview1();
      } else {
        // vm.printorderinfo = res.info;
        var str = $(vm.$refs.printorderstr).html();
        Preview2(str);
      }
    },
    // 没有安装打印机
    noPrint: function () {
      let self = this;
      self.$message({
        type: "error",
        message: "打印机未准备好,无法打印",
        duration: 1500,
        onClose: function () {
          LODOPbol = false;
        },
      });
    },

    // 计算小计
    subtotalAmount() {
      var _self = this;
      var carData = _self.C_open_order_specifications_save;

      var index = _self.carIndex;
      var discount = 0;

      if (carData[index].manualDiscountCard.card_type == 1) {
        if (
          carData[index].manualDiscountCard.card_type == 3 ||
          carData[index].manualDiscountCard.card_type == 2 ||
          carData[index].manualDiscountCard.card_type == 1
        ) {
          discount = 0;
        }
      } else {
        discount = parseFloat(
          carData[index].manualDiscountCard["discount"] / 10
        ).toFixed(3);
      }

      Vue.set(carData[index], "discount", discount);
      if (
        carData[index].manualDiscount == 2 ||
        carData[index].manualDiscount == 3
      ) {
        if (carData[index].zhonglei == 3) {
          var subtotal = parseFloat(
            carData[index].price *
              carData[index].buyNum *
              carData[index].discount
          ).toFixed(2);
        } else {
          var subtotal = parseFloat(
            carData[index].price * carData[index].num * carData[index].discount
          ).toFixed(2);
        }
      }
      if (
        carData[index].manualDiscount == 4 ||
        carData[index].manualDiscount == 1
      ) {
        if (carData[index].zhonglei == 3) {
          var subtotal = parseFloat(
            carData[index].price * carData[index].buyNum
          ).toFixed(2);
        } else {
          var subtotal = parseFloat(
            carData[index].price * carData[index].num
          ).toFixed(2);
        }
      }

      //Vue.set(_self.C_open_order_specifications_save[index], "subtotal", subtotal);

      app.$set(
        _self.C_open_order_specifications_save[index],
        "subtotal",
        subtotal
      );
      this.Pending();
      this.$forceUpdate();
    },

    binsChangeSubtotal: function (val, index) {
      val = val.replace(/[^\d\.]/g, "");
      val = val.replace(/^\./g, "");
      val = val.replace(/\.{2,}/g, ".");
      val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      val = val.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
      val = val.replace(/^0.$/, "0.");
      app.$set(this.C_open_order_specifications_save[index], "subtotal", val);
      this.Pending();
    },
    handleUseMemberPrice(index) {
      if (
        this.memberInfo?.is_vip &&
        this.C_open_order_specifications_save[index].member_price > 0 &&
        this.C_open_order_specifications_save[index].member_price / 100 <
          this.C_open_order_specifications_save[index].price
      ) {
        const reduceAmount = (
          (this.C_open_order_specifications_save[index].price -
            this.C_open_order_specifications_save[index].member_price / 100) *
          this.C_open_order_specifications_save[index].num
        ).toFixed(2);
        this.C_open_order_specifications_save[index].reduceAmount =
          reduceAmount;
        this.handleReduceAmountChange(reduceAmount, index);
      }
    },
    handleReduceAmountChange(val, index) {
      val = val.replace(/[^\d\.]/g, "");
      val = val.replace(/^\./g, "");
      val = val.replace(/\.{2,}/g, ".");
      val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      val = val.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
      val = val.replace(/^0.$/, "0.");
      let reduceAmount = 0;
      if (val) {
        reduceAmount = val;
        // 1 无权益 2折扣 3抵扣 4手动改价
        this.C_open_order_specifications_save[index].manualDiscount = 4;
      } else {
        this.C_open_order_specifications_save[index].manualDiscount = 1;
      }
      let subtotal = (
        this.C_open_order_specifications_save[index].price *
          this.C_open_order_specifications_save[index].num -
        reduceAmount
      ).toFixed(2);
      if (subtotal < 0) {
        subtotal = 0;
        this.$set(
          this.C_open_order_specifications_save[index],
          "reduceAmount",
          (
            this.C_open_order_specifications_save[index].price *
            this.C_open_order_specifications_save[index].num
          ).toString()
        );
      } else {
        this.C_open_order_specifications_save[index].reduceAmount =
          reduceAmount;
      }
      this.C_open_order_specifications_save[index].subtotal = subtotal;
      this.Pending();
    },
    // 计算待支付总价
    Pending: function () {
      var _self = this;
      var buyArr = _self.C_open_order_specifications_save;
      var all = 0;
      for (var i = 0; i < buyArr.length; i++) {
        if (buyArr[i].subtotal) {
          all += parseFloat(buyArr[i].subtotal);
        }
      }

      if (buyArr.judgeCard == 3) {
        let price = 0;
        if (_self.assignGoods) {
          price = _self.assignPrice / 100;
        } else {
          price = Number(buyArr.couponCard.faceValue);
        }
        all = all - price;
        if (all < 0) {
          buyArr.couponCard.counpon_money = price + all;
          all = 0;
        } else {
          buyArr.couponCard.counpon_money = price;
        }
      }
      this.payTotal = all;
    },

    //获取会员可用充值卡数据
    getBalanceCard: function (num) {
      var _self = this;

      $.ajax({
        url: _self.url + "/android/vip/getBalanceCard",
        type: "post",
        data: {
          storeid: _self.loginInfo.storeid,
          merchantid: _self.loginInfo.merchantid,
          memberid: _self.memberInfo.id, //会员id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.is_dynamic = true;
            _self.balanceCard = res.data;
          }
          if (num) {
            _self.memberSearchSuccess++;
            if (_self.memberSearchSuccess >= 3) {
              _self.loading = false;
            }
          } else {
            _self.loading = false;
          }
        },
        error: function (error) {
          _self.loading = false;
        },
      });
    },
    // 办卡收款
    bindOpenCard: function () {
      if (JSON.stringify(this.cardTemp) == "{}") {
        this.$message.error({
          message: "选择卡项服务",
          duration: 1500,
        });
      } else if (!this.memberObj.phone) {
        this.$message.error({
          message: "输入手机号,登录会员",
          duration: 1500,
        });
        this.inputFocus(this.$refs.memberPhone);
      } else {
        this.orderSave("Card");
      }
    },

    //input 获得焦点
    inputFocus: function (dom) {
      this.$nextTick(
        function () {
          dom.focus();
        }.bind(this)
      );
    },

    // 办卡and开单 查询会员
    memberSearch: function (phone, id) {
      var _self = this;
      // switch (_self.isactive1) {
      //     case 0:
      //         keyword = _self.queryMember;
      //         break;
      //     case 1:
      //         keyword = _self.memberObj.phone;
      //         break
      // }
      var keyword = phone;
      let data = {
        merchantid: _self.loginInfo.merchantid,
        storeid: _self.loginInfo.storeid,
      };
      if (id) {
        data.uid = id;
      } else {
        data.keyword = keyword;
      }
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/vip/memberSearch",
        type: "post",
        data: data,
        success: function (res) {
          // var res = JSON.parse(res);
          //定义的变量赋值，给
          //console.log('会员信息', res);
          _self.memberInformation = res.data[0];
          if (res.code == 1 && res.data.length != "") {
            switch (_self.isactive1) {
              case 0:
                // 收银台开单

                _self.memberInfo = res.data[0];
                _self.memberSearchSuccess = 1;
                // _self.is_dynamic = true;
                _self.getBalanceCard(1);
                _self.getMemberStampCardInfo(1);
                _self.billingType = 2;
                break;
              case 1:
                // 收银台办卡
                _self.memberObjData = res.data;

                _self.isVipCardMember = true;
                _self.memberObj.name = res.data[0].member_name;
                _self.memberObj.phone = res.data[0].phone;
                _self.memberObj.id = res.data[0].id;
                _self.memberObj.pic = res.data[0].pic;
                break;
              case 2:
                // 收银台充值
                _self.is_sechargeMember = true;
                _self.cz_huiyuanxinxi = res.data[0];
                _self.getVipRechargeData(res.data[0].phone);

                break;
              case 3:
                _self.receiptMember = res.data[0];
                break;
            }
          } else {
            _self.$message.error({
              message: "未找到此会员信息",
              duration: 1500,
            });
          }
        },
        error: function (error) {},
        complete: () => {
          _self.loading = false;
        },
      });
    },

    /**
     *   扣卡
     *
     * */

    //选择次卡
    bind_add_card: function (data, index) {
      this.is_product_server = false;
      this.index_server_product = index;
      this.data_server_product = JSON.parse(JSON.stringify(data));

      let data1 = data;
      this.C_open_order_Specifications = this.memberSecondaryCard[index];
      this.C_open_order_Specifications["imgurl"] =
        this.memberSecondaryCard[index]["img"];
      this.cashier_open_order_service_choice[0] =
        this.memberSecondaryCard[index];

      if (data1.status == 1) {
        //上架and下架 serviceSpecificationStatus

        if (data1.issku == 1) {
          //规格
          this.C_open_order_Specifications = JSON.parse(JSON.stringify(data1));
          this.cashier_open_order_Specifications = true;
          let id = data1.id;
          let _self = this;
          _self.loading = true;
          $.ajax({
            url: _self.url + "/android/Service/serviceSku",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              serviceid: id,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.C_open_order_specifications_name = res.data["sku"];
                _self.C_open_order_specifications_attr = res.data["sku_attr"];
                for (
                  let i = 0;
                  i < _self.C_open_order_specifications_name.length;
                  i++
                ) {
                  for (
                    let j = 0;
                    j <
                    _self.C_open_order_specifications_name[i]["sku_val"].length;
                    j++
                  ) {
                    _self.C_open_order_specifications_name[i]["sku_val"][
                      j
                    ].is_show = false;
                  }
                }
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            complete: () => {
              _self.loading = false;
            },
          });
        } else {
          this.cashier_open_order_service_choice[0]["specifications_id"] = 0;
          let new0bj = this.cashier_open_order_service_choice[0];
          new0bj["subtotal"] =
            this.cashier_open_order_service_choice[0].buyNum *
            this.cashier_open_order_service_choice[0].price;
          this.C_open_order_specifications_save.unshift(
            JSON.parse(JSON.stringify(new0bj))
          );

          this.loading = false;
          this.Pending();
        }
      } else {
        this.$message({
          message: "该商品已经下架",
          center: true,
          duration: 1500,
        });
        this.loading = false;
      }
    },

    //选择扣卡   会员权益，扣卡
    bind_add_membercard: function (data, cardInfo) {
      let _self = this;

      data = this.deepCopy(data);
      if (typeof data.can_use !== "undefined" && data.can_use == 0) {
        return this.$message.warning("权益未到可用期限");
      }
      // 是否能使用会员价
      /* 会员价开始 */
      if (data.issku == 2) {
        // 非规格数据
        let memberPriceData = [];
        let levelId = 0;
        // data['price'] 单位是元
        let price = (data["price"] * 1).toFixed(2);
        if (
          _self.memberInfo &&
          _self.memberInfo.levelInfo &&
          _self.memberInfo.levelInfo.id
        ) {
          levelId = _self.memberInfo.levelInfo.id;
        }
        if (data["memberPriceData"]) {
          memberPriceData = data["memberPriceData"];
        }
        memberPriceData.some((item) => {
          if (item["sku_id"] == 0 && item["member_level_id"] == levelId) {
            price = parseFloat((item["price"] / 100).toFixed(2));
            return true;
          }
          return false;
        });
        data["realPrice"] = price;
        data["price"] = price;
      }
      /* 会员价结束 */
      cardInfo = this.deepCopy(cardInfo);
      // 先检查 余额是否充足，次数是否足够使用
      // 不能被点过去的情况
      // 1. 已下架   非次数权益
      if (data.status != 1 && data.equityType == 2) {
        return this.$message({
          message: "该商品已下架，请重新选择",
          center: true,
          duration: 1500,
        });
      }
      if (data.goods_type == 2 && data.equityType == 2) {
        // 2 产品 1 服务
        return this.$message({
          message: "产品扣卡，不支持折扣",
          center: true,
          duration: 1500,
        });
      }
      let balanceCardItem = {};
      //  data.equityType  权益类型 1 无权益 2折扣 3抵扣 4手动改价
      if (data.equityType == 2) {
        // 折扣权益  检查余额是否充足
        if (data.issku == 2) {
          if (cardInfo.cardSource < 0) {
            // 导入权益
          } else {
            let cardId = cardInfo["card_id"];
            let cardDetailsId = data["card_details_id"];
            let hasUsedMoney = 0;
            let needMoney = ((data.price * 100 * data.discount) / 10).toFixed(
              0
            );
            let balance = 0;
            this.balanceCard.some((item) => {
              if (item["id"] == cardId) {
                balance = item["residuebalance"];
                balanceCardItem = item;
                return true;
              }
            });
            this.balanceCardUse.forEach((item) => {
              // 耗卡id 等于当前id
              if (item["consumeCardId"] == cardId) {
                // console.log(item);
                hasUsedMoney += item["money"];
              }
            });
            if (!(balance - hasUsedMoney > needMoney)) {
              return this.$message({
                message: "充值卡余额不足",
                center: true,
                duration: 1500,
              });
            }
          }
        }
      } else if (data.equityType == 3) {
        // 抵扣权益  检查次数是否充足
        // 是否是赠送
        let cardId = cardInfo["card_id"];
        let cardDetailsId = data["card_details_id"];
        let hasUsedNum = 0;
        // 统计已使用次数
        this.timerCardUse.forEach((item) => {
          if (cardInfo["once_cardtype"] == 3 && !(data.givenum > 0)) {
            // 通卡，非赠送
            if (item["cardId"] == cardId && item["once_cardtype"] == 3) {
              hasUsedNum = hasUsedNum + item["num"];
            }
          } else {
            if (
              item["cardId"] == cardId &&
              item["cardDetailsId"] == cardDetailsId
            ) {
              hasUsedNum = hasUsedNum + item["num"];
            }
          }
        });
        if (data.givenum > 0 && hasUsedNum >= data.givenum) {
          // 是赠送
          return this.$message({
            message: "可用次数不足",
            center: true,
            duration: 1500,
          });
        } else {
          if (data.infinite == 1) {
            // 无限次数
          } else {
            if (hasUsedNum >= data.num) {
              return this.$message({
                message: "可用次数不足",
                center: true,
                duration: 1500,
              });
            }
          }
        }
      } else {
        return this.$message({
          message: "扣卡一定使用权益，数据错误",
          center: true,
          duration: 1500,
        });
      }
      _self.is_product_server = false;
      _self.data_server_product = data;
      data.membercardChangeIndex = this.membercardChangeIndex; //把当前折叠面板的index值存到数组中去
      let data1 = data;
      this.C_open_order_Specifications = data;
      this.C_open_order_Specifications["imgurl"] = data["img"];
      this.cashier_open_order_service_choice[0] = data;
      if (data1.issku == 1) {
        // 规格数据
        if (typeof data.goods_type !== "undefined" && data.goods_type == 2) {
          // 扣卡规格产品  bind_add_product
          let id = data1.id;
          _self.loading = true;
          // 产品 规格选择
          this.is_product_server = true;

          _self.chooseProjuctFlag = true;
          // 保存 扣卡数据 handleCloseSpecification
          _self.chooseProjuctService = {
            id: id,
            cardId: cardInfo.id,
            givenum: data.givenum,
            data: data,
            cardInfo: cardInfo,
          };
          this.C_open_order_Specifications = JSON.parse(JSON.stringify(data1));
          _self.cashier_open_order_Specifications = true;
          this.C_open_order_specifications_name = []; //将要循环的数值和对比的清空
          this.C_open_order_specifications_attr = [];
          $.ajax({
            url: _self.url + "/android/Product/getProductInfo",
            type: "post",
            data: {
              productId: id,
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.C_open_order_specifications_name =
                  res["data"]["skuinfo"]["skuattr"];
                _self.C_open_order_specifications_attr =
                  res["data"]["skuinfo"]["skulist"];
                for (
                  let i = 0;
                  i < _self.C_open_order_specifications_name.length;
                  i++
                ) {
                  for (
                    let j = 0;
                    j <
                    _self.C_open_order_specifications_name[i]["item"].length;
                    j++
                  ) {
                    _self.C_open_order_specifications_name[i]["item"][
                      j
                    ].is_show = false;
                  }
                }
              } else {
                // console.log(1);
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            complete: () => {
              _self.loading = false;
            },
          });
        } else {
          this.C_open_order_Specifications = JSON.parse(JSON.stringify(data1));
          this.cashier_open_order_Specifications = true;
          let id = data1.id;
          _self.loading = true;
          _self.chooseProjuctFlag = true;
          // 保存 扣卡数据
          _self.chooseProjuctService = {
            id: id,
            cardId: cardInfo.id,
            givenum: data.givenum,
            data: data,
            cardInfo: cardInfo,
          };
          // 获取规格数据
          $.ajax({
            url: _self.url + "/android/Service/serviceSku",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              serviceid: id,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                // console.log(res);
                _self.C_open_order_specifications_name = res.data["sku"];
                _self.C_open_order_specifications_attr = res.data["sku_attr"];
                for (
                  let i = 0;
                  i < _self.C_open_order_specifications_name.length;
                  i++
                ) {
                  for (
                    let j = 0;
                    j <
                    _self.C_open_order_specifications_name[i]["sku_val"].length;
                    j++
                  ) {
                    _self.C_open_order_specifications_name[i]["sku_val"][
                      j
                    ].is_show = false;
                  }
                }
                _self.serviceSpecificationStatus = data["status"];
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            complete: () => {
              _self.loading = false;
            },
          });
        }
      } else {
        // 添加进购物车
        //  data.equityType  权益类型 1 无权益 2折扣 3抵扣 4手动改价
        if (data.equityType == 2) {
          // 导入权益
          if (cardInfo.cardSource < 0) {
            item = {};
            item.discount = data.discount;
            item.cardName = cardInfo.card_name;
            item.membercard_id = cardInfo.card_id;
            item.cardDetailsId = data.card_details_id;
            item.cardSource = cardInfo.cardSource;
            let new0bj = data;
            // item.id = item.card_id;
            new0bj["specifications_id"] = 0;
            new0bj["manualDiscount"] = data.equityTypes;
            new0bj["manualDiscountCard"] = item;
            new0bj["zhonglei"] = 3;
            new0bj["discount"] = data.discount;
            new0bj["choosemembercardId"] = data.card_details_id;
            new0bj["chooseMemberCardCount"] = 0;
            new0bj["isActiveCostCard"] = 1;
            new0bj.judgeCard = 0;
            new0bj.costCard = {};
            new0bj.costCard.card_info = "不使用耗卡";
            new0bj["subtotal"] = data.buyNum * data.price;
            _self.C_open_order_specifications_save.unshift(
              this.deepCopy(new0bj)
            );
            _self.carIndex = 0;
            _self.subtotalAmount();
            _self.loading = false;
          } else {
            // 充值卡权益
            let item = balanceCardItem;
            item.discount = data.discount;
            item.cardName = item.card_info;
            item.membercard_id = item.id;
            item.cardDetailsId = data.card_details_id;
            let new0bj = data;
            // item.id = item.card_id;
            new0bj["specifications_id"] = 0;
            new0bj["manualDiscount"] = data.equityTypes;
            new0bj["manualDiscountCard"] = item;
            new0bj["zhonglei"] = 3;
            new0bj["discount"] = data.discount;
            new0bj["choosemembercardId"] = data.card_details_id;
            new0bj["chooseMemberCardCount"] = 0;
            new0bj["isActiveCostCard"] = 1;
            new0bj.judgeCard = 1;
            new0bj.costCard = {};
            new0bj.costCard.card_info = item.card_info;
            new0bj["subtotal"] = new0bj.buyNum * new0bj.price;
            _self.C_open_order_specifications_save.unshift(
              this.deepCopy(new0bj)
            );
            //设置carIndex
            _self.carIndex = 0;
            _self.subtotalAmount();
            _self.loading = false;
          }
        } else if (data.equityType == 3) {
          let item = {
            cardName: cardInfo["card_name"],
            cardSource: cardInfo["cardSource"],
            card_type: cardInfo["cardtype"],
            discount: data["discount"] ? data["discount"] : "1",
            id: data["card_details_id"],
            indate: cardInfo["indateName"],
            isgive: data["givenum"] > 0 ? 1 : 2,
            maxnum: data["num"],
            membercard_id: cardInfo["card_id"],
            once_cardtype: cardInfo["once_cardtype"],
          };
          let new0bj = data;
          new0bj["specifications_id"] = 0;
          new0bj["manualDiscount"] = 3;
          new0bj["manualDiscountCard"] = item;
          if (data.goods_type == 2) {
            // zhonglei 1 服务 2 产品 3 服务
            new0bj["zhonglei"] = 2;
            new0bj["is_open_product_jishi"] = true;
          } else {
            new0bj["zhonglei"] = 3;
          }
          new0bj["discount"] = item.discount;
          new0bj["choosemembercardId"] = data.card_details_id;
          new0bj["chooseMemberCardCount"] = 0;
          new0bj["isActiveCostCard"] = 1;
          new0bj.judgeCard = 1;
          new0bj.costCard = {};
          new0bj.costCard.card_info = "不使用耗卡";
          // console.log(new0bj);
          new0bj["num"] = 1;
          new0bj["buyNum"] = 1;
          new0bj["subtotal"] = new0bj.buyNum * new0bj.price;
          _self.C_open_order_specifications_save.unshift(this.deepCopy(new0bj));
          //设置carIndex
          _self.carIndex = 0;
          _self.subtotalAmount();
          _self.loading = false;
        }
      }
    },

    getMemberStampCardInfo: function (num) {
      var _self = this;
      _self.loading = true;
      const data = {
        storeid: _self.loginInfo.storeid,
        merchantid: _self.loginInfo.merchantid,
        memberid: _self.memberInfo.id, //会员id
      };

      $.ajax({
        url: _self.url + "/android/vip/getNewMemberStampCardInfo2",
        type: "post",
        data,
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.memberSecondaryCard = res.data;

            // 单独抽出所有权益中的卡
            _self.sampleMemberCardInfo = [];
            res.data.forEach((item) => {
              item.goodsInfo.forEach((goodItem) => {
                var d = {
                  ...goodItem,
                  technician_id: [],
                  salesmen: [],
                  buyNum: 1,
                  manualDiscount: 1,
                  manualDiscountCard: {},
                  zhonglei: 3,
                  cardInfo: {
                    allnum: item.cardInfo.allnum,
                    cardSource: item.cardInfo.cardSource,
                    card_id: item.cardInfo.card_id,
                    card_name: item.cardInfo.card_name,
                    cardtype: item.cardInfo.cardtype,
                    indate: item.cardInfo.indate,
                    indateName: item.cardInfo.indateName,
                    once_cardtype: item.cardInfo.once_cardtype,
                    planMinTotal: item.cardInfo.planMinTotal,
                  },
                };
                _self.sampleMemberCardInfo.push(d);
              });
            });
          } else {
            _self.memberSecondaryCard = [];
            _self.sampleMemberCardInfo = [];
          }
          if (num) {
            _self.memberSearchSuccess++;
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });

      $.ajax({
        url: _self.url + "/android/vip/getNewMemberStampCardInfo3",
        type: "post",
        data,
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            // 单独抽出所有权益中的卡
            _self.sampleMemberCardInfo_runOut = [];
            res.data.forEach((item) => {
              item.goodsInfo.forEach((goodItem) => {
                var d = {
                  ...goodItem,
                  technician_id: [],
                  salesmen: [],
                  buyNum: 1,
                  manualDiscount: 1,
                  manualDiscountCard: {},
                  zhonglei: 3,
                  cardInfo: {
                    allnum: item.cardInfo.allnum,
                    cardSource: item.cardInfo.cardSource,
                    card_id: item.cardInfo.card_id,
                    card_name: item.cardInfo.card_name,
                    cardtype: item.cardInfo.cardtype,
                    indate: item.cardInfo.indate,
                    indateName: item.cardInfo.indateName,
                    once_cardtype: item.cardInfo.once_cardtype,
                    planMinTotal: item.cardInfo.planMinTotal,
                  },
                };
                _self.sampleMemberCardInfo_runOut.push(d);
              });
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },
    // 选择会员可用权益
    bindUseOffer: function (data, index) {
      if (!this.memberInfo?.id) {
        return;
      }

      var _self = this;
      _self.carIndex = index;
      _self.isGoodsType = data.zhonglei == 2 ? 2 : 1;
      var is_repeat = 0;
      //判断该唯一的服务有没有选择过权益
      var purchaseGoods = _self.C_open_order_specifications_save;
      //judgeCard  1：使用优惠权益 2：使用耗卡 0：默认 3：使用现金券
      if (purchaseGoods[index].judgeCard == 2) {
        _self.$message({
          message: "选择不使用耗卡才能选择优惠权益",
          type: "warning",
        });
        return false;
      } else {
        if (purchaseGoods[index].status == 2) {
          _self.$message({
            message: "已下架的产品不能切换权益",
            type: "warning",
          });
          return false;
        }
        if (purchaseGoods.judgeCard == 3) {
          //判断是否使用了优惠券
          if (_self.C_open_order_specifications_save.judgeCard == 3) {
            _self.C_open_order_specifications_save.couponCard = {};
            _self.isCouponCard = false;
            _self.C_open_order_specifications_save.judgeCard = 0;
            _self.assignGoods = false;
          }
          _self.Pending();
        }
      }

      //记录选中的服务的服务id
      var chioced_id = purchaseGoods[index].id;
      _self.chioced_id = chioced_id;
      for (let i = 0; i < purchaseGoods.length; i++) {
        if (chioced_id == purchaseGoods[i].id) {
          is_repeat++;
        }
      }
      if (is_repeat == 1) {
        if (purchaseGoods[index].hasOwnProperty("give_preferential")) {
          // console.log('有');
          _self.bk_youhui_power = true;
          _self.offerCardOnce = JSON.parse(
            JSON.stringify(purchaseGoods[index]["give_preferential"]["once"])
          );
          _self.offerCarddis = JSON.parse(
            JSON.stringify(purchaseGoods[index]["give_preferential"]["dis"])
          );
          _self.availableEquity = JSON.parse(
            JSON.stringify(purchaseGoods[index]["give_preferential"])
          );
          is_have_give_preferential = true;
        } else {
          _self.loading = true;
          _self.bk_youhui_power = true;
          $.ajax({
            url: _self.url + "/android/vip/getMSEquityDetail",
            type: "post",
            data: {
              storeid: _self.loginInfo.storeid,
              merchantid: _self.loginInfo.merchantid,
              memberid: _self.memberInfo.id, //会员id
              serviceid: chioced_id,
              type: _self.isGoodsType,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              purchaseGoods[index]["give_preferential"] = res.data;
              _self.availableEquity = res.data;
              if (res.code == 1) {
                _self.offerCardOnce = res.data.once; //可用的优惠权益
                _self.offerCard_info = res.data.card_info;
                _self.offerCarddis = res.data.dis;
              } else {
                _self.bk_youhui_power = true;
                _self.offerCardOnce = []; //可用的优惠权益
                _self.offerCard_info = [];
                _self.offerCarddis = [];
              }
            },
            complete: () => {
              _self.loading = false;
            },
          });
        }

        // console.log('插入值后的服务数据',purchaseGoods[index]);
      } else {
        var is_have_give_preferential = false;
        for (let i = 0; i < purchaseGoods.length; i++) {
          if (
            chioced_id == purchaseGoods[i].id &&
            purchaseGoods[i].hasOwnProperty("give_preferential")
          ) {
            // console.log('有');
            _self.bk_youhui_power = true;

            _self.offerCardOnce = JSON.parse(
              JSON.stringify(purchaseGoods[i]["give_preferential"]["once"])
            );
            _self.offerCarddis = JSON.parse(
              JSON.stringify(purchaseGoods[i]["give_preferential"]["dis"])
            );
            _self.availableEquity = JSON.parse(
              JSON.stringify(purchaseGoods[i]["give_preferential"])
            );
            is_have_give_preferential = true;
            break;
          }
        }
        if (!is_have_give_preferential) {
          _self.loading = true;
          _self.bk_youhui_power = true;
          $.ajax({
            url: _self.url + "/android/vip/getMSEquityDetail",
            type: "post",
            data: {
              storeid: _self.loginInfo.storeid,
              merchantid: _self.loginInfo.merchantid,
              memberid: _self.memberInfo.id, //会员id
              serviceid: chioced_id,
              type: 1,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              purchaseGoods[index]["give_preferential"] = res.data;
              _self.availableEquity = res.data;
              if (res.code == 1) {
                _self.offerCardOnce = res.data.once; //可用的优惠权益
                _self.offerCard_info = res.data.card_info;
                _self.offerCarddis = res.data.dis;
              } else {
                _self.bk_youhui_power = true;
                _self.offerCardOnce = []; //可用的优惠权益
                _self.offerCard_info = [];
                _self.offerCarddis = [];
              }
            },
            complete: () => {
              _self.loading = false;
            },
          });
        }
      }
    },
    /**
     *
     *  充值
     *
     */

    // 点击充值卡获取会员信息
    cz_list_getPhoneInfo: function (item) {
      this.is_sechargeMember = true;
      this.is_recharge_card = true;
      this.cz_single_czk = item;

      // this.cz_chongzhi.benjin = parseFloat(item.capitalbalance / 100).toFixed(2);
      // this.cz_chongzhi.zengsong = parseFloat(item.presentbalance / 100).toFixed(2);

      this.cz_chongzhi.chongzhikaid = item.id;
      this.cz_chongzhi.recharge_name = item.card_info;
    },

    // 点击右侧删除充值金额的按钮
    cz_right_chongzhikaInfo_close: function () {
      this.cz_single_czk = {};
      this.cz_chongzhi = {};
      this.rechange_xiao_shou_zhanshi = [];
      this.rechangeSalesShow = "";
      this.is_recharge_card = false;
    },

    // 充值查询会员数据（充值卡列表，默认账户）
    getVipRechargeData: function (phone, fn) {
      var _self = this;
      // var keyword = '';
      // if (this.isactive1 == 1) {
      //     keyword = _self.memberObj.phone
      // } else if (this.isactive1 == 2) {
      //     keyword = _self.cz_search_keyword
      // }

      this.loading = true;
      $.ajax({
        url: _self.url + "/api/Vip/getVipRechargeData",
        type: "post",
        data: {
          keyword: phone,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.cz_chongzhika = res.data.cardInfo;
            // _self.cz_huiyuanxinxi = res.data.data;
            _self.cz_huiyuanxinxi = {
              ..._self.cz_huiyuanxinxi,
              ...res.data.data,
            };
            _self.is_sechargeMember = true;
            fn && fn();
          } else if (res.code == 0) {
            _self.$message({
              message: res.msg,
              type: "error",
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },

    // 充值列表
    getRecharge: function () {
      var _self = this;
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/card/getRecharge",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.cz_chongzhijine = res.data.RechargeData;
            _self.userDefined = res.data.userDefined;
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
    },

    // 选择充值金额
    cz_xuanze_chongzhi_money: function () {
      this.cz_ischongzhijine = true;
      this.getRecharge();
    },

    // 选择弹框中的充值金额
    cz_choice_chongzhijine: function (item) {
      // console.log(item);
      this.cz_ischongzhijine = false;
      this.cz_chongzhi.benjin = item.recharge_money;
      this.cz_chongzhi.zengsong = item.present_money;
      this.cz_chongzhi.chongzhikaid = item.id;
      this.cz_chongzhi.recharge_name = item.recharge_name;
      this.cz_customize = 0;
    },

    // 自定义充值
    bindCustomize: function (data) {
      this.cz_customize = data;
      this.cz_ischongzhijine = false;
      if (this.cz_chongzhi.chongzhikaid !== null) {
        this.cz_chongzhi.chongzhikaid = null;
        this.cz_chongzhi.benjin = "";
        this.cz_chongzhi.recharge_name = "";
        this.cz_chongzhi.zengsong = "";
      }
    },

    // 点击清空页面
    cz_over_open: function () {
      this.cz_chongzhika = [];
      this.is_sechargeMember = false;
      this.is_recharge_card = false;
      this.cz_huiyuanxinxi = {};
      this.cz_single_czk = {};
      this.cz_search_keyword = "";
      this.cz_chongzhi.benjin = null;
      this.cz_chongzhi.beizhu = "";
      this.cz_chongzhi.zengsong = null;
      this.cz_chongzhi.chongzhikaid = "";
      this.cz_chongzhi.recharge_name = "";
      this.rechange_xiao_shou_zhanshi = [];
      this.rechangeSalesShow = "";
      this.cz_customize = 0;
      this.billGiftData = [];
      this.showGiftData = {
        allPrice: 0,
        serverNum: 0,
        productNum: 0,
      };
      this.helpStaffArr[this.isactive1] = [];

      this.$nextTick(
        function () {
          this.inputFocus(this.$refs.cz_search_keyword);
        }.bind(this)
      );
    },

    // 点击收款
    cz_shoukuan: function () {
      if (!this.cz_search_keyword && !this.is_sechargeMember) {
        this.$message({
          message: "请输入手机号,查询充值卡",
          type: "warning",
          duration: 1500,
        });
        return;
      }
      if (JSON.stringify(this.cz_single_czk) == "{}") {
        this.$message({
          message: "请选择充值卡,进行充值",
          type: "warning",
          duration: 1500,
        });
        return;
      }
      if (!this.cz_chongzhi.benjin) {
        this.$message({
          message: "请输入充值金额或选择充值金额",
          type: "warning",
          duration: 1500,
        });
        return;
      }
      if (Number(this.cz_chongzhi.benjin) < Number(this.cz_chongzhi.zengsong)) {
        this.$message({
          message: "赠送金额不能大于充值金额",
          type: "warning",
          duration: 1500,
        });
        return;
      }
      // global.flag = 1;
      this.orderSave("Recharge");
    },

    /**
     *   收款
     *
     * */

    // 下单(办卡 and 充值)
    orderSave: function (payOrderType) {
      var _self = this;
      var buyerId = ""; // 用户id
      var orderType = 0; // 1：服务；2：产品；3：办卡；4：充值 5：充卡；6 直接收款
      var remark = ""; // 留言
      var sourceType = 1; // 来源类型 1,开单，2,预约，3,取单
      var totalPay = 0; // 订单总价（分）
      var itemName = ""; // 商品名称
      var arr = [];
      let extraData = [];
      //充值
      if (payOrderType == "Recharge") {
        // 充值
        buyerId = this.cz_huiyuanxinxi.id;
        orderType = 4;
        remark = this.cz_chongzhi.beizhu;
        totalPay = this.cz_chongzhi.benjin * 100;
        if (!this.cz_chongzhi.recharge_name) {
          itemName =
            "充" + this.cz_chongzhi.benjin + "送" + this.cz_chongzhi.zengsong;
        } else {
          itemName = this.cz_chongzhi.recharge_name;
        }
        var salesid = [];
        for (let i = 0; i < _self.rechange_xiao_shou_zhanshi.length; i++) {
          salesid.push(_self.rechange_xiao_shou_zhanshi[i]["id"]);
        }
        // 充值
        arr = [
          {
            cardDetailsId: 0, //	(开单使用)使用卡项详情的id
            cardId: this.cz_single_czk.id, //卡项id
            discount: "10", //折扣  （开单选择充值卡有）
            equityType: "1", // 1 无权益 2折扣 3抵扣 4手动改价
            goodsId: this.cz_chongzhi.chongzhikaid || 0, //商品id 服务，产品卡项都填写id
            itemId: "0",
            itemImgId: "0", //预览图id
            itemName: itemName, //商品名称
            itemType: "4", //1 服务 2产品 3卡项 4充值
            num: "1", //数量，除产品外，其他都填写1
            originPrice: this.cz_chongzhi.benjin * 100, // 充值金额  原价( 分 )
            recharge_money: this.cz_chongzhi.benjin * 100, //充值金额（本金）金额 （分） 手动充值时必传
            realPay: this.cz_chongzhi.benjin * 100, //充值金额真实支付（分）
            present_money: this.cz_chongzhi.zengsong * 100, //充值（赠送）金额 (分) 手动充值时必传
            salesmen: salesid || [], //选择的销售id
            skuId: "0", //规格id，非规格天写0
            skuName: "", //规格名称（如：红色,大）没有填空字符串
            stage: "1", //当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
            technicians: [], //服务人员id
          },
        ];
      } else if (payOrderType == "Card") {
        //办卡
        // console.log(this.card_server_name);
        var equityType = 1;
        buyerId = this.memberObjData[0].id;
        orderType = 3;
        remark = this.bk_beizhu_info;
        totalPay = parseInt(this.paymentMoney * 100);
        itemName = this.cardTemp.card_name;
        if (this.isBeneficial == 4) {
          equityType = 4;
        }
        var salesid = [];
        for (let i = 0; i < _self.bk_xiao_shou_zhanshi.length; i++) {
          salesid.push(_self.bk_xiao_shou_zhanshi[i]["id"]);
        }
        arr = [
          {
            cardDetailsId: 0, //	(开单使用)使用卡项详情的id
            cardId: this.cardTemp.id, //卡项id
            // 'consumeCard':this.cardTemp.costCard?this.cardTemp.costCard.consumeCard:0,
            // 'consumeCardId':this.cardTemp.costCard?this.cardTemp.costCard.consumeCardId:0,
            discount: "10", //折扣  （开单选择充值卡有）
            equityType: equityType, // 1 无权益 2折扣 3抵扣 4手动改价
            goodsId: this.cardTemp.id, //商品id 服务，产品卡项都填写id
            itemId: "0",
            itemImgId: "0", //预览图id
            itemName: this.cardTemp.card_name, //商品名称
            itemType: "3", //1 服务 2产品 3卡项 4充值
            num: "1", //数量，除产品外，其他都填写1
            originPrice: this.cardTemp.realPrice * 100, // 充值金额  原价( 分 )
            recharge_money: 0, //充值金额（本金）金额 （分） 手动充值时必传
            realPay: parseInt(this.manualPrice * 100), //充值金额真实支付（分）
            present_money: 0, //充值（赠送）金额 (分) 手动充值时必传
            salesmen: salesid || [], //选择的销售id
            skuId: "0", //规格id，非规格天写0
            skuName: "", //规格名称（如：红色,大）没有填空字符串
            stage: "1", //当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
            technicians: [], //服务人员id
          },
        ];
      }
      var obj = {
        addressInfo: "",
        bookerid: 0,
        buyerId: buyerId, // 用户id
        cashierId: this.loginInfo.id,
        dispatchFee: 0,
        dispatchType: 0,
        merchantid: this.loginInfo.merchantid,
        orderGiftItems: "",
        orderNo: "",
        orderType: orderType,
        promotions: "",
        remark: remark,
        sourceType: sourceType,
        storeid: this.loginInfo.storeid,
        totalPay: totalPay,
        orderItems: JSON.stringify(arr),
        shift_no: this.loginInfo.shift_no,
      };
      var _self = this;
      this.loading = true;
      let couponData = {};
      if (_self.cardTemp.judgeCard == 3) {
        couponData.id = _self.cardTemp.couponCard.id;
        couponData.counpon_money = _self.cardTemp.couponCard.counpon_money;
      }
      let presentData = [];
      if (this.billGiftData.length > 0) {
        presentData = JSON.parse(JSON.stringify(this.billGiftData));
        presentData.forEach((item) => {
          delete item.price;
        });
      }
      // 选择协助销售
      if (this.helpStaffArr[this.isactive1]) {
        extraData["help_staff"] = this.helpStaffArr[this.isactive1].map(
          (helpStaff) => {
            return helpStaff.id;
          }
        );
      }
      $.ajax({
        url: _self.url + "/android/order/orderSave",
        type: "post",
        data: {
          addressInfo: obj.addressInfo,
          bookerid: obj.bookerid,
          buyerId: obj.buyerId,
          cashierId: obj.cashierId,
          dispatchFee: obj.dispatchFee,
          dispatchType: obj.dispatchType,
          merchantid: obj.merchantid,
          orderGiftItems: obj.orderGiftItems,
          orderNo: obj.orderNo,
          orderType: obj.orderType,
          promotions: obj.promotions,
          remark: obj.remark,
          sourceType: obj.sourceType,
          storeid: obj.storeid,
          totalPay: obj.totalPay,
          orderItems: obj.orderItems,
          shift_no: obj.shift_no,
          couponData:
            _self.cardTemp.judgeCard == 3 ? JSON.stringify(couponData) : 0,
          presentData: JSON.stringify(presentData),
          extraData: JSON.stringify(extraData),
        },
        success: function (res) {
          // var res = JSON.parse(res);
          //console.log(res);
          if (res.code == 1) {
            _self.helpStaffArr[_self.isactive1] = [];
            _self.buy_receipt = true;
            _self.orderNo = res.data.orderNo;
            _self.orderId = res.data.id;
            _self.isRechargeCard = false;
            _self.couponVipCardCode = "";

            if (payOrderType == "Card") {
              if (_self.cardTemp.card_type == 1) {
                _self.isPayStatus = 0;
                // _self.isPayStatus = 4;
                _self.isRechargeCard = true;
              }
              _self.bk_over_open();
            } else {
              _self.cz_over_open();
              _self.rechangeSalesShow = "";
              _self.rechange_xiao_shou_zhanshi = [];
            }
          } else if (res.code == 0) {
            _self.$message({
              message: res.msg,
              type: "warning",
            });
          }
        },
        complete: () => {
          _self.deductionPrice = "";
          _self.input_dis = "";
          _self.loading = false;
        },
      });
    },

    // (清空页面数据)收款数据
    clearData: function () {
      this.kd_kaidanxinxi = []; // 收款开单信息
      this.kd_xinxi_list = {};
      this.kd_xinxi_list_buyer = {};
      this.kd_xinxi_list_cashierInfo = {}; //
      this.orderDetails = {};
      this.payCardInfo = [];
      this.paymentOffer = [];
      this.checkedOffer = [];
      this.cz_shou_qian = "";
      this.paymentCode = ""; //微信支付宝付款码
      this.vipPass = ""; //会员密码
      this.returnType = 3;

      // 充值

      // 办卡
    },
    // 充值页面  充值本金
    RechargeCardPrincipal: function () {
      this.cz_chongzhi.benjin = this.cz_chongzhi.benjin.replace(/[^\d\.]/g, "");
      this.cz_chongzhi.benjin = this.cz_chongzhi.benjin.replace(/^\./g, "");
      this.cz_chongzhi.benjin = this.cz_chongzhi.benjin.replace(/\.{2,}/g, ".");
      this.cz_chongzhi.benjin = this.cz_chongzhi.benjin
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", ".");
      this.cz_chongzhi.benjin = this.cz_chongzhi.benjin.replace(
        /^(\-)*(\d+)\.(\d\d).*$/,
        "$1$2.$3"
      );
      this.cz_chongzhi.benjin = this.cz_chongzhi.benjin.replace(/^0.$/, "0.");
      if (this.cz_chongzhi.benjin > 100000.0) {
        this.cz_chongzhi.benjin = "100000.00";
      }
    },
    //充值页面  充值赠送
    ComplimentaryCard: function () {
      this.cz_chongzhi.zengsong = this.cz_chongzhi.zengsong.replace(
        /[^\d\.]/g,
        ""
      );
      this.cz_chongzhi.zengsong = this.cz_chongzhi.zengsong.replace(/^\./g, "");
      this.cz_chongzhi.zengsong = this.cz_chongzhi.zengsong.replace(
        /\.{2,}/g,
        "."
      );
      this.cz_chongzhi.zengsong = this.cz_chongzhi.zengsong
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", ".");
      this.cz_chongzhi.zengsong = this.cz_chongzhi.zengsong.replace(
        /^(\-)*(\d+)\.(\d\d).*$/,
        "$1$2.$3"
      );
      this.cz_chongzhi.zengsong = this.cz_chongzhi.zengsong.replace(
        /^0.$/,
        "0."
      );
      if (this.cz_chongzhi.zengsong > 100000.0) {
        this.cz_chongzhi.zengsong = "100000.00";
      }
    },
    // 没有安装打印机
    noPrint: function () {
      let self = this;
      self.$message({
        type: "error",
        message: "打印机未准备好,无法打印",
        duration: 1500,
        onClose: function () {
          LODOPbol = false;
        },
      });
    },

    //核销修改员工业绩
    //获取所有销售和服务人员
    getAllSales: function () {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Deduct/getStaff",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.AllSales = res.data;
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
            _self.loading = false;
          }
        },
        error: function (error) {},
      });
    },

    //获取单据业绩提成记录
    modifyEmployeePerformance: function (orderNo) {
      var _self = this;
      this.isModifyPerformance = true;
      // console.log(_self.loginInfo.merchantid,_self.loginInfo.storeid,_self.kd_xinxi_list.order_number)
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/Deduct/getOrderDeductData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderNo: orderNo,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.zuhekaPerformance = res.data.type;
            _self.totalPerformance = res.data;
            _self.performanceList = res.data.performance;
            let length = _self.performanceList.length - 1;
            for (let i = 0; i < _self.performanceList.length; i++) {
              _self.performanceList[i].salesChecked = [];
              _self.performanceList[i].craftsChecked = [];
              if (_self.performanceList.base_amount) {
              } else {
                _self.performanceList[i].base_amount =
                  _self.performanceList[length].base_amount;
              }
            }
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });
      _self.getAllSales();
    },

    //选择销售
    chooseSales: function (performanceList, index) {
      var _self = this;
      this.isSales = true;
      this.isHandelIndex = index;
      _self.salesChecked =
        _self.performanceList[_self.isHandelIndex].salesChecked;
      for (let j = 0; j < _self.AllSales.length; j++) {
        Vue.delete(_self.AllSales[j], "isDisabled");
        for (
          let i = 0;
          i < _self.performanceList[_self.isHandelIndex].salesmen.length;
          i++
        ) {
          if (
            _self.performanceList[_self.isHandelIndex].salesmen[i].staff_id ==
            _self.AllSales[j].id
          ) {
            _self.AllSales[j].isDisabled = 1;
          }
        }
      }
    },
    //添加销售
    addSalesmen: function () {
      var _self = this;
      this.isSales = false;
      let salesmenLength =
        _self.performanceList[_self.isHandelIndex].salesmen.length;
      // console.log(_self.salesChecked,"下标")
      _self.performanceList[_self.isHandelIndex].addSalesmen = []; //存储添加的销售
      _self.performanceList[_self.isHandelIndex].salesChecked =
        _self.salesChecked;
      for (let i = 0; i < _self.salesChecked.length; i++) {
        salesmenLength += 1;
        _self.performanceList[_self.isHandelIndex].addSalesmen[i] = {
          staffName: _self.AllSales[_self.salesChecked[i]].nickname,
          lengthh: salesmenLength,
          assign: 2,
          base_amount: _self.performanceList[_self.isHandelIndex].base_amount,
          commission: 0.0,
          commission_proportion: 0.0,
          performance: 0.0,
          performance_proportion: 0.0,
          deduct_type: 1,
          deduct_way: 1,
          order_time: _self.totalPerformance.order_time,
          staff_id: _self.AllSales[_self.salesChecked[i]].id,
          storeid: _self.totalPerformance.storeid,
          merchantid: _self.totalPerformance.merchantid,
          id: 0,
          order_id: _self.performanceList[_self.isHandelIndex].order_id,
          order_detail_id: _self.performanceList[_self.isHandelIndex].id,
        };
      }
    },

    //选择服务人员
    chooseCrafts: function (performanceList, index) {
      var _self = this;
      this.isCrafts = true;
      this.isHandelIndex = index;
      _self.AllCrafts = _self.AllSales.filter(function (items, index, ar) {
        if (items.isTech == 2) {
          return items;
        }
      });
      _self.craftsChecked =
        _self.performanceList[_self.isHandelIndex].craftsChecked;
      for (let j = 0; j < _self.AllCrafts.length; j++) {
        Vue.delete(_self.AllCrafts[j], "isDisabled");
        for (
          let i = 0;
          i < _self.performanceList[_self.isHandelIndex].technicians.length;
          i++
        ) {
          if (
            _self.performanceList[_self.isHandelIndex].technicians[i]
              .staff_id == _self.AllCrafts[j].id
          ) {
            _self.AllCrafts[j].isDisabled = 1;
          }
        }
      }
    },
    //添加服务人员
    addCrafts: function () {
      var _self = this;
      this.isCrafts = false;
      let craftsLength =
        _self.performanceList[_self.isHandelIndex].technicians.length;
      _self.performanceList[_self.isHandelIndex].addCrafts = [];
      _self.performanceList[_self.isHandelIndex].craftsChecked =
        _self.craftsChecked;
      for (let i = 0; i < _self.craftsChecked.length; i++) {
        craftsLength += 1;
        _self.performanceList[_self.isHandelIndex].addCrafts[i] = {
          staffName: _self.AllCrafts[_self.craftsChecked[i]].nickname,
          lengthh: craftsLength,
          assign: 2,
          base_amount: _self.performanceList[_self.isHandelIndex].base_amount,
          commission: 0.0,
          commission_proportion: 0.0,
          performance: 0.0,
          performance_proportion: 0.0,
          deduct_type: 2,
          deduct_way: 1,
          order_time: _self.totalPerformance.order_time,
          staff_id: _self.AllCrafts[_self.craftsChecked[i]].id,
          storeid: _self.totalPerformance.storeid,
          merchantid: _self.totalPerformance.merchantid,
          id: 0,
          order_id: _self.performanceList[_self.isHandelIndex].order_id,
          order_detail_id: _self.performanceList[_self.isHandelIndex].id,
        };
      }
    },

    //选择提成方式
    chooseDeductType: function (e, sindex, lindex) {
      this.$forceUpdate();
    },

    limitInput: function (e) {
      // e.target.value = e.target.value.replace(/[^\d\.]/g, '')
      // e.target.value = e.target.value.replace(/^\./g, '0.');
      // e.target.value = e.target.value.replace(/\.{2,}/g, '.');
      // e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      // e.target.value = e.target.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
      // e.target.value = e.target.value.replace(/^0.$/, '0.');
    },
    //输入金额呈现百分比
    limitInputMoney: function (e) {
      // 当前操作值e.target._value
      //$(e.path[2]).find('input')[1].value  改变值
      //parseInt(e.path[7].children[0].childNodes[0].textContent)-1  每一项服务在服务数组中下标
      //parseInt(e.path[3].children[0].childNodes[0].textContent)-1 销售或服务人员数组中每一条数据的下标
      let performanceIndex =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      let lineIndex =
        parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
      let per = (
        (e.target.value * 10000) /
        this.performanceList[performanceIndex].base_amount
      ).toFixed(2);
      $(e.path[2]).find("input")[1].value = per;
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceIndex].salesmen[
          lineIndex
        ].performance_proportion = per.toString();
      } else {
        this.performanceList[performanceIndex].technicians[
          lineIndex
        ].performance_proportion = per.toString();
      }
    },
    limitInputMoneyAdd: function (e, items, index) {
      // e.target.value=items.commission
      let performanceIndex =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      $(e.path[2]).find("input")[1].value = (
        (parseInt(items.performance) * 10000) /
        items.base_amount
      ).toFixed(2);
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceIndex].addSalesmen[
          index
        ].performance_proportion = (
          (parseInt(items.performance) * 10000) /
          items.base_amount
        ).toFixed(2);
      } else {
        this.performanceList[performanceIndex].addCrafts[
          index
        ].performance_proportion = (
          (parseInt(items.performance) * 10000) /
          items.base_amount
        ).toFixed(2);
      }
      this.$forceUpdate();
    },
    limitInputMoneyAdd1: function (e, items, index) {
      let performanceIndex =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      $(e.path[2]).find("input")[1].value = (
        (parseInt(items.commission) * 10000) /
        items.base_amount
      ).toFixed(2);
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceIndex].addSalesmen[
          index
        ].commission_proportion = (
          (parseInt(items.commission) * 10000) /
          items.base_amount
        ).toFixed(2);
      } else {
        this.performanceList[performanceIndex].addCrafts[
          index
        ].commission_proportion = (
          (parseInt(items.commission) * 10000) /
          items.base_amount
        ).toFixed(2);
      }
      this.$forceUpdate();
    },
    limitInputMoney1: function (e) {
      let performanceIndex =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      let lineIndex =
        parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
      let per = (
        (e.target.value * 10000) /
        this.performanceList[performanceIndex].base_amount
      ).toFixed(2);
      $(e.path[2]).find("input")[1].value = per;
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceIndex].salesmen[
          lineIndex
        ].commission_proportion = per.toString();
      } else {
        this.performanceList[performanceIndex].technicians[
          lineIndex
        ].commission_proportion = per.toString();
      }
    },
    //输入百分比呈现金额
    limitInputPer: function (e) {
      //e.target._value 当前操作值
      //e.path[2]).find('input')[0].value 改变值
      let performanceMoney =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      let lineIndex =
        parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
      let money = (
        (e.target.value * this.performanceList[performanceMoney].base_amount) /
        10000
      ).toFixed(2);
      $(e.path[2]).find("input")[0].value = money;
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceMoney].salesmen[lineIndex].performance =
          money.toString();
      } else {
        this.performanceList[performanceMoney].technicians[
          lineIndex
        ].performance = money.toString();
      }
    },
    limitInputPer1: function (e) {
      let performanceMoney =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      let lineIndex =
        parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
      let money = (
        (e.target.value * this.performanceList[performanceMoney].base_amount) /
        10000
      ).toFixed(2);
      $(e.path[2]).find("input")[0].value = money;
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceMoney].salesmen[lineIndex].commission =
          money.toString();
      } else {
        this.performanceList[performanceMoney].technicians[
          lineIndex
        ].commission = money.toString();
      }
    },
    limitInputPerAdd: function (e, items, index) {
      let performanceMoney =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      $(e.path[2]).find("input")[0].value = (
        (parseInt(items.performance_proportion) * items.base_amount) /
        10000
      ).toFixed(2);
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceMoney].addSalesmen[index].performance =
          (
            (parseInt(items.performance_proportion) * items.base_amount) /
            10000
          ).toFixed(2);
      } else {
        this.performanceList[performanceMoney].addCrafts[index].performance = (
          (parseInt(items.performance_proportion) * items.base_amount) /
          10000
        ).toFixed(2);
      }
      this.$forceUpdate();
    },
    limitInputPerAdd1: function (e, items, index) {
      let performanceMoney =
        parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
      $(e.path[2]).find("input")[0].value = (
        (parseInt(items.commission_proportion) * items.base_amount) /
        10000
      ).toFixed(2);
      if (e.path[5].children[0].textContent == "选择销售") {
        this.performanceList[performanceMoney].addSalesmen[index].commission = (
          (parseInt(items.commission_proportion) * items.base_amount) /
          10000
        ).toFixed(2);
      } else {
        this.performanceList[performanceMoney].addCrafts[index].commission = (
          (parseInt(items.commission_proportion) * items.base_amount) /
          10000
        ).toFixed(2);
      }
      this.$forceUpdate();
    },

    //删除销售、服务人员
    delectsalesmen: function (info, index, inde) {
      var _self = this;
      _self.allDelect.push(_self.performanceList[inde].salesmen[index]);
      _self.performanceList[inde].salesmen.splice(index, 1);
      this.$forceUpdate();
    },
    delectCrafts: function (info, index, inde) {
      var _self = this;
      _self.allDelect.push(_self.performanceList[inde].technicians[index]);
      _self.performanceList[inde].technicians.splice(index, 1);
      this.$forceUpdate();
    },

    //提交保存修改的数据
    saveModify: function () {
      var _self = this;
      _self.loading = true;
      _self.saveModifyArr = [];
      _self.delArr = [];
      _self.addArr = [];
      //遍历拼接数组
      for (let i = 0; i < _self.performanceList.length; i++) {
        _self.saveModifyArr = _self.saveModifyArr.concat(
          _self.performanceList[i].salesmen,
          _self.performanceList[i].technicians
        );
        if (_self.performanceList[i].addSalesmen) {
          if (_self.performanceList[i].addCrafts) {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addSalesmen,
              _self.performanceList[i].addCrafts
            );
          } else {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addSalesmen
            );
          }
        } else {
          if (_self.performanceList[i].addCrafts) {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addCrafts
            );
          } else {
            _self.addArr = _self.addArr;
          }
        }
      }
      //删除多余的属性
      for (let i = 0; i < _self.addArr.length; i++) {
        Vue.delete(_self.addArr[i], "lengthh");
      }
      _self.delArr = _self.allDelect;
      _self.saveModifyArr = JSON.stringify(_self.saveModifyArr);
      _self.delArr = JSON.stringify(_self.delArr);
      _self.addArr = JSON.stringify(_self.addArr);
      $.ajax({
        url: _self.url + "/android/Deduct/saveDeductData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderNo: _self.verifyDataOrderNo,
          storeid: _self.loginInfo.storeid,
          addArr: _self.addArr,
          delArr: _self.delArr,
          saveArr: _self.saveModifyArr,
          nickname: _self.loginInfo.nickname,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              message: res.msg,
              type: "success",
              duration: 1500,
            });
            _self.isModifyPerformance = false;
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
          }
        },
        complete: () => {
          _self.loading = false;
        },
      });

      // console.log(_self.addArr)
    },
    /* handleDiscountMethodsClick: function (value) {
      this.discountMethods = value;
      this.Pending();
    },
    //优惠减扣匹配
    deductionMatch: function (input_price_show) {
      var _self = this;
      _self.deductionPrice = _self.deductionPrice.replace(/[^\d\.]/g, "");
      _self.deductionPrice = _self.deductionPrice.replace(/^\./g, "");
      _self.deductionPrice = _self.deductionPrice.replace(/\.{2,}/g, ".");
      _self.deductionPrice = _self.deductionPrice
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", ".");
      _self.deductionPrice = _self.deductionPrice.replace(
        /^(\-)*(\d+)\.(\d\d).*$/,
        "$1$2.$3"
      );
      _self.deductionPrice = _self.deductionPrice.replace(/^0.$/, "0.");
      _self.payTotal = input_price_show - _self.deductionPrice;
      if (_self.payTotal < 0) {
        _self.payTotal = 0;
      }
      _self.payTotal = _self.payTotal.toFixed(2);

    },
    //折扣匹配
    discountMatch: function (input_price_show) {
      var _self = this;
      var reg = /^100$|^(\d|[1-9]\d)$/g;
      if (_self.input_dis != "") {
        if (_self.input_dis != 0) {
          if (_self.input_dis.match(reg)) {

          } else {
            _self.input_dis = 0;
          }
        } else {
          _self.input_dis = 0;
        }
      }
      if (_self.input_dis != "") {
        _self.payTotal = (
          input_price_show *
          ((100 - _self.input_dis) / 10000) *
          100
        ).toFixed(2);
      } else {
        _self.payTotal = ((input_price_show / 100) * 100).toFixed(2);
      }
    }, */
  },
  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    subtotalFilter: function (val) {
      return val == "" ? 0 : val;
    },
  },
  watch: {
    vipSearch: function (n, o) {
      if (!n && o) {
        this.searchCurrentPage = 1;
        this.getMember();
      }
    },
    helpStaffArr: {
      immediate: true,
      deep: true,
      handler(n) {},
    },
    C_open_order_specifications_save: {
      immediate: true,
      deep: true,
      handler(save_orderData, o) {
        console.log(save_orderData, "C_open_order_specifications_save");
        // console.table(n);
        let orderItems = [];
        let timerCardUse = [];
        let balanceCardUse = [];
        save_orderData.forEach((item, i) => {
          if (item.zhonglei != 2) {
            isCostServer = true;
          }
          let cardDetailsId = 0;
          if (item.manualDiscountCard.cardSource != -1) {
            cardDetailsId =
              item.manualDiscountCard["cardDetailsId"] ||
              item.manualDiscountCard["id"] ||
              0;
          } else if (item.manualDiscount == 3) {
            cardDetailsId =
              item.manualDiscountCard["cardDetailsId"] ||
              item.manualDiscountCard["id"] ||
              0;
          }
          let cardId = 0;
          if (item.manualDiscountCard.cardSource != -1) {
            cardId = item.manualDiscountCard["membercard_id"] || 0;
          } else if (item.manualDiscount == 3) {
            cardId = item.manualDiscountCard["membercard_id"] || 0;
          }
          let consumeCardId = cardId;
          if (item.costCard && item.costCard["membercard_id"]) {
            consumeCardId = item.costCard["membercard_id"];
          }
          if (item.manualDiscount == 3) {
            // 次卡类型
            let onceCardtype = item.manualDiscountCard.once_cardtype
              ? item.manualDiscountCard.once_cardtype
              : 1;
            timerCardUse.push({
              carIndex: i,
              cardDetailsId: cardDetailsId,
              cardId: cardId,
              consumeCardId: consumeCardId,
              cardName: item.manualDiscountCard["cardName"] || "",
              once_cardtype: onceCardtype,
              // num: 1,
              num: item.num,
              money: 0,
              goodsId: item.id, // 商品id 服务，产品卡项都填写id
              itemId: item.itemId || 0, //取单时候传的itemId
              itemImgId: item.itemImgId || "0", // 预览图id
              itemName: item.service_name || item.product_name, // 商品名称
              itemType: item.zhonglei == 2 ? 2 : 1, // 1 服务 2产品 3卡项 4充值
              isgive: item.manualDiscountCard.isgive,
            });
          } else {
            // bindChooseCostCard
            if (item.isActiveCostCard == 2 || item.judgeCard == 1) {
              // 耗卡
              balanceCardUse.push({
                carIndex: i,
                cardDetailsId: cardDetailsId,
                cardId: cardId,
                consumeCardId: consumeCardId,
                cardName: item.manualDiscountCard["cardName"] || "",
                num: 0,
                money: Math.round(item.subtotal * 100), // 耗卡金额
                goodsId: item.id, // 商品id 服务，产品卡项都填写id
                itemId: item.itemId || 0, //取单时候传的itemId
                itemImgId: item.itemImgId || "0", // 预览图id
                itemName: item.service_name || item.product_name, // 商品名称
                itemType: item.zhonglei == 2 ? 2 : 1, // 1 服务 2产品 3卡项 4充值
              });
            } else if (item.judgeCard == 2 && consumeCardId === 0) {
              // 耗卡
              balanceCardUse.push({
                carIndex: i,
                cardDetailsId: cardDetailsId,
                cardId: cardId,
                consumeCardId: consumeCardId,
                cardName: item.manualDiscountCard["cardName"] || "",
                num: 0,
                money: Math.round(item.subtotal * 100), // 耗卡金额
                goodsId: item.id, // 商品id 服务，产品卡项都填写id
                itemId: item.itemId || 0, //取单时候传的itemId
                itemImgId: item.itemImgId || "0", // 预览图id
                itemName: item.service_name || item.product_name, // 商品名称
                itemType: item.zhonglei == 2 ? 2 : 1, // 1 服务 2产品 3卡项 4充值
              });
            }
          }
          /*orderItems.push({
					    cardName: item.manualDiscountCard['cardName'] || '',
					    cardDetailsId: cardDetailsId,
					    cardId: cardId,
					    discount: item.manualDiscountCard['discount'] || '10', // 折扣（开单选择充值卡有）
					    consumeCard: (item.costCard && item.costCard.manualDiscount == 2) ? 2 : 1, //是否耗卡（1,不是，2，是）
					    consumeCardId: (item.costCard && item.costCard['membercard_id']) ? item.costCard['membercard_id'] : 0, //耗卡的卡项id（consumeCard==2时有效）默认账户传0，其他他传卡项id
					    equityType: item.manualDiscount, // 1 无权益 2折扣 3抵扣 4手动改价
					    goodsId: item.id,       // 商品id 服务，产品卡项都填写id
					    itemId: item.itemId || 0, //取单时候传的itemId
					    itemImgId: item.itemImgId || '0',             // 预览图id
					    itemName: item.service_name || item.product_name,           // 商品名称
					    itemType: item.zhonglei == 2 ? 2 : 1,     // 1 服务 2产品 3卡项 4充值
					    num: item.zhonglei == 2 ? item.num : 1,  // 数量，除产品外，其他都填写1
					    originPrice: Math.round(item.price * 100),       // 充值金额  原价( 分 )
					    recharge_money: 0,                                // 充值金额（本金）金额 （分） 手动充值时必传
					    realPay: Math.round(item.subtotal * 100),           // 充值金额真实支付（分）
					    present_money: 0,                                 // 充值（赠送）金额 (分) 手动充值时必传
					    salesmen: item.salesmen || [],       // 选择的销售id
					    skuId: item.specifications_id || item.sku_val_id || 0,           // 规格id，非规格天写0
					    skuName: item.sku || '',               // 规格名称（如：红色,大）没有填空字符串
					})*/
        });
        //  console.log('次数使用情况', timerCardUse);   // 次数使用情况
        //  console.log('余额耗卡情况', balanceCardUse); // 余额耗卡情况
        if (save_orderData.judgeCard == 3 && o.length > 0) {
          // 只要点单发生变化 立即清除现金券
          this.C_open_order_specifications_save.couponCard = {};
          this.isCouponCard = false;
          this.C_open_order_specifications_save.judgeCard = 0;
          this.assignGoods = false;
          this.Pending();
        }
        this.timerCardUse = timerCardUse;
        this.balanceCardUse = balanceCardUse;
      },
    },
    searchcode: function (n, o) {
      this.searchcode = n.replace(/[^A-Za-z0-9-]/g, "");
      if (!n && o) {
        this.goods = this.menuSort[this.classgoodsarrindex].goods;
      }
    },

    // 服务搜索
    search_keyword: function (n, o) {
      if (!n && o) {
        this.serverPage = 1;
        this.serviceList();
      }
    },

    // 产品搜索
    search_product: function (n, o) {
      if (!n && o) {
        this.productPage = 1;
        this.productList();
      }
    },

    // 办卡--卡项搜索
    cardKeyword: function (n, o) {
      if (!n && o) {
        this.cardPage = 1;
        this.getCardDataByPage();
      }
    },

    // 监听cardTemp变化，实现动画效果
    cardTemp: {
      handler: function (newVal, oldVal) {
        // 当cardTemp.id变化时，触发动画效果
        if (
          newVal &&
          newVal.id &&
          oldVal &&
          oldVal.id &&
          newVal.id !== oldVal.id
        ) {
          this.refreshCardTemp();
        }
      },
      deep: true,
    },

    manualPrice: function () {
      this.manualPrice = this.manualPrice.replace(/[^\d\.]/g, "");
      this.manualPrice = this.manualPrice.replace(/^\./g, "");
      this.manualPrice = this.manualPrice.replace(/\.{2,}/g, ".");
      this.manualPrice = this.manualPrice
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", ".");
      this.manualPrice = this.manualPrice.replace(
        /^(\-)*(\d+)\.(\d\d).*$/,
        "$1$2.$3"
      );
      this.manualPrice = this.manualPrice.replace(/^0.$/, "0.");
      if (this.manualPrice > 100000.0) {
        this.manualPrice = "100000.00";
      }
      this.paymentMoney = this.manualPrice.replace(/[^\d\.]/g, "");
      this.paymentMoney = this.manualPrice.replace(/^\./g, "");
      this.paymentMoney = this.manualPrice.replace(/\.{2,}/g, ".");
      this.paymentMoney = this.manualPrice
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", ".");
      this.paymentMoney = this.manualPrice.replace(
        /^(\-)*(\d+)\.(\d\d).*$/,
        "$1$2.$3"
      );
      this.paymentMoney = this.manualPrice.replace(/^0.$/, "0.");
      if (this.paymentMoney > 100000.0) {
        this.paymentMoney = "100000.00";
      }
    },
    /* deductionPrice: {
      handler(n) {
        this.Pending();
      },
    },
    input_dis: {
      handler(n) {
        this.Pending();
      },
    }, */
  },
});
